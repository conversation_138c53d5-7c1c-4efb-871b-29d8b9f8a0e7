from uuid import UUID

from flask import current_app
from flask_login import current_user
from infrastructure_common.logging import get_logger
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.origin import Origin
from static_common.enums.permission import PermissionType
from structlog.stdlib import Bound<PERSON>ogger
import flask
import nh3

from copilot.logic.dao.submission_dao import SubmissionDAO
from copilot.logic.generated_notes import (
    create_text_note,
    generate_note_for_account_review_document,
    generate_note_for_pdf_export_download,
    generate_note_for_recommendation,
)
from copilot.logic.nationwide_boss import handle_submission_data_update
from copilot.logic.submission_notes import matched_submission_notes
from copilot.models import ReportPermission, User, db
from copilot.models.reports import (
    CreateOrReplaceNoteRequest,
    RecommendationSubmissionNoteRequest,
    ReportShadowDependency,
    SaveSubmissionNoteOptions,
    Submission,
    SubmissionNote,
    UserSubmissionNotification,
)
from copilot.models.types import SubmissionEvent
from copilot.schemas.report import (
    CreateOrReplaceNoteRequestSchema,
    RecommendationSubmissionNoteRequestSchema,
    SaveSubmissionNoteOptionsSchema,
    SubmissionNoteSchema,
)
from copilot.v3.controllers.permissions import add_permission

logger = get_logger()

note_schema = SubmissionNoteSchema()
save_submission_note_options_schema = SaveSubmissionNoteOptionsSchema()
recommendation_submission_note_request_schema = RecommendationSubmissionNoteRequestSchema()
create_or_replace_note_request_schema = CreateOrReplaceNoteRequestSchema()


def _notify_tagged_users(note: SubmissionNote, options: SaveSubmissionNoteOptions, log: BoundLogger) -> None:
    user_ids: set[int] = set(options.users_to_notify)
    if not user_ids:
        log.info("No users to notify")
        return

    log.info("Requested users to notify", user_ids=list(user_ids))
    submission: Submission | None = Submission.query.get(note.submission_id)
    if submission:
        tagged_users = (
            User.query.filter(User.id.in_(user_ids)).filter(User.organization_id == current_user.organization_id).all()
        )
        for user in tagged_users:
            log.info("Notifying user", user=user)
            current_app.knock_client.publish_user_tagged_in_note_notification(
                current_user, user, note, options, submission
            )


def _refer_users(note: SubmissionNote, options: SaveSubmissionNoteOptions, log: BoundLogger) -> None:
    if not options.users_to_refer or len(options.users_to_refer) == 0:
        log.info("No users to refer")
        return None

    report_details: tuple = (
        db.session.query(Submission.report_id)
        .filter(Submission.id == note.submission_id)
        .filter(Submission.report_id != None)
        .first_or_404()
    )
    report_id = report_details[0]

    requested_user_ids: set[int] = set(options.users_to_refer)
    log.info("Requested users to notify", users_ids=requested_user_ids)
    valid_user_ids: list[tuple] = (
        db.session.query(User.id)
        .filter(User.organization_id == current_user.organization_id)
        .filter(User.id.in_(requested_user_ids))
        .all()
    )
    if len(requested_user_ids) != len(valid_user_ids):
        logger.error("Invalid user in referral", requested_user_ids=requested_user_ids)
        flask.abort(404)

    for user_id in requested_user_ids:
        log.info("Referring user", users_id=user_id)
        report_permission: ReportPermission = ReportPermission(
            grantee_user_id=user_id,
            is_referral=True,
            report_id=report_id,
            message=nh3.clean(options.note_html) if options.note_html is not None else None,
            permission_type=PermissionType.EDITOR,
            referred_by_user_id=current_user.id,
        )
        add_permission(report_permission=report_permission, is_referred=True)


def _raise_if_xss_detected(note: SubmissionNote, note_html: str | None) -> None:
    banned_phrase = "javascript:"
    lower_note_text: str = note.text.lower() if note.text else ""
    lower_note_html: str = note_html.lower() if note_html else ""
    if banned_phrase in lower_note_text or banned_phrase in lower_note_html:
        logger.warning("Potential XSS detected in note", note=note)
        flask.abort(400)


# Permission ID
def close_submission_referral(id: str):
    permission: ReportPermission = ReportPermission.query.get(id)
    if not permission or not permission.is_referral:
        flask.abort(404)

    is_grantee = permission.grantee_user_id == current_user.id
    can_close = is_grantee or permission.referred_by_user_id == current_user.id
    if not can_close:
        flask.abort(403)

    permission.is_referral_resolved = True
    submission = SubmissionDAO.get_minimal_submission(report_id=permission.report_id, additional_fields=["name"])
    db.session.add(
        SubmissionNote(
            submission_id=submission.id,
            author_id=current_user.id,
            text="",
            referrals_closed_to_user_ids=[permission.grantee_user_id],
            is_generated_note=False,
        )
    )

    db.session.commit()

    if is_grantee and permission.referred_by_user_id:
        current_app.knock_client.publish_submission_referral_closed_notification(
            current_user, permission.referred_by_user, submission
        )


def add_submission_note(id: str, body: dict) -> tuple[dict, int]:
    submission = SubmissionDAO.get_minimal_submission(id)

    if not current_user.has_submission_permission(PermissionType.COMMENTER, id):
        flask.abort(403)

    note: SubmissionNote = note_schema.load(body.get("submission_note", {}))
    note.submission_id = UUID(id)
    note.author_id = current_user.id

    options_json = body.get("options", {})

    log = logger.bind(note=note, options=options_json)
    log.info("Adding submission note")

    _raise_if_xss_detected(note, options_json.get("note_html", None))

    options: SaveSubmissionNoteOptions = save_submission_note_options_schema.load(options_json)
    options.note_html = nh3.clean(options.note_html)
    note.referred_to_user_ids = options.users_to_refer

    db.session.add(note)

    UserSubmissionNotification.query.filter(
        UserSubmissionNotification.submission_id == note.submission_id,
        UserSubmissionNotification.user_id == current_user.id,
    ).update({UserSubmissionNotification.seen_notes: UserSubmissionNotification.seen_notes + 1})

    db.session.commit()

    _notify_tagged_users(note, options, log)
    _refer_users(note, options, log)

    if (
        submission.organization_id == ExistingOrganizations.Nationwide.value
        and submission.origin == Origin.API
        and submission.is_verified
    ):
        handle_submission_data_update(submission)
        current_app.event_service.handle_submission_event(SubmissionEvent.SUBMISSION_CLIENT_DATA_UPDATED, submission)

    return note_schema.dump(note), 201


def add_recommendation_submission_notes(body: list[dict]) -> int:
    if not current_user.is_internal_machine_user:
        flask.abort(403)

    note_requests: list[RecommendationSubmissionNoteRequest] = recommendation_submission_note_request_schema.load(
        body, many=True
    )

    submission_ids: list[UUID] = [note.submission_id for note in note_requests]
    rule_ids: list[UUID] = [note.rule_id for note in note_requests]

    existing_notes = (
        db.session.query(SubmissionNote)
        .filter(SubmissionNote.rule_id.in_(rule_ids), SubmissionNote.submission_id.in_(submission_ids))
        .all()
    )

    existing_notes_map = {(note.rule_id, note.submission_id): note for note in existing_notes}

    notes_to_add = []
    notes_to_delete = {key for key in existing_notes_map.keys()}

    for note_request in note_requests:
        key = (note_request.rule_id, note_request.submission_id)
        existing_note = existing_notes_map.get(key)

        if existing_note:
            if existing_note.last_edit_by_id:
                notes_to_delete.remove((existing_note.rule_id, existing_note.submission_id))
                continue
            hashed_text = hash(note_request.text)
            if existing_note.hashed_text == hashed_text and not note_request.is_delete_request:
                notes_to_delete.remove((existing_note.rule_id, existing_note.submission_id))
                continue

        if note_request.is_delete_request:
            continue

        new_note = generate_note_for_recommendation(note_request)
        _raise_if_xss_detected(new_note, note_request.text)
        notes_to_add.append(new_note)

    if notes_to_delete:
        for note in notes_to_delete:
            db.session.delete(existing_notes_map[note])

    if notes_to_add:
        db.session.add_all(notes_to_add)
    if notes_to_delete or notes_to_add:
        db.session.commit()

    return 201


def remove_submission_note(id: str) -> tuple[None, int]:
    note: SubmissionNote | None = SubmissionNote.query.get(id)

    if not note:
        flask.abort(404)

    submission = SubmissionDAO.get_minimal_submission(note.submission_id)

    if note.is_generated_note:
        flask.abort(403, "Generated notes cannot be deleted")

    if note.author_id != current_user.id and not current_user.is_organization_manager(note.author.organization_id):
        flask.abort(403)

    db.session.delete(note)

    UserSubmissionNotification.query.filter(UserSubmissionNotification.submission_id == note.submission_id).update(
        {UserSubmissionNotification.seen_notes: UserSubmissionNotification.seen_notes - 1}
    )

    if (
        submission.organization_id == ExistingOrganizations.Nationwide.value
        and submission.origin == Origin.API
        and submission.is_verified
    ):
        handle_submission_data_update(submission)
        current_app.event_service.handle_submission_event(SubmissionEvent.SUBMISSION_CLIENT_DATA_UPDATED, submission)

    db.session.commit()

    return None, 204


def update_submission_note(id: str, body: dict) -> tuple[dict, int]:
    existing_note: SubmissionNote = SubmissionNote.query.get(id)

    if not existing_note:
        submission_id = body.get("submission_note", {}).get("submission_id")
        if current_user.is_internal_machine_user or not submission_id:
            flask.abort(404)
        else:
            return add_submission_note(submission_id, body)

    submission = SubmissionDAO.get_minimal_submission(existing_note.submission_id, return_deleted_submission=True)
    if not existing_note.author_id and not existing_note.is_editable:
        flask.abort(403)
    if (
        not existing_note.is_note
        and existing_note.author_id
        and existing_note.author_id != current_user.id
        and not current_user.is_organization_manager(existing_note.author.organization_id)
    ):
        flask.abort(403)

    if submission.is_deleted:
        shadow_dependency = ReportShadowDependency.query.filter_by(report_id=submission.report_id).one_or_none()
        if not shadow_dependency or (not existing_note.original_note_id and not existing_note.hashed_text):
            flask.abort(404)

        notes = shadow_dependency.shadow_report.submission.submission_notes
        filtered_notes = (
            (note for note in notes if note.id == existing_note.original_note_id)
            if existing_note.original_note_id
            else (note for note in notes if note.hashed_text == existing_note.hashed_text)
        )
        existing_note = next(filtered_notes, None)
        if not existing_note:
            flask.abort(404)

    new_note: SubmissionNote = note_schema.load(body.get("submission_note", {}), instance=existing_note)
    new_note.last_edit_by_id = current_user.id

    options_json = body.get("options", {})
    _raise_if_xss_detected(new_note, options_json.get("note_html", None))

    log = logger.bind(existing_note=existing_note, new_note=new_note, options=options_json)
    log.info("Updating submission note")

    options: SaveSubmissionNoteOptions = save_submission_note_options_schema.load(options_json)
    options.note_html = nh3.clean(options.note_html) if options.note_html is not None else None

    db.session.commit()

    _notify_tagged_users(new_note, options, log)

    if (
        submission.organization_id == ExistingOrganizations.Nationwide.value
        and submission.origin == Origin.API
        and submission.is_verified
    ):
        handle_submission_data_update(submission)
        current_app.event_service.handle_submission_event(SubmissionEvent.SUBMISSION_CLIENT_DATA_UPDATED, submission)

    return note_schema.dump(new_note), 200


def get_submission_notes(id: str) -> dict:
    if not current_user.has_submission_permission(PermissionType.VIEWER, id):
        flask.abort(403)

    notes = SubmissionNote.query.filter_by(submission_id=id).order_by(SubmissionNote.created_at).all()

    return note_schema.dump(notes, many=True)


def add_account_review_document_ready_note(id: str, body: dict):
    if not body or "url" not in body:
        flask.abort(400)

    if not current_user.is_internal_machine_user:
        flask.abort(403)

    submission = SubmissionDAO.get_minimal_submission_or_404(id)

    url = body["url"]
    note = generate_note_for_account_review_document(submission, url)

    db.session.add(note)
    db.session.commit()

    return note_schema.dump(note), 201


def add_pdf_export_ready_note(body: dict):
    if not current_user.is_internal_machine_user:
        flask.abort(403)

    if not body or "url" not in body or "submission_id" not in body:
        flask.abort(400)

    submission = SubmissionDAO.get_minimal_submission_or_404(body["submission_id"])

    note = generate_note_for_pdf_export_download(submission, body["url"])

    db.session.add(note)
    db.session.commit()

    return None, 204


def create_or_replace_note(report_id: str, note_id: str, body: dict):
    log = logger.bind(report_id=report_id, note_id=note_id)

    if not current_user.has_report_permission(PermissionType.COMMENTER, report_id):
        log.info("Current user doesn't have permission to edit report")
        flask.abort(403)

    request: CreateOrReplaceNoteRequest = create_or_replace_note_request_schema.load(body)
    submission = SubmissionDAO.get_minimal_submission(report_id=report_id, raise_404=False)

    if not submission:
        log.info("Submission for specified report couldn't be found")
        flask.abort(404, "Submission for specified report couldn't be found")

    note: SubmissionNote = (
        db.session.query(SubmissionNote)
        .filter(SubmissionNote.submission_id == submission.id)
        .filter(SubmissionNote.canonical_id == note_id)
        .one_or_none()
    )

    was_existing_before = bool(note)

    log.info("Fetched existing note", note=note, was_existing_before=was_existing_before)

    if not note:
        log.info("Note didn't exist - creating new one", note=note)

        note = SubmissionNote()
        note.author_id = current_user.id
        note.submission_id = submission.id
        note.canonical_id = note_id
    else:
        log.info("Note existed - updating existing one", note=note)

        note.last_edit_by_id = current_user.id

    note.text = create_text_note(request.notes)

    db.session.add(note)
    db.session.commit()

    log.info("Completed note creation or update", note=note)

    return {"id": note_id}, 200 if was_existing_before else 201


def add_submission_note_external(report_id: str, body: dict):
    log = logger.bind(report_id=report_id)

    if not current_user.has_report_permission(PermissionType.COMMENTER, report_id):
        log.info("Current user doesn't have permission to edit report")
        flask.abort(403)

    request: CreateOrReplaceNoteRequest = create_or_replace_note_request_schema.load(body)

    if not request.notes:
        log.info("No note text provided")
        flask.abort(400, "No note text provided")

    submission = SubmissionDAO.get_minimal_submission(report_id=report_id)

    notes = matched_submission_notes(submission.id, request)

    log.info("Notes added", number_of_notes=len(notes))

    status = 200 if request.smart_matching else 201

    return {"notes": [{"id": str(note.id), "text": note.text_content} for note in notes]}, status
