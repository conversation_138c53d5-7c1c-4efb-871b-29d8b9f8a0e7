from collections import defaultdict
from http import H<PERSON><PERSON>tatus
import os

from flask import current_app
from flask_login import current_user
from infrastructure_common.logging import get_logger
from redis_lock import AlreadyAcquired
from sqlalchemy import func, or_
from sqlalchemy.exc import IntegrityError
from static_common.enums.caller_context import CallerContext
from static_common.enums.submission_sync import SyncMatchingRunStatus
import flask
import psycopg2
import redis_lock

from copilot.constants import INTERNAL_CALLER_CONTEXT_KEY
from copilot.kalepa_domain_events.kalepa_events_handler import KalepaEventsHandler
from copilot.logic.dao.submission_sync_dao import SubmissionSyncRequestDAO
from copilot.logic.synchronization.configuration import get_sync_config_map
from copilot.logic.synchronization.glue import save_sync_results_to_glue
from copilot.logic.synchronization.submission_lookup import SubmissionLookup
from copilot.logic.synchronization.submissions import SubmissionSyncProcessor
from copilot.models import db
from copilot.models.submission_sync import (
    DryRunSubmissionMatch,
    GetSyncRequestsForClientIdsRequest,
    MatcherDryRunResult,
    SubmissionSync,
    SyncMatchingResult,
    SyncMatchingRun,
    SyncMatchingRunRequest,
)
from copilot.models.synchronization.submissions import (
    DeleteSyncRequests,
    MatcherDryRunRequest,
    RunSyncRequest,
    SubmissionSyncProcessorConfiguration,
)
from copilot.schemas.submission_sync import (
    DeleteSyncRequestsSchema,
    MatcherDryRunResultSchema,
    RunSyncRequestSchema,
    SubmissionSyncSchema,
    SyncMatchingRunRequestSchema,
)
from copilot.schemas.synchronization.submissions import SynchronizationResultSchema
from copilot.utils import set_context_in_g
from copilot.v3.utils.db_session import no_expire_on_commit

IS_TEST = os.environ.get("TESTING_SCOPE", "false").lower() == "true"

logger = get_logger()

synchronization_result_schema = SynchronizationResultSchema()
run_sync_request_schema = RunSyncRequestSchema()
delete_sync_requests_schema = DeleteSyncRequestsSchema()
submissions_sync_schema = SubmissionSyncSchema(exclude=("applied_changes", "pending_changes"))
loading_submissions_sync_schema = SubmissionSyncSchema(
    used_for_loading=True, exclude=("applied_changes", "pending_changes")
)
dry_run_response_schema = MatcherDryRunResultSchema()
sync_matching_run_req_schema = SyncMatchingRunRequestSchema()


def _ensure_run_id_provided(run_sync_request: RunSyncRequest):
    if not run_sync_request.run_id:
        logger.error("Parameter run_id is required for this endpoint", run_sync_request=run_sync_request)
        flask.abort(400, "Parameter run_id is required for this endpoint")


def _update_replaced_and_replacing_client_ids(submission_sync: SubmissionSync):
    if submission_sync.replaced_by_client_id:
        replacement = submission_sync.replaced_by_submission_sync
        if replacement:
            replacement.replaces_client_id = submission_sync.policy_number
            replacement.applied = False
            db.session.add(replacement)
            db.session.commit()
    if submission_sync.replaces_client_id:
        replaced = submission_sync.replaces_submission_sync
        if replaced:
            replaced.replaced_by_client_id = submission_sync.policy_number
            db.session.add(replaced)
            db.session.commit()


def _shift_to_last_position(request: SubmissionSync) -> SubmissionSync:
    shifted = True
    while shifted:
        shifted = request.shift(force=True)
    db.session.add(request)
    db.session.commit()
    return request


def _update_grouped_sync_requests(to_sync: list[SubmissionSync]):
    org_to_client_ids = defaultdict(list)
    for req in to_sync:
        org_to_client_ids[req.organization_id].append(req.policy_number)

    for org_id, client_ids in org_to_client_ids.items():
        grouped_requests: list[SubmissionSync] = SubmissionSync.query.filter(
            SubmissionSync.policy_number.in_(client_ids),
            SubmissionSync.organization_id == org_id,
            SubmissionSync.is_deleted.is_(False),
        ).all()
        for req in grouped_requests:
            req.applied = False
            req.attempt = max(req.attempt - 1, 0)
            db.session.add(req)
    db.session.commit()


def load_sync_requests(body: list[dict], update_existing_only: bool = False) -> dict:
    to_sync = []
    if not current_user.is_internal_machine_user:
        flask.abort(403)

    with no_expire_on_commit():
        requests: list[SubmissionSync] = loading_submissions_sync_schema.load(body, many=True)
        req_ids = {req.id for req in requests}

        saved: list[SubmissionSync] = SubmissionSync.query.filter(SubmissionSync.id.in_(req_ids)).all()
        id_to_pending_len = {s.id: len(s.pending_changes) for s in saved}  # type: ignore

        id_req = {req.id: req for req in saved}

        for req in requests:
            if update_existing_only and req.id not in id_req:
                continue

            if req.id not in id_req:
                id_req[req.id] = req
                req.is_new = True
                to_sync.append(req)
                continue

            to_update: SubmissionSync = id_req[req.id]
            # if we are receiving an update request for a deleted submission_sync we need to un-delete it
            if to_update.is_deleted:
                to_update.is_deleted = False
            to_update.append_change(req)

        for req in list(id_req.values()):
            db.session.add(req)

        try:
            db.session.commit()
        except IntegrityError as e:
            db.session.rollback()
            if isinstance(e.orig, psycopg2.errors.UniqueViolation):
                logger.warning("Error when saving sync requests", exc=e)
            else:
                logger.error("IntegrityError when saving sync requests", exc=e)
                flask.abort(409, "Cannot add or update sync requests. Please try again later")

        for s in saved:
            if len(s.pending_changes) != id_to_pending_len.get(s.id):  # type: ignore
                to_sync.append(s)

        for s in to_sync:
            _shift_to_last_position(s)
            _update_replaced_and_replacing_client_ids(s)

        _update_grouped_sync_requests(to_sync)

        return submissions_sync_schema.dump(to_sync, many=True)


@set_context_in_g(key=INTERNAL_CALLER_CONTEXT_KEY, value=CallerContext.SUBMISSION_SYNC)
def run_sync(body: dict) -> tuple[dict, int]:
    if not current_user.is_internal_machine_user:
        flask.abort(403)

    request: RunSyncRequest = run_sync_request_schema.load(body)
    _ensure_run_id_provided(request)

    with no_expire_on_commit():
        organization_processors = {
            organization_id: SubmissionSyncProcessor(
                SubmissionSyncProcessorConfiguration.from_dict(config), organization_id
            )
            for organization_id, config in get_sync_config_map().items()
        }
        requests: list[SubmissionSync] = SubmissionSync.query.filter(SubmissionSync.id.in_(request.ids)).all()
        results = []
        for req in requests:
            processor: SubmissionSyncProcessor | None = organization_processors.get(req.organization_id)
            if not processor:
                logger.error(
                    (
                        "Processor for Organization not created. "
                        "This is possibly due to missing configuration for the organization"
                    ),
                    organization_id=req.organization_id,
                )
                continue
            lock_acquired: bool = False
            try:
                lock_name = f"submission_sync_{req.id}"
                lock_id = str(req.id)
                lock = redis_lock.Lock(current_app.locks_client, name=lock_name, id=lock_id, expire=60)
                lock_acquired = lock.acquire(blocking=False)
                if not lock_acquired:
                    logger.info("Lock not acquired skipping sync request", submission_sync_id=req.id)
                    continue

                result = processor.process_sync_request(req, str(request.run_id))
                if result:
                    results.append(result)
            except AlreadyAcquired:
                lock_acquired = False
                logger.info("Lock already acquired skipping sync request", submission_sync_id=req.id)
            finally:
                if lock and lock_acquired:
                    try:
                        lock.release()
                    except redis_lock.NotAcquired:
                        logger.warning("Lock already expired")

            # We are in no_expired_on_commit context so all operations happening in request processor and its handlers
            # will use performance boost as there are multiple commits happening inside deeper methods.
            # However when switching to the next request we need to expire all objects in the session to avoid
            # stale data and invalid duplicates for some relations.
            db.session.commit()
            db.session.expire_all()

        if request.run_id and not IS_TEST:
            try:
                save_sync_results_to_glue(results, request.run_id)
            except Exception:
                logger.exception("Failed to upload synchronization results to Glue", results=results, request=request)

        return synchronization_result_schema.dump(results, many=True)


def get_pending_sync_requests(organization_id: int) -> dict:
    if not current_user.is_internal_machine_user:
        flask.abort(403)
    if organization_id not in get_sync_config_map():
        flask.abort(400, f"No sync configuration created for organization id {organization_id}")

    pending_sync_requests = (
        SubmissionSync.query.filter(SubmissionSync.organization_id == organization_id)
        .filter(
            or_(SubmissionSync.applied == False, func.jsonb_array_length(SubmissionSync.pending_changes) > 0),
        )
        .all()
    )
    logger.info("Pending sync requests", organization_id=organization_id, count=f"{len(pending_sync_requests)}")
    return submissions_sync_schema.dump(pending_sync_requests, many=True)


def get_matching_results(body: dict) -> tuple[dict, int]:
    result = []
    request: MatcherDryRunRequest = MatcherDryRunRequest.from_dict(body)

    submission_lookup = SubmissionLookup(request.lookup_config)

    requests: list[SubmissionSync] = SubmissionSync.query.filter(SubmissionSync.id.in_(request.ids)).all()

    for req in requests:
        res = submission_lookup.lookup(req)
        resp = MatcherDryRunResult(
            req.id,
            req.policy_number,
            req.submission_created_at_reference,
            req.submission_name,
            [
                DryRunSubmissionMatch(
                    m.submission.id, m.submission.name, m.submission.created_at, m.matcher, m.matching_score
                )
                for m in res.matches
            ],
        )
        result.append(resp)

    return dry_run_response_schema.dump(result, many=True)


def schedule_for_resync(body: dict) -> tuple[dict | None, int]:
    if not current_user.is_internal_machine_user:
        flask.abort(403)

    request: RunSyncRequest = run_sync_request_schema.load(body)

    db.session.query(SubmissionSync).filter(SubmissionSync.id.in_(request.ids)).update(
        {"applied": False},
        synchronize_session=False,
    )
    db.session.commit()

    return None, HTTPStatus.NO_CONTENT


def shift_to_last_position(body: dict) -> tuple[dict | None, int]:
    if not current_user.is_internal_machine_user:
        flask.abort(403)

    request: RunSyncRequest = run_sync_request_schema.load(body)

    requests: list[SubmissionSync] = SubmissionSync.query.filter(SubmissionSync.id.in_(request.ids)).all()

    for r in requests:
        shifted = True
        while shifted:
            shifted = r.shift(force=True)
    db.session.commit()

    return None, HTTPStatus.NO_CONTENT


def get_sync_requests_for_client_ids(body: dict) -> tuple[dict | None, int]:
    if not current_user.is_internal_machine_user:
        flask.abort(403)

    request: GetSyncRequestsForClientIdsRequest = GetSyncRequestsForClientIdsRequest.from_dict(body)
    sync_requests = SubmissionSyncRequestDAO.get_sync_requests(request.client_ids, request.organization_id)

    return submissions_sync_schema.dump(sync_requests, many=True), HTTPStatus.OK


def get_sync_request_by_id(id: str) -> tuple[dict | None, int]:
    if not current_user.is_internal_machine_user:
        flask.abort(403)

    sync_request = SubmissionSync.query.get_or_404(id)
    return submissions_sync_schema.dump(sync_request), HTTPStatus.OK


def _save_sync_matching_request(req: SyncMatchingRunRequest) -> SyncMatchingRun:
    matching_run = SyncMatchingRun()
    matching_run.id = req.run_id
    matching_run.producer_config = req.config
    matching_run.organization_id = req.organization_id
    matching_run.automatically_triggered = bool(req.automatically_triggered)
    for submission_sync_id in req.submission_sync_ids:
        matching_result = SyncMatchingResult()
        matching_result.submission_sync_id = submission_sync_id
        matching_run.sync_matching_results.append(matching_result)
    db.session.add(matching_run)
    db.session.commit()

    if matching_run.automatically_triggered:
        KalepaEventsHandler.send_sync_matching_run_created_event(matching_run, req.assign_client_ids)
    return matching_run


# POST /submissions/sync/sync_matching_runs
def create_sync_matching_run(body: dict) -> tuple[dict, int]:
    if not current_user.is_internal_machine_user:
        flask.abort(403)

    run_request: SyncMatchingRunRequest = sync_matching_run_req_schema.load(body)

    if not run_request.submission_sync_ids:
        flask.abort(400, "No submission sync ids provided")

    if not run_request.config:
        flask.abort(400, "Invalid config provided")

    matching_run = _save_sync_matching_request(run_request)

    return {
        "run_id": str(matching_run.id),
        "status": matching_run.status,
        "organization_id": matching_run.organization_id,
        "started_at": None,
    }, HTTPStatus.CREATED


# GET /submissions/sync/sync_matching_runs/{run_id}
def get_sync_matching_run_by_id(run_id: str) -> tuple[dict, int]:
    if not current_user.is_internal_machine_user:
        flask.abort(403)

    matching_run = SyncMatchingRun.query.get_or_404(run_id)
    return {
        "run_id": str(matching_run.id),
        "status": matching_run.status,
        "organization_id": matching_run.organization_id,
        "started_at": matching_run.started_at if matching_run.started_at else None,
    }, HTTPStatus.OK


# POST /submissions/sync/sync_matching_runs/{run_id}/test_run
def sync_matching_run_test(run_id: str) -> tuple[None, int]:
    # import everything here because this should be used only for testing purposes
    import os

    is_test = os.environ.get("TESTING_SCOPE", "false").lower() == "true"
    env = os.environ.get("KALEPA_ENV", "dev")

    if not is_test or env == "prod":
        flask.abort(400, "This endpoint is only for testing purposes")

    from copilot.adhoc_tasks.submission_sync_matching import SubmissionSyncMatchingTask

    task = SubmissionSyncMatchingTask(
        adhoc_task_name="conifer_load_agents",
        executing_capi_user_email=current_user.email,
        adhoc_task_input={
            "run_id": run_id,
            "assign_client_ids": True,
        },
    )
    task.is_test = True
    task.execute()

    return None, HTTPStatus.NO_CONTENT


# POST /submissions/sync/delete_sync_requests
def delete_sync_requests(body: dict) -> tuple[None, int]:
    if not current_user.is_internal_machine_user:
        flask.abort(403)

    request: DeleteSyncRequests = delete_sync_requests_schema.load(body)

    if not request.client_ids:
        flask.abort(400, "No client ids provided")

    db.session.query(SubmissionSync).filter(
        SubmissionSync.policy_number.in_(request.client_ids),
        SubmissionSync.organization_id == request.organization_id,
    ).update({SubmissionSync.is_deleted: True}, synchronize_session=False)
    db.session.commit()

    return None, HTTPStatus.ACCEPTED


# PATCH /submissions/sync/sync_matching_runs/{run_id}/status
def update_sync_matching_run_status(run_id: str, body: dict) -> tuple[None, int]:
    if not current_user.is_internal_machine_user:
        flask.abort(403)

    if not run_id:
        flask.abort(400, "Parameter run_id is required for this endpoint")

    status = body.get("status")
    if not status:
        flask.abort(400, "No 'status' property found in body")

    if status not in SyncMatchingRunStatus:
        flask.abort(400, "Invalid status provided")

    status_enum: SyncMatchingRunStatus = SyncMatchingRunStatus[status]

    matching_run = SyncMatchingRun.query.get_or_404(run_id)
    matching_run.status = status_enum
    db.session.add(matching_run)
    db.session.commit()

    if status_enum.is_terminal_status:
        KalepaEventsHandler.send_sync_matching_run_finished_event(matching_run)

    return None, HTTPStatus.NO_CONTENT
