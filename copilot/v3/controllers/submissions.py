from collections import defaultdict
from dataclasses import asdict
from datetime import datetime, timedelta
from http import HTTPStatus
from uuid import UUID, uuid4
import json
import os
import re

from common.clients.utils import chunks
from common.logic.queries import quicker_paginate
from dateutil import parser
from dateutil.relativedelta import relativedelta
from email_validator import EmailNotValidError, validate_email
from entity_resolution_service_client_v3 import Entity, Premises
from flask import current_app
from flask_login import current_user
from infrastructure_common.logging import get_logger
from marshmallow import ValidationError
from paragon_ims_api_client.models.submission_information import SubmissionInformation
from paragon_ims_api_client.paragon_ims_client import ParagonIMSAPIClient
from retrying import retry
from sqlalchemy import (
    TEXT,
    and_,
    any_,
    asc,
    case,
    cast,
    desc,
    extract,
    func,
    inspect,
    or_,
    text,
)
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import (
    ColumnProperty,
    contains_eager,
    exc,
    joinedload,
    lazyload,
    load_only,
    selectinload,
)
from sqlalchemy.orm.exc import StaleDataError
from sqlalchemy.orm.session import make_transient
from static_common.enums.brokerage_employee_roles import BrokerageEmployeeRoles
from static_common.enums.entity import EntityInformation
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.organization import ExistingOrganizations, OrganizationGroups
from static_common.enums.origin import Origin
from static_common.enums.sensible import SensibleStatus
from static_common.enums.submission_business import (
    SubmissionBusinessEntityNamedInsured,
    SubmissionBusinessEntityRole,
)
from static_common.enums.submission_processing_state import SubmissionProcessingState
from static_common.enums.underwriters import SubmissionUserSource
from static_common.models.paragon import ImsCoverage
from static_common.models.submission_action.core import SubmissionAction
from static_common.models.submission_events import (
    SubmissionUnderwritersChangedPayload,
    UnderwriterChange,
    UnderwriterChangeType,
)
from static_common.schemas.first_party import FirstPartyFieldSchema
from static_common.schemas.submission_action.core import (
    PolymorphicSubmissionActionSchema,
)
from werkzeug.exceptions import BadRequest, Conflict, Forbidden, HTTPException, NotFound
import flask
import psycopg2
import pytz
import redis_lock

from copilot.clients.ers_v3 import ERSClientV3
from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.clients.paragon_ims import get_logged_in_paragon_ims_client
from copilot.constants import NATIONWIDE_ALIGN_STAGES_HOURS
from copilot.db_debug_utils import with_db_lock_debug
from copilot.exceptions import NoPDSMailBox
from copilot.kalepa_domain_events.kalepa_events_handler import (
    KalepaEventsHandler,
    StageChangePayload,
)
from copilot.logic.dao.report_dao import ReportDAO
from copilot.logic.dao.submission_client_id_dao import SubmissionClientIdDAO
from copilot.logic.dao.submission_dao import SubmissionDAO
from copilot.logic.loss_runs.deduplication import (
    generate_full_hash,
    generate_partial_hash,
    get_loss_policy_duplicate,
)
from copilot.logic.loss_runs.lob import normalize_loss_policy_lob_fields
from copilot.logic.loss_runs.manual_processing import (
    add_manual_processing_cache_entry,
    get_loss_run_files_to_upload,
    get_manual_processing_cache_key,
    invoke_loss_run_processing_internal,
    manual_processing_cache_exists,
    try_add_notifyee_to_manual_processing_cache,
)
from copilot.logic.nationwide_boss import (
    handle_client_stage_id_change_for_nationwide_boss,
    handle_submission_data_update,
)
from copilot.logic.paragon import (
    IMS_COVERAGE_TO_COPILOT_PROGRAM_MAPPING,
    IMS_TO_COPILOT_COVERAGE_MAPPING,
    has_one_primary_and_one_excess_liabilities,
)
from copilot.logic.pds.submission_handler import SubmissionHandler
from copilot.logic.pds.verified_shells_service import VerifiedShellsService
from copilot.logic.reports import start_report_processing
from copilot.logic.submission_action.factory import SubmissionActionExecutorFactory
from copilot.logic.submission_identifiers import patch_identifiers
from copilot.logic.submissions import (
    extract_submission_level_data,
    should_update_submission,
)
from copilot.logic.synchronization.configuration import (
    RESYNC_EXCLUDE,
    get_sync_config_map,
)
from copilot.logic.synchronization.manual_reconciliation import (
    cleanup_after_manual_client_id_assignment,
)
from copilot.logic.taxonomy import (
    update_submission_gl_code_if_needed,
    update_submission_icc_code_if_needed,
    update_submission_naics_if_needed,
    update_submission_sic_if_needed,
)
from copilot.logic.users.errors import (
    CrossOrgUserAssignmentError,
    MultipleUsersAssignmentError,
    UserAlreadyAssignedError,
    UserNotFoundError,
)
from copilot.logic.users.underwriter_assigner import UnderwriterAssignerConfig
from copilot.logic.users.users import auto_assign_underwriters
from copilot.models import (
    BrokerageEmployee,
    Organization,
    ReportPermission,
    Submission,
    SubmissionBusiness,
    SubmissionHistory,
    User,
    db,
)
from copilot.models.email_template import ScheduledEmail
from copilot.models.emails import Email
from copilot.models.files import File, ProcessedFile
from copilot.models.losses import (
    LossLobInferenceRequirement,
    SummaryOfLosses,
    SummaryOfLossesGrouping,
    UpdateLossLobBulkRequest,
)
from copilot.models.policy import (
    CARRIER_REGEX,
    Loss,
    LossesEnvelope,
    LossPolicy,
    default_loss_grouping,
    loss_formatted_carrier,
)
from copilot.models.quality_audit_question import QualityAuditQuestion
from copilot.models.reports import (
    ClientSubmissionStageConfig,
    Coverage,
    ReportV2,
    SubmissionClientId,
    SubmissionCoverage,
    SubmissionIdentifier,
    SubmissionNote,
    SubmissionUser,
    SubmissionUserRequest,
    UserSubmissionNotification,
    adjust_submission_fields,
)
from copilot.models.sent_email import SentEmail
from copilot.models.shareholders import Shareholder, ShareholderSchema
from copilot.models.submission_level_extracted_data import SubmissionLevelExtractedData
from copilot.models.submission_premises import SubmissionPremises
from copilot.models.submission_sync import (
    SubmissionIdentifiersSuggestion,
    SubmissionIdentifiersSuggestionResponse,
    SubmissionSync,
)
from copilot.models.types import (
    BrokerageEmployeeSource,
    LineOfBusinessType,
    PermissionType,
    ReportShadowType,
    SubmissionActionType,
    SubmissionEvent,
    SubmissionParentType,
    SubmissionStage,
)
from copilot.notifications.errors import SendNotificationError
from copilot.notifications.submission_notification_handler_v2 import (
    get_dynamic_template_data,
)
from copilot.schemas.ers import ERSEntityEnvelopeSchema
from copilot.schemas.files import SubmissionFilesDataSchema
from copilot.schemas.losses import (
    LossLobInferenceRequirementSchema,
    SummaryOfLossesResponseSchema,
    UpdateLossLobBulkRequestSchema,
)
from copilot.schemas.policy import LossesEnvelopeSchema, LossPolicySchema, LossSchema
from copilot.schemas.quality_audit_question import QualityAuditQuestionSchema
from copilot.schemas.report import (
    ClientSubmissionStageConfigSchema,
    DeleteSubmissionIdentifierRequestSchema,
    ReportV2Schema,
    SendSubmissionEmailRequest,
    SetSubmissionIdentifiersRequestSchema,
    SubmissionBulkDeclinePatch,
    SubmissionBulkDeclinePatchSchema,
    SubmissionBusinessSchema,
    SubmissionClientIdSchema,
    SubmissionIdentifierSchema,
    SubmissionIdentifiersSuggestionResponseSchema,
    SubmissionPremisesSchema,
    SubmissionSchema,
    SubmissionStageUpdateRequest,
    SubmissionStageUpdateRequestSchema,
    SubmissionUserRequestSchema,
    SubmissionUserSchema,
    SubmissionUserUpdateSchema,
    UserSubmissionNotificationSchema,
)
from copilot.schemas.sent_email import SentEmailSchema
from copilot.schemas.submission_history import SubmissionHistorySchema
from copilot.schemas.submission_level_extracted_data import (
    SubmissionLevelExtractedDataSchema,
)
from copilot.utils import (
    commit_ignore_unique_violation,
    convert_to_uuid,
    get_most_suitable_premises,
)
from copilot.v3.controllers.paragon import (
    _split_paragon_excess_and_surplus_sub_without_ims_correlations,
)
from copilot.v3.controllers.permissions import add_permission
from copilot.v3.utils.db_session import no_expire_on_commit
from copilot.v3.utils.reports import (
    enrich_and_store_grouped_first_party_fields,
    enrich_submission_businesses_with_aliases,
)
from copilot.v3.utils.submission import (
    create_external_resource_snapshots_if_needed,
    get_submission_data,
    handle_updating_naics_code,
    maybe_update_renewal_reason_for_declining,
)

logger = get_logger()

_SUMMARY_OF_LOSSES_UNKNOWN_FIELD = "Unknown"
DEFAULT_SUBMISSION_FIELDS = [
    "id",
    "created_at",
    "name",
    "owner_id",
    "stage",
    "account_name",
    "proposed_effective_date",
    "files",
    "proposed_effective_date",
    "policy_expiration_date",
    "due_date",
    "received_date",
    "stage_details",
    "history",
    "coverages",
    "deductibles",
    "businesses",
    "description_of_operations",
    "is_renewal",
    "is_verified",
    "is_verification_required",
    "assigned_underwriters",
    "is_metrics_set_manually",
    "renewal_creation_date",
    "declined_user_id",
    "declined_date",
    "reason_for_declining",
    "parent_id",
    "clearing_issues",
    "primary_naics_code",
    "iso_gl_code",
    "lob_id",
    "line_of_business",
    "brokerage_contact",
    "mode",
    "client_submission_ids",
    "lost_reasons",
    "decline_email_recipient_address",
    "account_id",
    "generated_description_of_operations",
    "email_project_description",
    "missing_documents",
    "broker",
    "brokerage",
    "clearing_assignee_id",
]
loss_schema = LossSchema()
loss_policy_schema = LossPolicySchema()
first_party_field_schema = FirstPartyFieldSchema()
submission_user_update_schema = SubmissionUserUpdateSchema()
loss_envelope_schema = LossesEnvelopeSchema()
summary_of_losses_schema = SummaryOfLossesResponseSchema()
update_loss_bulk_request_schema = UpdateLossLobBulkRequestSchema()
loss_lob_inference_requirement_schema = LossLobInferenceRequirementSchema()
shareholder_schema_many = ShareholderSchema(many=True)
submission_files_data_schema = SubmissionFilesDataSchema()
set_submission_identifiers_req_schema = SetSubmissionIdentifiersRequestSchema()
delete_submission_identifier_req_schema = DeleteSubmissionIdentifierRequestSchema()
submission_identifiers_suggestions_schema = SubmissionIdentifiersSuggestionResponseSchema()
submission_premises_schema = SubmissionPremisesSchema()


def get_submission_or_404(submission_id) -> Submission:
    submission = Submission.query.get_or_404(submission_id, description="The submission with specified ID wasn't found")
    if submission.is_deleted:
        flask.abort(404, "The submission with specified ID is deleted")
    return submission


def get_light_submission(
    submission_id, additional_fields: list[str] | None = None, throw_not_found_for_deleted: bool = True
) -> Submission:
    if not additional_fields:
        additional_fields = []

    fields = ["is_deleted", "id", "stage", "owner_id", "report_id"]
    if additional_fields:
        fields.extend(additional_fields)
    submission = (
        Submission.query.options(
            lazyload(Submission.businesses),
            lazyload(Submission.user),
            lazyload(Submission.deductibles),
            lazyload(Submission.coverages),
            lazyload(Submission.files),
            lazyload(Submission.clearing_issues),
            lazyload(Submission.assigned_underwriters),
            load_only(*adjust_submission_fields(fields)),
        )
        .filter(Submission.id == submission_id)
        .first()
    )
    if not submission or (submission.is_deleted and throw_not_found_for_deleted):
        flask.abort(404, "The submission with specified ID does not exist")
    return submission


def load_submission_history(
    submission_id: str,
    parent_type: SubmissionParentType | None = SubmissionParentType.SUBMISSION_BUSINESS,
    action_type: SubmissionActionType | None = None,
) -> list[SubmissionHistory]:
    query = SubmissionHistory.query.filter(SubmissionHistory.submission_id == submission_id)

    if action_type:
        query = query.filter(SubmissionHistory.submission_action_type == action_type)
    if parent_type:
        query = query.filter(SubmissionHistory.parent_type == parent_type)

    submission_history = query.order_by(SubmissionHistory.occurred_at.desc()).all()
    return submission_history


def get_submissions(
    name: str | None = None,
    report_name: str | None = None,
    organization_id: int | None = None,
    fuzzy: bool | None = None,
):
    if name and report_name:
        raise ValidationError("At most one of name and report_name can be used!")

    fields = ["id", "name", "stage"]
    submissions = SubmissionDAO.get_submissions(0, fields, name, report_name, organization_id)
    if not submissions:
        submissions = SubmissionDAO.get_submissions(1, fields, name, report_name, organization_id)
    if not submissions and fuzzy:
        submissions = SubmissionDAO.get_submissions(2, fields, name, report_name, organization_id)

    return {"submissions": SubmissionSchema(only=fields, many=True).dump(submissions)}


def get_submission_lite(
    id: str,
    expand: list[str] | None = None,
    enrich_with_ers_data: bool | None = False,
    call_origin: str | None = None,
):
    if not expand:
        expand = []
    if not current_user.has_submission_permission(PermissionType.VIEWER, id):
        flask.abort(403)

    context = {}

    expand_businesses = any("businesses" in e for e in expand)
    if enrich_with_ers_data and not expand_businesses:
        flask.abort(400, "Enrich with ERS data requires expanding businesses")

    only = {
        "id",
        "stage",
        "name",
        "owner_id",
        "is_verification_required",
        "is_verified",
        "due_date",
        "proposed_effective_date",
        "primary_naics_code",
        "iso_gl_code",
        "created_at",
        *expand,
    }
    columns_to_load = {a.key for a in inspect(Submission).attrs if isinstance(a, ColumnProperty) if a.key in only}
    columns_to_load.add("is_deleted")
    if "report_ids" in only or "report_is_deleted" in only:
        columns_to_load.add("report_id")
    if "broker_group" in expand:
        columns_to_load.add("broker_id")
    query = Submission.query.options(
        lazyload(Submission.assigned_underwriters), load_only(*adjust_submission_fields(columns_to_load))
    )
    if any("files" in e for e in expand):
        files_options = selectinload(Submission.files)

        if "files.s3_key" in expand:
            only.remove("files.s3_key")
            context = {"include_s3_key": True}

        if any("files.processed_file" in e for e in expand):
            context = {"dump_processed_file": True}
            processed_files_fields = [
                ProcessedFile.id,
                ProcessedFile.created_at,
                ProcessedFile.updated_at,
            ]

            if "files.processed_file.business_resolution_data" in expand:
                processed_files_fields.append(ProcessedFile.business_resolution_data)
            if "files.processed_file.entity_mapped_data" in expand:
                processed_files_fields.append(ProcessedFile.entity_mapped_data)
            if "files.processed_file.onboarded_data" in expand:
                processed_files_fields.append(ProcessedFile.onboarded_data)
            if "files.processed_file.processed_data" in expand:
                processed_files_fields.append(ProcessedFile.processed_data)

            if any("files.processed_file" == e for e in expand):
                # exactly "files.processed_file" is requested, meaning full load
                files_options = files_options.joinedload(File.processed_file)
            else:
                files_options = files_options.joinedload(File.processed_file).load_only(*processed_files_fields)

        query = query.options(files_options)

    if expand_businesses:
        query = query.options(selectinload(Submission.businesses))
    if any("coverages" in e for e in expand):
        query = query.options(selectinload(Submission.coverages))
    if any("assigned_underwriters" in e for e in expand) or any("broker_group" in e for e in expand):
        query = query.options(
            joinedload(Submission.assigned_underwriters)
            .joinedload(SubmissionUser.user)
            .options(lazyload(User.settings), lazyload(User.organization), selectinload(User.groups))
        )
    if any("brokerage" in e for e in expand):
        query = query.options(joinedload(Submission.brokerage))
    if any("policies" in e for e in expand):
        query = query.options(joinedload(Submission.policies))
    if any("client_submission_ids" in e for e in expand):
        query = query.options(joinedload(Submission.client_submission_ids))
    if any("clearing_issues" in e for e in expand):
        query = query.options(joinedload(Submission.clearing_issues))
    if any("brokerage_contact" in e for e in expand):
        query = query.options(joinedload(Submission.brokerage_contact))
    if "broker" in expand:
        query = query.options(joinedload(Submission.broker))
    if any("deductibles" in e for e in expand):
        query = query.options(selectinload(Submission.deductibles))
    if any("organization_group" in e for e in expand):
        query = query.options(joinedload(Submission.report).joinedload(ReportV2.correspondence))
    if any("identifiers" in e for e in expand):
        query = query.options(joinedload(Submission.identifiers))
    submission = query.get_or_404(id, description="The Submission with specified ID wasn't found")
    if submission.is_deleted:
        flask.abort(404, "The submission with specified ID is deleted")

    if enrich_with_ers_data:
        logger.warning("Enrich with ERS data is deprecated and has no effect")

    return SubmissionSchema(only=list(only), context=context).dump(submission)


def get_submission_by_id(id: str, expand: list[str] | None = None, enrich_with_ers_data: bool | None = False):
    if not expand:
        expand = []
    id = UUID(id)
    submission = get_submission_or_404(id)
    if not (current_user.is_internal_machine_user or current_user.has_submission_permission(PermissionType.VIEWER, id)):
        flask.abort(403)
    if enrich_with_ers_data:
        logger.warning("Enrich with ERS data is deprecated and has no effect")
    only = DEFAULT_SUBMISSION_FIELDS + expand
    return SubmissionSchema(only=only).dump(submission)


def get_submission_report_id(id: str):
    id = UUID(id)
    submission = SubmissionDAO.get_minimal_submission_or_404(id)
    return submission.report_id


def get_submission_report_public_info(id: str):
    id = UUID(id)

    report = (
        ReportV2.query.options(
            joinedload(ReportV2.report_permissions).options(joinedload(ReportPermission.grantee)),
            joinedload(ReportV2.submission).options(
                joinedload(Submission.assigned_underwriters).options(joinedload(SubmissionUser.user)),
                joinedload(Submission.coverages).options(joinedload(SubmissionCoverage.coverage)),
                selectinload(Submission.files),
            ),
        )
        .join(ReportV2.submission)
        .filter(Submission.id == id)
        .first()
    )

    if not report:
        flask.abort(404, "The submission with specified ID wasn't found")

    if current_user.organization_id != report.organization_id:
        flask.abort(403)

    return ReportV2Schema(
        only=[
            "id",
            "permissions",
            "is_deleted",
            "submissions.id",
            "submissions.frozen_as_of",
            "submissions.stage",
            "submissions.primary_naics_code",
            "submissions.files",
            "submissions.is_renewal",
            "submissions.businesses",
            "submissions.created_at",
            "submissions.proposed_effective_date",
            "submissions.coverages",
            "submissions.coverages.coverage.name",
            "submissions.coverages.coverage_type",
            "submissions.assigned_underwriters",
            "submissions.coverages.coverage.coverage_types",
            "submissions.coverages.coverage.display_name",
            "current_user_permission_type",
        ]
    ).dump(report)


def update_submission(id: str, body: dict, enrich_with_ers_data: bool | None = False):
    submission = update_submission_internal(id, body, enrich_with_ers_data)
    return SubmissionSchema(only=DEFAULT_SUBMISSION_FIELDS).dump(submission)


@with_db_lock_debug(table_name="submissions")
def update_submission_internal(
    id: str,
    body: dict,
    enrich_with_ers_data: bool | None = False,
    is_external: bool = False,
    is_update_from_sync: bool = False,
) -> Submission:
    id = UUID(id)
    submission = get_submission_or_404(id)
    ignore_verified = False

    if not (current_user.is_internal_machine_user or current_user.has_submission_permission(PermissionType.EDITOR, id)):
        flask.abort(403)
    # Do not allow None clearing status to be set.
    if "clearing_status" in body and body["clearing_status"] is None:
        body.pop("clearing_status")
    if "prevent_clearing_updates" in body and body["prevent_clearing_updates"] is None:
        body.pop("prevent_clearing_updates")

    current_clearing_status = submission.clearing_status
    current_stage = submission.stage
    was_frozen_before_update = submission.is_frozen
    naics_verified_before_update = submission.is_naics_verified
    primary_naics_code_before_update = submission.primary_naics_code
    sic_code_before_update = submission.sic_code
    current_effective_date = submission.proposed_effective_date
    risk_score_before_update = submission.recommendation_v2_score
    client_stage_id_before_update = submission.client_stage_id
    client_clearing_status_before_update = submission.client_clearing_status
    brokerage_source = BrokerageEmployeeSource.SYNC if is_update_from_sync else BrokerageEmployeeSource.MANUAL

    correspondence_fields = {
        "decline_email_cc": "email_cc",
        "decline_email_recipient_address": "recipient_address",
        "active_email_template_id": "active_email_template_id",
    }
    update_correspondence_request = {}
    for field in correspondence_fields:
        if value := body.get(field):
            update_correspondence_request[correspondence_fields[field]] = value
    if update_correspondence_request:
        # Importing inline due to circular import.
        from copilot.v3.controllers.emails import (
            create_new_correspondence,
            update_correspondence,
        )

        correspondence_id = submission.report.correspondence_id
        if not correspondence_id:
            create_new_correspondence(submission.report.id, update_correspondence_request)
        else:
            update_correspondence(correspondence_id, update_correspondence_request)

    emails_to_test = (body.get("decline_email_cc") or [])[:]
    if decline_email_recipient := body.get("decline_email_recipient_address"):
        emails_to_test.append(decline_email_recipient)
    try:
        for email in emails_to_test:
            validate_email(email)
    except EmailNotValidError as e:
        logger.warning("The passed email is invalid", submission_id=submission.id, email=email, error=e)
        flask.abort(400, f"The passed email {email} is invalid - {e}")

    # TODO(ENG-13475): This is a patch as we need to be able to update the field even if the submission is frozen.
    #  Should be removed.
    email_fields = ["decline_email_cc", "decline_email_recipient_address"]
    email_patch = {k: v for k, v in body.items() if k in email_fields}
    was_patched = False
    if email_patch:
        SubmissionSchema().load(email_patch, instance=submission)
        db.session.commit()
        was_patched = True

    if brokerage_contact_id := body.get("brokerage_contact_id"):
        brokerage_contact: BrokerageEmployee = BrokerageEmployee.query.filter(
            BrokerageEmployee.id == brokerage_contact_id
        ).first()
        if BrokerageEmployeeRoles.CORRESPONDENCE_CONTACT not in brokerage_contact.roles:
            brokerage_contact.roles.append(BrokerageEmployeeRoles.CORRESPONDENCE_CONTACT)
            db.session.commit()

    broker_id_before_update = submission.broker_id
    brokerage_id_before_update = submission.brokerage_id
    brokerage_contact_id_before_update = submission.brokerage_contact_id
    fni_state_before_update = submission.fni_state

    if identifiers := body.get("identifiers"):
        identifiers = SubmissionIdentifierSchema(many=True).load(identifiers)
        patch_identifiers(submission, identifiers)
        db.session.refresh(submission)
        del body["identifiers"]

    if submission.recommendation_result and body.get("recommendation_result"):
        recommendation_result = _backfill_recommendation_result(submission, body.get("recommendation_result"))
        body["recommendation_result"] = recommendation_result

    is_just_updating_naics = _is_just_changing_primary_naics_code(body)
    is_just_updating_gl_iso_code = _is_just_changing_gl_iso_code(body)

    should_update_naics = "primary_naics_code" in body
    updated_naics_value = body.pop("primary_naics_code", None)

    should_update_sic = "sic_code" in body
    updated_sic_value = body.pop("sic_code", None)

    should_update_gl_code = "iso_gl_code" in body
    updated_iso_gl_code = body.pop("iso_gl_code", None)

    should_update_icc_code = "icc_code" in body
    updated_icc_code_value = body.pop("icc_code", None)

    submission = SubmissionSchema().load(body, instance=submission)

    if should_update_naics:
        update_submission_naics_if_needed(submission, updated_naics_value)

    if should_update_sic:
        update_submission_sic_if_needed(submission, updated_sic_value)

    if should_update_gl_code:
        update_submission_gl_code_if_needed(submission, updated_iso_gl_code)

    if should_update_icc_code:
        update_submission_icc_code_if_needed(submission, updated_icc_code_value)

    if (
        submission.projects
        and body.get("primary_naics_code") is not None
        and not body.get("primary_naics_code").startswith("NAICS_23")
    ):
        flask.abort(400, "Primary NAICS code must start with 'NAICS_23' for projects")

    handle_updating_naics_code(
        submission,
        naics_verified_before_update,
        primary_naics_code_before_update,
        sic_code_before_update,
    )
    VerifiedShellsService(submission).handle_recommendation_update(body)

    current_broker_id = submission.broker_id

    if brokerage_id_before_update != submission.brokerage_id:
        submission.set_brokerage_id(submission.brokerage_id, brokerage_source)

    if broker_id_before_update != submission.broker_id:
        submission.set_broker_id(submission.broker_id, brokerage_source)

    if brokerage_contact_id_before_update != submission.brokerage_contact_id:
        submission.set_brokerage_contact_id(submission.brokerage_contact_id, brokerage_source)

    if body.get("set_status_result") and (
        os.environ.get("KALEPA_ENV", "dev") == "stage"
        or (submission.is_waiting_for_auto_verify and submission.is_auto_verified and not submission.is_verified)
    ):
        logger.info(
            "Got a set_status_result from recommendations",
            submission_id=submission.id,
            set_status_result=body.get("set_status_result", "None"),
        )
        submission.client_stage_id = body.get("set_status_result")
        ignore_verified = True

    if _should_handle_submission_client_status_update(
        submission, is_external, client_stage_id_before_update, ignore_verified
    ):
        current_app.event_service.handle_submission_event(SubmissionEvent.SUBMISSION_CLIENT_STATUS_UPDATED, submission)
        if submission.organization_id == ExistingOrganizations.Nationwide.value:
            handle_client_stage_id_change_for_nationwide_boss(submission)

    if (
        not is_external
        and submission.client_clearing_status != client_clearing_status_before_update
        and submission.is_verified
        and submission.is_boss
        and FeatureFlagsClient.is_feature_enabled(FeatureType.NATIONWIDE_BOSS_R3_RELEASE)
    ):
        current_app.event_service.handle_submission_event(SubmissionEvent.SUBMISSION_CLIENT_DATA_UPDATED, submission)
        handle_submission_data_update(submission)

    if current_broker_id != broker_id_before_update:
        uw_config = UnderwriterAssignerConfig(
            raise_exceptions=False, should_share=True, already_assigned_error=False, delete_other_assigned=True
        )
        auto_assign_underwriters(submission, uw_config, skip_if_assigned=True)

    if (
        not current_user.is_internal_machine_user
        and submission.stage == SubmissionStage.EXPIRED
        and submission.proposed_effective_date
    ):
        if submission.proposed_effective_date != current_effective_date and submission.proposed_effective_date.replace(
            tzinfo=None
        ) > datetime.utcnow().replace(tzinfo=None):
            submission.stage = SubmissionStage.ON_MY_PLATE

    if current_stage == SubmissionStage.DECLINED.value and current_stage != submission.stage:
        submission.send_decline_email = None
        submission.decline_email_sent = None
        submission.send_decline_email_error = None
        submission.decline_email_delivered = None
        submission.decline_email_tracking_id = None

    if (
        not should_update_submission(submission, current_stage, was_frozen_before_update, current_clearing_status)
        and not _is_auditing(body)
        and not is_just_updating_naics
        and not _is_client_stage_update(body)
        and not _is_prevent_clearing_updates_update(body)
        and not _is_just_updating_clearing_assignee(body)
        and not is_just_updating_gl_iso_code
    ):
        logger.info(
            "Submission was not updated",
            submission_id=id,
            current_stage=current_stage,
            current_clearing_status=current_clearing_status,
            was_frozen_before_update=was_frozen_before_update,
            body=body,
        )
        if was_patched:
            return submission
        flask.abort(422, "The submission cannot be updated")

    maybe_update_renewal_reason_for_declining(submission)
    if current_stage != SubmissionStage.DECLINED.value and submission.stage == SubmissionStage.DECLINED.value:
        submission.stage_details = {
            "updateDate": datetime.utcnow().isoformat(),
            "reason": submission.reason_for_declining,
        }
        if os.environ.get("INTERACT_WITH_EXTERNAL_RESOURCES", True) is True:
            KalepaEventsHandler.send_submission_declined_event(submission, current_user.id, current_user.email)

    if (
        current_stage != SubmissionStage.QUOTED.value
        and submission.stage
        in {SubmissionStage.QUOTED.value, SubmissionStage.QUOTED_BOUND.value, SubmissionStage.QUOTED_LOST.value}
        and submission.quoted_date is None
    ):
        submission.quoted_date = datetime.now()

    if (
        fni_state_before_update != submission.fni_state
        and submission.organization_id == ExistingOrganizations.BishopConifer.value
    ):
        auto_assign_underwriters(submission)

    if (
        current_stage != SubmissionStage.QUOTED_BOUND.value
        and submission.stage == SubmissionStage.QUOTED_BOUND.value
        and submission.bound_date is None
    ):
        submission.bound_date = datetime.now()

    if (
        Organization.is_nationwide_for_id(submission.report.organization_id)
        and SubmissionStage.is_terminal(current_stage)
        and not submission.is_in_terminal_stage
        and submission.processing_state == SubmissionProcessingState.CANCELLED
        and submission.origin == "API"
    ):
        submission.processing_state = None
        submission.is_auto_processed = False
        start_report_processing(submission.report)
    if (
        risk_score_before_update != submission.recommendation_v2_score
        and (submission.is_verified or submission.is_verified_shell)
        and SubmissionStage.is_recommendable(submission.stage)
    ):
        current_app.event_service.handle_submission_event(SubmissionEvent.EXTERNAL_FACING_DATA_UPDATED, submission)

    db.session.commit()

    if submission.grouped_first_party_fields:
        entities = enrich_submission_businesses_with_aliases(
            submission.businesses, submission.grouped_first_party_fields
        )
        enrich_and_store_grouped_first_party_fields(submission, entities)
        current_app.event_service.handle_submission_event(
            SubmissionEvent.BUSINESSES_ADDED_TO_SHELL_SUBMISSION, submission, business=None, raw_submission=body
        )
    else:
        if os.environ.get("INTERACT_WITH_EXTERNAL_RESOURCES", True) is True:
            create_external_resource_snapshots_if_needed(
                submission, refresh_existing_snapshots=not was_frozen_before_update
            )
            if enrich_with_ers_data:
                logger.warning("Enrich with ERS data is deprecated and has no effect")
            current_app.event_service.handle_submission_event(
                SubmissionEvent.SUBMISSION_UPDATED, submission, business=None, raw_submission=body
            )
    return submission


def _should_handle_submission_client_status_update(
    submission: Submission, is_external: bool, client_stage_id_before_update: int, ignore_verified: bool = False
) -> bool:
    orgs_that_deal_with_client_status_change = {
        ExistingOrganizations.Nationwide.value: {Origin.API},
        ExistingOrganizations.ARU.value: {Origin.EMAIL},
    }

    return (
        not is_external
        and submission.client_stage_id is not None
        and submission.client_stage_id != client_stage_id_before_update
        and (ignore_verified or submission.is_verified)
        and submission.organization_id in set(orgs_that_deal_with_client_status_change.keys())
        and submission.origin in orgs_that_deal_with_client_status_change.get(submission.organization_id, set())
    )


def _backfill_recommendation_result(submission: Submission, patch: dict[str:any]) -> dict[str, any]:
    if not patch.get("action"):
        patch["action"] = submission.recommendation_result.action
    if not patch.get("priority"):
        patch["priority"] = submission.recommendation_result.priority
    if not patch.get("is_refer"):
        patch["is_refer"] = submission.recommendation_result.is_refer
    if patch.get("score") is None:
        patch["score"] = submission.recommendation_result.score
    if patch.get("score_ml") is None:
        patch["score_ml"] = submission.recommendation_result.score_ml
    if patch.get("pm_rules_modifier") is None:
        patch["pm_rules_modifier"] = submission.recommendation_result.pm_rules_modifier
    return patch


def assign_submission_to_clearing_user(submission_id: str | UUID, body: dict):
    """CWA offers two ways to assign a submission to a clearing user. One is with a drop-down where the end-user can
    choose among peers (ends up using `update_submission`), and one is with the "Assign to me" button that's shown next
    to the drop-down for unassigned submissions (ends up using this endpoint). If the submission was unassigned, there
    might be a race between two users to assign themselves, and we want to prevent a lost update (and notify with 409).
    """

    if not (
        current_user.is_internal_machine_user
        or current_user.has_submission_permission(PermissionType.EDITOR, submission_id)
    ):
        flask.abort(HTTPStatus.FORBIDDEN, "Not allowed")

    if "clearing_assignee_id" not in body:
        flask.abort(HTTPStatus.BAD_REQUEST, "Missing clearing_assignee_id")

    submission = db.session.query(Submission).with_for_update(of=Submission).filter_by(id=submission_id).one()
    if submission.clearing_assignee_id:
        db.session.rollback()
        flask.abort(HTTPStatus.CONFLICT, "Submission already assigned to clearing user")

    submission.clearing_assignee_id = body["clearing_assignee_id"]
    db.session.commit()

    return SubmissionSchema(only=DEFAULT_SUBMISSION_FIELDS).dump(submission)


def send_submission_declined_email(body: dict):
    request = SendSubmissionEmailRequest.from_dict(body)

    submission = get_submission_or_404(request.submission_id)
    user = User.query.get_or_404(request.user_id, description="The user with specified id was not found")

    if not (
        user.is_internal_machine_user or user.has_submission_permission(PermissionType.EDITOR, request.submission_id)
    ):
        flask.abort(403)

    cc_email = submission.report.correspondence.email_cc if submission.report.correspondence else None

    if Organization.is_paragon_for_id(submission.organization_id) and submission.report.is_backfill:
        # For Paragon backfilled submissions we do not want to send email to brokers
        return SentEmailSchema().dump(
            SentEmail(
                error=SendNotificationError.OTHER_ERROR,
                error_message="Declined emails are not sent for Paragon backfilled submissions",
            )
        )

    sent_email = flask.current_app.notifications_handler_v2.send_submission_declined_notification(
        user=user, submission=submission, preview_only=request.preview_only, cc_emails=cc_email
    )

    if not request.preview_only:
        submission.decline_email_sent = sent_email.error is None
        submission.send_decline_email_error = sent_email.error
        submission.decline_email_tracking_id = sent_email.tracking_id
        if Organization.is_arch_or_arch_test_for_id(user.organization_id):
            submission.decline_email_delivered = True
        db.session.commit()

    return SentEmailSchema().dump(sent_email)


def send_scheduled_emails():
    obsolete = ScheduledEmail.query.filter(ScheduledEmail.created_at < datetime.utcnow() - relativedelta(days=7))
    obsolete.delete()
    db.session.commit()

    scheduled_emails = (
        ScheduledEmail.query.join(ScheduledEmail.user)
        .join(ScheduledEmail.submission)
        .join(Submission.assigned_underwriters)
        .limit(51)
        .execution_options(bypass_filter_required=True)
        .all()
    )

    has_more = len(scheduled_emails) == 51

    scheduled_emails = scheduled_emails[:50]

    scheduled_email: ScheduledEmail
    for scheduled_email in scheduled_emails:
        logger.info(
            "Sending scheduled email",
            submission_id=scheduled_email.submission_id,
            template_id=scheduled_email.template_id,
            rule_name=scheduled_email.rule_name,
        )

        flask.current_app.notifications_handler_v2.send_submission_notification(
            user=scheduled_email.user,
            submission=scheduled_email.submission,
            template_id=scheduled_email.template_id,
            preview_only=False,
        )

        db.session.delete(scheduled_email)

    db.session.commit()

    return {"has_more": has_more}


def get_submission_email_dynamic_data(submission_id: str):
    submission = get_submission_or_404(submission_id)

    if not current_user.has_submission_permission(PermissionType.VIEWER, submission_id):
        flask.abort(403)

    return get_dynamic_template_data(submission, current_user, submission.broker, submission.brokerage_contact)


def send_submission_email(body: dict):
    request: SendSubmissionEmailRequest = SendSubmissionEmailRequest.from_dict(body)

    submission = get_submission_or_404(request.submission_id)
    user = User.query.get_or_404(request.user_id, description="The user with specified id was not found")

    if not (
        user.is_internal_machine_user or user.has_submission_permission(PermissionType.EDITOR, request.submission_id)
    ):
        flask.abort(403)

    # Only one email from PM rules per submission
    if request.rule_name:
        if submission.sent_rule_email:
            return None, 204

        submission.sent_rule_email = request.rule_name

    if not request.template_id and not request.custom_template:
        flask.abort(400, "Neither template_id nor custom_template was passed")
    if request.template_id and request.custom_template:
        logger.warning(
            "Both template_id and custom_template were passed! The custom_template will take precedence",
            submission_id=request.submission_id,
            user_id=request.user_id,
            template_id=request.template_id,
            custom_template=request.custom_template,
        )

    if request.cc_emails is None:
        cc_email = submission.report.correspondence.email_cc if submission.report.correspondence else None
    else:
        cc_email = request.cc_emails

    try:
        sent_email: SentEmail = flask.current_app.notifications_handler_v2.send_submission_notification(
            user=user,
            submission=submission,
            template_id=request.template_id,
            preview_only=request.preview_only,
            cc_emails=cc_email,
            custom_template=request.custom_template,
            custom_subject=request.custom_subject,
            attachments=request.attachments,
            add_user_to_cc=request.cc_emails is None,
        )
        if sent_email.error:
            flask.abort(
                400, f"Failed to send email - {sent_email.error.print()} {sent_email.error_message or ''}".strip()
            )

    except NoPDSMailBox as e:
        flask.abort(422, str(e))
    except Exception as e:
        logger.exception("Failed to send email", submission_id=submission.id, error=e)
        flask.abort(422, "Failed to send email")

    if not request.preview_only:
        UserSubmissionNotification.query.filter(
            UserSubmissionNotification.submission_id == submission.id,
            UserSubmissionNotification.user_id == current_user.id,
        ).update({UserSubmissionNotification.seen_emails: UserSubmissionNotification.seen_emails + 1})
        db.session.commit()
    return SentEmailSchema().dump(sent_email)


def bulk_decline_submissions(body: dict):
    request: SubmissionBulkDeclinePatch = SubmissionBulkDeclinePatchSchema().load(body)

    for submission_id in request.ids:
        if not (
            current_user.is_internal_machine_user
            or current_user.has_submission_permission(PermissionType.EDITOR, submission_id)
        ):
            flask.abort(403)

    submissions = [get_submission_or_404(s_id) for s_id in request.ids]
    conflict_message = ""
    declined_ids = []

    for submission in submissions:
        update = SubmissionStageUpdateRequest(
            stage=SubmissionStage.DECLINED,
            client_stage_id=request.client_stage_id,
            reason_for_declining=request.reason,
            send_decline_email=request.send_decline_email,
            declined_date=datetime.utcnow(),
            clearing_status=request.clearing_status,
            prevent_clearing_updates=request.prevent_clearing_updates,
        )
        update_dict = SubmissionStageUpdateRequestSchema().dump(update)
        try:
            update_submission_internal(
                str(submission.id),
                body=update_dict,
            )
            declined_ids.append(submission.id)
        except HTTPException as e:
            db.session.rollback()
            if e.code == 409:
                conflict_message += f"\n{e.description}" if conflict_message else e.description
            else:
                raise e

    if conflict_message:
        flask.abort(409, conflict_message)

    return SubmissionSchema(only=DEFAULT_SUBMISSION_FIELDS).dump(submissions, many=True)


def delete_submission(id: str):
    submission = get_light_submission(UUID(id))
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission.id):
        flask.abort(403)
    submission.is_deleted = True
    SubmissionHandler.handle_submission_deletion(submission)
    db.session.merge(submission)
    db.session.commit()

    current_app.event_service.handle_submission_event(
        SubmissionEvent.SUBMISSION_UPDATED,
        submission,
        business=None,
    )

    return None, 204


def get_business_by_submission(submission_id: str, id: str):
    if not current_user.has_submission_permission(PermissionType.VIEWER, UUID(submission_id)):
        flask.abort(403)
    submission_business = SubmissionBusiness.query.get_or_404(
        UUID(id), description="The submission business with specified ID wasn't found"
    )
    return SubmissionBusinessSchema().dump(submission_business)


def get_businesses_by_submission(submission_id: str):
    if not current_user.has_submission_permission(PermissionType.VIEWER, UUID(submission_id)):
        flask.abort(403)
    submission = get_submission_or_404(UUID(submission_id))
    return SubmissionBusinessSchema().dump(submission.businesses, many=True)


def get_business_at_location(submission_id: str, id: str):
    if not current_user.has_submission_permission(PermissionType.VIEWER, UUID(submission_id)):
        flask.abort(403)
    submission_business: SubmissionBusiness = SubmissionBusiness.query.get_or_404(
        UUID(id), description="The submission business with specified ID wasn't found"
    )
    ers_client: ERSClientV3 = flask.current_app.ers_client_v3
    if not submission_business.business_id:
        return ERSEntityEnvelopeSchema().dump({"entities": []})
    entity: Entity | None = ers_client.get_entity_if_exists(str(submission_business.business_id), with_tenants=True)
    if not entity:
        return ERSEntityEnvelopeSchema().dump({"entities": []})
    premises = get_most_suitable_premises(entity.premises)
    inner_premises: Premises = premises.premises if premises else None
    entities = []
    if inner_premises and inner_premises.entity_tenants_ids:
        entities_dict = ers_client.get_entities(entity_ids=inner_premises.entity_tenants_ids)
        entities = list(entities_dict.values())
    return ERSEntityEnvelopeSchema().dump({"entities": entities})


def create_or_replace_submission_business(submission_id: str, id: str, body: dict):
    id = UUID(id)
    submission_id = UUID(submission_id)
    submission = Submission.query.get_or_404(submission_id)
    if submission.is_frozen:
        flask.abort(422, "The submission is quoted and cannot be modified")
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission_id):
        flask.abort(403)

    existing = SubmissionBusiness.query.filter_by(id=id).first()
    old_business_id = None

    if existing:
        _send_business_disconfirmed_if_apply(
            existing=existing, submission=submission, new_business_id=body.get("business_id")
        )
        old_business_id = existing.business_id
        db.session.delete(existing)
        db.session.commit()

    # SQL alchemy can update the existing record on schema load
    submission_business = SubmissionBusinessSchema().load(body)
    new_business_id = submission_business.business_id
    was_business_id_updated = new_business_id and str(old_business_id) != str(new_business_id)

    if was_business_id_updated and os.environ.get("INTERACT_WITH_EXTERNAL_RESOURCES", True) is True:
        if not flask.current_app.ers_client_v3.get_entity_if_exists(str(submission_business.business_id)):
            flask.abort(409, "The requested business was deleted")

    submission_business.submission_id = submission_id
    submission_business.id = id
    db.session.add(submission_business)
    db.session.commit()

    if not existing:
        current_app.event_service.handle_submission_event(
            SubmissionEvent.BUSINESS_ADDED_TO_SUBMISSION, submission, submission_business
        )
    elif was_business_id_updated:
        current_app.event_service.handle_submission_event(
            SubmissionEvent.BUSINESS_CONFIRMED, submission, submission_business
        )
    return SubmissionBusinessSchema().dump(submission_business)


def _send_business_disconfirmed_if_apply(
    existing: SubmissionBusiness, submission: Submission, new_business_id: UUID | str | None
):
    if not existing.business_id:
        return
    if str(new_business_id) != str(existing.business_id) and str(existing.business_id) not in {
        str(b.business_id) for b in submission.businesses if b.business_id is not None and str(b.id) != str(existing.id)
    }:
        current_app.event_service.handle_submission_event(SubmissionEvent.BUSINESS_DISCONFIRMED, submission, existing)


def auto_assign_submission_users(
    submission_id: str, skip_if_assigned: bool = False, body: dict | None = None
) -> tuple[list[dict], int]:
    submission_id = UUID(submission_id)  # type: ignore
    submission = get_submission_or_404(submission_id)
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission_id):
        flask.abort(HTTPStatus.FORBIDDEN)

    from copilot.logic.users.users import (  # type: ignore
        UnderwriterAssignerConfig,
        auto_assign_underwriters,
    )

    try:
        config = UnderwriterAssignerConfig.from_dict(body) if body else None
        submission_users = auto_assign_underwriters(submission, skip_if_assigned=skip_if_assigned, config=config)
    except ValidationError as e:
        flask.abort(HTTPStatus.BAD_REQUEST, f"Configuration is malformed {e!s}")
    except CrossOrgUserAssignmentError:
        flask.abort(HTTPStatus.BAD_REQUEST, "Cross org users cannot be assigned to any submissions")
    except UserNotFoundError:
        flask.abort(
            HTTPStatus.NOT_FOUND,
            "User was not found",
        )

    return SubmissionUserSchema().dump(submission_users, many=True), HTTPStatus.CREATED


def assign_submission_users(
    submission: Submission,
    user_ids: list[int],
    sharing_permission: PermissionType | None,
    source: SubmissionUserSource | None,
    should_share: bool | None = None,
) -> list[SubmissionUser]:
    from copilot.logic.users.users import (  # type: ignore
        UnderwriterAssignerConfig,
        assign_underwriters,
    )

    users = assign_underwriters(
        submission,
        user_ids,
        sharing_permission=sharing_permission,
        config=UnderwriterAssignerConfig(
            should_share=should_share,
            already_assigned_error=source == SubmissionUserSource.MANUAL,
        ),
        source=source,
    )

    if users and submission.is_verified:
        handle_submission_data_update(submission)
        current_app.event_service.handle_submission_event(SubmissionEvent.SUBMISSION_CLIENT_DATA_UPDATED, submission)
    return users


def add_submission_user(
    submission_id: str,
    body: dict,
    sharing_permission: PermissionType | None = None,
    source: SubmissionUserSource | None = SubmissionUserSource.MANUAL,
) -> tuple[dict, int]:
    submission_id = UUID(submission_id)  # type: ignore
    submission = get_submission_or_404(submission_id)
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission_id):
        flask.abort(HTTPStatus.FORBIDDEN)

    submission_user_request: SubmissionUserRequest = SubmissionUserRequestSchema().load(body)

    if submission.is_frozen and not (submission_user_request.ignore_frozen and current_user.is_internal_machine_user):
        flask.abort(HTTPStatus.UNPROCESSABLE_ENTITY, "The submission is quoted and cannot be modified")

    if source == SubmissionUserSource.RECOMMENDATIONS:
        existing_users = SubmissionUser.query.filter(
            SubmissionUser.submission_id == submission_id,
            or_(
                SubmissionUser.source != SubmissionUserSource.RECOMMENDATIONS,
                SubmissionUser.source == None,
                SubmissionUser.user_id == submission_user_request.user_id,
            ),
        ).all()

        if existing_users:
            if len(existing_users) == 1 and existing_users[0].user.email == "<EMAIL>":
                db.session.delete(existing_users[0])
            else:
                return {}, HTTPStatus.OK

    try:
        users = assign_submission_users(
            submission,
            [submission_user_request.user_id],
            sharing_permission=sharing_permission,
            source=source,
            should_share=submission_user_request.should_share,
        )

        if users:
            return SubmissionUserSchema().dump(users[0]), HTTPStatus.CREATED
        else:
            return {}, HTTPStatus.OK
    except (exc.ObjectDeletedError, psycopg2.errors.ForeignKeyViolation, StaleDataError) as e:
        db.session.rollback()
        logger.warning("Could not assign UW due to integrity error", e=e, submission_id=submission_id)
        return {}, HTTPStatus.CONFLICT
    except CrossOrgUserAssignmentError:
        flask.abort(HTTPStatus.BAD_REQUEST, "Cross org users cannot be assigned to any submissions")
    except UserAlreadyAssignedError:
        flask.abort(
            HTTPStatus.CONFLICT,
            f"User with id {submission_user_request.user_id} is already assigned to this submission",
        )
    except UserNotFoundError:
        flask.abort(
            HTTPStatus.NOT_FOUND,
            f"User with id {submission_user_request.user_id} was not found",
        )
    except MultipleUsersAssignmentError:
        flask.abort(HTTPStatus.CONFLICT, "Cannot assign more than 1 user to this submission")


def delete_submission_user(submission_id: str, user_id: int, ignore_frozen: bool = False):
    submission_id = UUID(submission_id)
    submission = get_submission_or_404(submission_id)
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission_id):
        flask.abort(403)
    if submission.is_frozen and not (ignore_frozen and current_user.is_internal_machine_user):
        flask.abort(422, "The submission is quoted and cannot be modified")
    submission_user: SubmissionUser = SubmissionUser.query.filter_by(
        submission_id=submission_id, user_id=user_id
    ).first()
    if not submission_user:
        flask.abort(404)
    if (
        submission_user.source == SubmissionUserSource.AUTO.value
        and not current_user.is_cs_manager_or_internal_machine_user
        and current_user.is_support
        and not submission_user.user.is_kalepa_internal
    ):
        flask.abort(400)

    uw_changed_payload = SubmissionUnderwritersChangedPayload(
        change_maker_user_id=current_user.id,
        change_maker_user_email=current_user.email,
        submission_id=submission.id,
        report_id=submission.reports[0].id,
        changes=[
            UnderwriterChange(
                change_type=UnderwriterChangeType.DELETED,
                source=submission_user.source or SubmissionUserSource.AUTO,
                underwriter_user_id=submission_user.user_id,
                underwriter_user_email=submission_user.user.email,
            )
        ],
    )
    _delete_submission_user(submission_user)
    current_app.event_service.handle_submission_event(
        SubmissionEvent.SUBMISSION_USERS_UPDATED, submission, additional_data=asdict(uw_changed_payload)
    )
    return None, 204


def _delete_submission_user(submission_user: SubmissionUser):
    if not submission_user:
        return

    db.session.delete(submission_user)
    db.session.commit()


def share_submission_with_user(
    submission: Submission,
    submission_user: SubmissionUser,
    permission_type: PermissionType = None,
    is_underwriter_being_assigned=False,
    send_notification_for_machine_user=False,
    force: bool = False,
):
    report_id = submission.report_id
    logger.info(
        "Sharing submission with user",
        submission_id=submission.id,
        user_id=submission_user.user_id,
        permission_type=permission_type,
        report_id=report_id,
    )
    user = User.query.get_or_404(submission_user.user_id)
    existing_permission: ReportPermission = ReportPermission.query.filter(
        ReportPermission.report_id == report_id, ReportPermission.grantee_user_id == user.id
    ).first()
    target_permission = permission_type or PermissionType.OWNER
    if not existing_permission or existing_permission.permission_type != target_permission or force:
        report_permission = ReportPermission(grantee=user, report_id=report_id, permission_type=target_permission)
        add_permission(
            report_permission,
            is_underwriter_being_assigned=is_underwriter_being_assigned,
            send_notification_for_machine_user=send_notification_for_machine_user,
        )
    else:
        logger.info(
            "Permission exists", submission_id=submission.id, user_id=submission_user.user_id, report_id=report_id
        )


def __submission_business_update_requires_summarization_rerun(
    current_entity_role: SubmissionBusinessEntityRole,
    current_hide_property_facts: bool,
    previous_entity_role: SubmissionBusinessEntityRole,
    previous_hide_property_facts: bool,
) -> bool:
    entity_role_changed: bool = current_entity_role != previous_entity_role
    hide_property_facts_changed: bool = current_hide_property_facts != previous_hide_property_facts

    if entity_role_changed and (
        current_entity_role == SubmissionBusinessEntityRole.PROJECT
        or previous_entity_role == SubmissionBusinessEntityRole.PROJECT
    ):
        return True

    return hide_property_facts_changed


def __maybe_rerun_summarization_for_submission_business_update(
    submission_id: UUID,
    current_entity_role: SubmissionBusinessEntityRole,
    current_hide_property_facts: bool,
    previous_entity_role: SubmissionBusinessEntityRole,
    previous_hide_property_facts: bool,
) -> None:
    try:
        if __submission_business_update_requires_summarization_rerun(
            current_entity_role,
            current_hide_property_facts,
            previous_entity_role,
            previous_hide_property_facts,
        ):
            logger.info("Rerunning summarization after submission business change", submission_id=submission_id)
            KalepaEventsHandler.send_summarization_request(submission_id=submission_id, force=True)
    except Exception:
        logger.exception("Failed to rerun summarization after submission business change")


def update_submission_business(
    submission_id: str,
    id: str,
    body: dict,
    suspend_workers: bool | None = False,
    allow_emit_business_confirmed: bool | None = True,
    force_suspend_workers: bool | None = False,
):
    id = UUID(id)
    submission_id = UUID(submission_id)
    submission = Submission.query.get_or_404(submission_id)
    if (
        not current_user.is_internal_machine_user
        and submission.is_frozen
        and not submission.stage == SubmissionStage.CLEARING_ISSUE
    ):
        flask.abort(422, "Can't modify the Submission due to stage")
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission_id):
        flask.abort(403)
    submission_business = SubmissionBusiness.query.get_or_404(
        id, description="The submission business with specified ID wasn't found"
    )
    if "business_id" in body:
        _send_business_disconfirmed_if_apply(
            existing=submission_business, submission=submission, new_business_id=body.get("business_id")
        )
    old_business_id = submission_business.business_id
    saved_entity_role = submission_business.entity_role
    saved_named_insured = submission_business.named_insured
    saved_hide_property_facts = submission_business.hide_property_facts

    updated = SubmissionBusinessSchema().load(body, instance=submission_business)

    new_business_id = updated.business_id
    was_business_id_updated = new_business_id and str(new_business_id) != str(old_business_id)
    was_entity_role_updated_to_project = (
        updated.entity_role
        and updated.entity_role == SubmissionBusinessEntityRole.PROJECT
        and updated.entity_role != saved_entity_role
    )
    was_named_insured_updated = updated.named_insured and str(updated.named_insured) != str(saved_named_insured)

    if was_business_id_updated and os.environ.get("INTERACT_WITH_EXTERNAL_RESOURCES", True) is True:
        if not flask.current_app.ers_client_v3.get_entity_if_exists(str(submission_business.business_id)):
            flask.abort(409, "The requested business was deleted")

    db.session.add(updated)
    db.session.commit()

    __maybe_rerun_summarization_for_submission_business_update(
        submission_id,  # type: ignore
        updated.entity_role,
        updated.hide_property_facts,
        saved_entity_role,
        saved_hide_property_facts,
    )

    if saved_entity_role != updated.entity_role or was_named_insured_updated:
        try:
            from copilot.v3.controllers.reports import auto_set_name

            auto_set_name(str(submission.report_id))
        except Exception as e:
            if isinstance(e, Forbidden):
                logger.warning("User does not have sufficient privileges")
            else:
                logger.exception("Cannot automatically set report name")

    if was_entity_role_updated_to_project:
        KalepaEventsHandler.send_project_role_added_event(submission)

    if was_business_id_updated or was_named_insured_updated:
        # suspend workers if suspend_workers is True and there are still businesses to confirm
        suspend_workers = suspend_workers and submission.has_unconfirmed_businesses()
        suspend_exclude_facts = suspend_workers or force_suspend_workers
        logger.info(
            "Business confirmed/NI updated",
            suspend_workers=suspend_workers,
            has_unconfirmed=submission.has_unconfirmed_businesses(),
            id=id,
            submission_id=submission.id,
        )
        if was_business_id_updated and saved_named_insured:
            logger.info("Updated NI", submission_id=submission.id, submission_business_id=submission_business.id)
            if not suspend_exclude_facts:
                to_hide = list({sb.business_id for sb in submission.businesses if sb.hide_property_facts})
                current_app.lambda_client.invoke_exclude_facts(submission.id, to_hide)
        if not suspend_workers:
            if allow_emit_business_confirmed:
                current_app.event_service.handle_submission_event(
                    SubmissionEvent.BUSINESS_CONFIRMED, submission, submission_business
                )
        else:
            current_app.event_service.handle_submission_event(
                SubmissionEvent.BUSINESS_AUTO_CONFIRMED, submission, submission_business
            )

    elif saved_hide_property_facts != updated.hide_property_facts:
        logger.info("Hide facts updated", submission_id=submission.id, submission_business_id=submission_business.id)
        if saved_hide_property_facts:
            logger.info(
                "Queueing premises ingestion",
                submission_id=submission.id,
                submission_business_id=submission_business.id,
            )
            KalepaEventsHandler.send_submission_business_hide_property_facts_removed_event(submission)
        suspend_exclude_facts = suspend_workers or force_suspend_workers
        if not suspend_exclude_facts:
            to_hide = list({sb.business_id for sb in submission.businesses if sb.hide_property_facts})
            current_app.lambda_client.invoke_exclude_facts(submission.id, to_hide)
    return SubmissionBusinessSchema().dump(updated)


def delete_submission_business(submission_id: str, id: str):
    id = UUID(id)
    submission_id = UUID(submission_id)
    submission = Submission.query.get_or_404(submission_id)
    if submission.is_frozen:
        flask.abort(422, "The submission is quoted and cannot be modified")
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission_id):
        flask.abort(403)

    submission_business = SubmissionBusiness.query.get_or_404(
        id, description="The submission business with specified ID wasn't found"
    )
    _try_delete_submission_business(submission_business)
    current_app.event_service.handle_submission_event(
        SubmissionEvent.BUSINESS_REMOVED_FROM_SUBMISSION, submission, submission_business
    )
    return None, 204


def add_submission_business(submission_id: str, body: str):
    submission_id = UUID(submission_id)
    submission = Submission.query.get_or_404(submission_id)
    if submission.is_frozen:
        flask.abort(422, "The submission is quoted and cannot be modified")
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission_id):
        flask.abort(403)
    submission_business = SubmissionBusinessSchema().load(body)
    submission_business.submission_id = submission_id
    db.session.add(submission_business)
    db.session.commit()
    current_app.event_service.handle_submission_event(
        SubmissionEvent.BUSINESS_ADDED_TO_SUBMISSION, submission, submission_business
    )
    return SubmissionBusinessSchema().dump(submission_business), 201


def append_loss(submission_id: str, body: dict):
    submission = get_light_submission(submission_id)
    if not current_user.is_internal_machine_user:
        if submission.is_frozen:
            flask.abort(422, "The submission is quoted and cannot be modified")
        if not current_user.has_submission_permission(PermissionType.EDITOR, submission.id):
            flask.abort(403)
    loss = loss_schema.load(body)
    loss.submission_id = submission_id
    loss.organization_id = submission.user.organization_id
    db.session.add(loss)
    try:
        db.session.commit()
        return loss_schema.dump(loss)
    except IntegrityError as e:
        db.session.rollback()
        if isinstance(e.orig, psycopg2.errors.UniqueViolation):
            raise Conflict
        raise e


def __get_merged_loss_query(loss: Loss):
    merged_losses_query = Loss.query.filter(
        Loss.submission_id == loss.submission_id,
        Loss.claim_number == loss.claim_number,
        Loss.is_manual == loss.is_manual,
    )
    merged_losses_query = (
        merged_losses_query.filter(Loss.carrier == None)
        if loss.carrier is None
        else merged_losses_query.filter(loss_formatted_carrier == re.sub(CARRIER_REGEX, "", loss.carrier))
    )

    return merged_losses_query


def update_loss(id: str, body: dict):
    instance = Loss.query.get_or_404(id, description="The Loss with specified ID wasn't found")
    submission = get_light_submission(instance.submission_id)
    if not current_user.is_internal_machine_user:
        if submission.is_frozen:
            flask.abort(422, "The submission is quoted and cannot be modified")
        if not current_user.has_submission_permission(PermissionType.EDITOR, submission.id):
            flask.abort(403)

    base_query = __get_merged_loss_query(instance)
    merged_losses = base_query.count()
    if merged_losses > 1:
        db.session.expunge(instance)
        make_transient(instance)
        loss = loss_schema.load(body, instance=instance, partial=True)

        new_loss_id = uuid4()
        loss.id = new_loss_id
        loss.merged_parent_id = None
        loss.partial_hash = generate_partial_hash(loss)
        loss.full_hash = generate_full_hash(loss)
        db.session.add(loss)
        original_losses = base_query.filter(Loss.id != new_loss_id).all()
        for ml in original_losses:
            ml.merged_parent_id = loss.id
            db.session.add(ml)
    else:
        loss = loss_schema.load(body, instance=instance, partial=True)
        loss.partial_hash = generate_partial_hash(loss)
        loss.full_hash = generate_full_hash(loss)
        db.session.add(loss)
    try:
        db.session.commit()
        return loss_schema.dump(loss)
    except IntegrityError as e:
        db.session.rollback()
        if isinstance(e.orig, psycopg2.errors.UniqueViolation):
            raise Conflict
        raise e


def update_loss_lob_bulk(submission_id: str, body: dict) -> tuple[None, int]:
    if not current_user.is_internal_machine_user:
        raise Forbidden

    request: UpdateLossLobBulkRequest = update_loss_bulk_request_schema.load(body)
    log = logger.bind(request=request)
    if len(request.loss_ids) != len(request.lobs):
        log.warning("Length of loss_ids does not match length of raw_lobs")
        raise BadRequest

    num_requiring_updates: int = len(request.loss_ids)

    lob_by_loss_id: dict[UUID, str | None] = {
        request.loss_ids[index]: request.lobs[index] for index in range(num_requiring_updates)
    }
    type_by_loss_id: dict[UUID, str | None] = {
        request.loss_ids[index]: request.types[index] if len(request.types) > index else None
        for index in range(num_requiring_updates)
    }
    log.info(
        "Updating loss LOBs",
        num_requiring_updates=num_requiring_updates,
        lob_by_loss_id=lob_by_loss_id,
        type_by_loss_id=type_by_loss_id,
    )

    losses: list[Loss] = (
        db.session.query(Loss).filter(Loss.submission_id == submission_id).filter(Loss.id.in_(request.loss_ids)).all()
    )
    if len(losses) != num_requiring_updates:
        [str(loss.id) for loss in losses if loss.id not in lob_by_loss_id]
        log.warning("Some losses were not found", request=request)
        raise NotFound

    for loss in losses:
        loss.line_of_business = lob_by_loss_id[loss.id]
        loss.loss_type = type_by_loss_id[loss.id]
        loss.full_hash = generate_full_hash(loss)

    db.session.commit()

    return None, 200


def delete_loss(id: str):
    loss = Loss.query.get_or_404(id, description="The Loss with specified ID wasn't found")
    submission = get_light_submission(loss.submission_id)
    if not current_user.is_internal_machine_user:
        if submission.is_frozen:
            flask.abort(422, "The submission is quoted and cannot be modified")
        if not current_user.has_submission_permission(PermissionType.EDITOR, submission.id):
            flask.abort(403)

    merged_loss_query = __get_merged_loss_query(loss)
    merged_losses = merged_loss_query.all()
    for merged_loss in merged_losses:
        db.session.delete(merged_loss)

    db.session.delete(loss)
    db.session.commit()
    return None, 204


def __get_distinct_concatenated_field(column):
    return func.array_to_string(func.array_agg(func.distinct(column)), " | ")


def __get_distinct_concatenated_trimmed_field(column):
    return func.array_to_string(func.array_agg(func.distinct(func.trim(func.upper(column)))), " | ")


def get_loss_lob_inference_requirements(submission_id: str, file_id: str | None = None) -> list[dict]:
    SubmissionDAO.get_minimal_submission_or_404(submission_id)
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission_id):
        flask.abort(403)
    loss_runs_enabled = (
        current_user.applicable_settings is not None and current_user.applicable_settings.loss_runs_enabled
    )
    if not loss_runs_enabled and not current_user.is_internal_machine_user:
        return []

    requirements_query = (
        db.session.query(
            Loss.id,
            Loss.carrier,
            Loss.original_line_of_business,
            func.coalesce(Loss.loss_type_raw, Loss.loss_type).label("loss_type_raw"),
            Loss.claim_description,
        )
        .filter(Loss.submission_id == submission_id)
        .filter(and_(Loss.carrier != None, Loss.original_line_of_business != None, Loss.claim_description != None))
        .filter(or_(Loss.line_of_business == None, Loss.line_of_business == "UNKNOWN"))
    )
    if file_id is not None:
        requirements_query = requirements_query.filter(Loss.file_id == file_id)

    results: list[tuple] = requirements_query.all()
    requirements: list[LossLobInferenceRequirement] = [LossLobInferenceRequirement(*result) for result in results]

    return loss_lob_inference_requirement_schema.dump(requirements, many=True)


def get_losses(
    submission_id: str,
    year: str | None = None,
    line_of_business: str | None = None,
    page: int | None = None,
    per_page: int | None = None,
    filter_from_partially_extracted_files: bool | None = True,
    filter_from_failed_files: bool | None = True,
    file_id: str | None = None,
) -> dict:
    submission = get_light_submission(submission_id)
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission.id):
        flask.abort(403)
    loss_runs_enabled = (
        current_user.applicable_settings is not None and current_user.applicable_settings.loss_runs_enabled
    )
    if (
        not loss_runs_enabled and not current_user.is_internal_machine_user
    ):  # need this so behave passes w/ loss runs off
        query = (
            db.session.query(Loss)
            .filter(Loss.submission_id == submission_id, Loss.is_manual.is_(True))
            .order_by(asc(Loss.claim_number))
        )
    else:
        query = (
            db.session.query(
                func.min(cast(Loss.id, TEXT)).label("id"),
                func.max(Loss.created_at).label("created_at"),
                func.max(Loss.updated_at).label("updated_at"),
                func.min(cast(Loss.coverage_id, TEXT)).label("coverage_id"),
                func.min(cast(Loss.submission_business_id, TEXT)).label("submission_business_id"),
                func.min(cast(Loss.submission_id, TEXT)).label("submission_id"),
                func.min(Loss.policy_id).label("policy_id"),
                func.min(cast(Loss.loss_policy_id, TEXT)).label("loss_policy_id"),
                func.min(Loss.loss_type).label("loss_type"),
                func.min(Loss.loss_type_raw).label("loss_type_raw"),
                Loss.claim_number,
                func.min(Loss.organization_id).label("organization_id"),
                __get_distinct_concatenated_field(Loss.insured_name).label("insured_name"),
                __get_distinct_concatenated_field(Loss.claimant_name).label("claimant_name"),
                func.max(Loss.loss_date).label("loss_date"),
                func.max(Loss.report_date).label("report_date"),
                func.max(Loss.claim_date).label("claim_date"),
                func.max(Loss.policy_effective_date).label("policy_effective_date"),
                func.max(Loss.exposure_close_date).label("exposure_close_date"),
                __get_distinct_concatenated_field(Loss.loss_address).label("loss_address"),
                __get_distinct_concatenated_field(Loss.claim_description).label("claim_description"),
                __get_distinct_concatenated_field(Loss.line_of_business).label("line_of_business"),
                __get_distinct_concatenated_field(Loss.lob_raw).label("lob_raw"),
                __get_distinct_concatenated_trimmed_field(Loss.original_line_of_business).label(
                    "original_line_of_business"
                ),
                func.sum(Loss.sum_of_loss_reserve).label("sum_of_loss_reserve"),
                func.sum(Loss.sum_of_alae_reserve).label("sum_of_alae_reserve"),
                func.sum(Loss.sum_of_net_paid_loss).label("sum_of_net_paid_loss"),
                func.sum(Loss.sum_of_net_paid_alae).label("sum_of_net_paid_alae"),
                func.sum(Loss.sum_of_net_outstanding_alae).label("sum_of_net_outstanding_alae"),
                func.sum(Loss.sum_of_net_outstanding_loss).label("sum_of_net_outstanding_loss"),
                func.sum(Loss.sum_of_total_net_incurred).label("sum_of_total_net_incurred"),
                func.min(Coverage.name).label("coverage_rollup"),
                case((any_(func.array_agg(Loss.claim_status)) == "CLOSED", "CLOSED"), else_="OPEN"),
                func.min(Loss.claim_status).label("claim_status"),
                func.sum(Loss.total_paid).label("total_paid"),
                func.max(Loss.policy_expiration_date).label("policy_expiration_date"),
                func.sum(Loss.net_of_deductible).label("net_of_deductible"),
                func.sum(Loss.recoveries).label("recoveries"),
                func.min(Loss.ligitation_status).label("ligitation_status"),
                loss_formatted_carrier,
                func.min(cast(Loss.file_id, TEXT)).label("file_id"),
                func.min(Loss.coverage_type).label("coverage_type"),
                func.max(Loss.report_generated_date).label("report_generated_date"),
                Loss.is_manual,
                Loss.evidence,
                func.array_agg(
                    func.json_build_object(
                        "id",
                        File.id,
                        "name",
                        File.name,
                    )
                )
                .filter(File.is_internal.is_not(True))
                .label("source_files"),
            )
            .filter(Loss.submission_id == submission_id)
            .filter(Loss.is_duplicate.is_not(True))
            .filter(
                and_(
                    Loss.claim_number.is_not(None),
                    Loss.claim_date.is_not(None),
                    Loss.loss_date.is_not(None),
                    Loss.merged_parent_id.is_(None),
                )
            )
            .join(File, File.id == Loss.file_id, isouter=True)
            .join(Coverage, Coverage.id == Loss.coverage_id, isouter=True)
            .group_by(*default_loss_grouping)
            .order_by(asc(Loss.claim_number))
        )
    if filter_from_partially_extracted_files or filter_from_failed_files:
        if filter_from_failed_files:
            query = query.filter(or_(Loss.is_manual.is_(True), File.sensible_status != SensibleStatus.INVALID))
        if filter_from_partially_extracted_files:
            query = query.filter(
                or_(Loss.is_manual.is_(True), File.sensible_status != SensibleStatus.PARTIALLY_COMPLETE)
            )
    if year:
        query = query.filter(extract("year", Loss.loss_date) == year)
    if line_of_business:
        query = query.filter(Loss.line_of_business == line_of_business)
    if file_id:
        query = query.filter(Loss.file_id == file_id)

    if page and per_page:
        losses_page = quicker_paginate(query, page, per_page)
        losses_envelope = LossesEnvelope(
            losses=losses_page.items,
            page=page,
            total_pages=losses_page.pages,
            total_items=losses_page.total,
            has_next=losses_page.has_next,
        )
    else:
        losses = query.all()
        losses_envelope = LossesEnvelope(
            losses=losses,
            page=1,
            total_pages=1,
            total_items=len(losses),
            has_next=False,
        )

    return loss_envelope_schema.dump(losses_envelope)


def put_loss_policy(submission_id: str, body: dict) -> tuple[dict, int]:
    organization_id: str | None = body.get("organization_id", None)
    if not current_user.is_internal_machine_user and current_user.organization_id != organization_id:
        flask.abort(HTTPStatus.FORBIDDEN)

    SubmissionDAO.get_minimal_submission_or_404(submission_id)

    if not body.get("number", None):
        body["number"] = "UNKNOWN"

    loss_policy_id: str | None = body.get("id", None)
    is_update: bool = loss_policy_id is not None
    loss_policy: LossPolicy
    existing_loss_policy: LossPolicy
    if is_update:
        existing_loss_policy = (
            db.session.query(LossPolicy)
            .filter(LossPolicy.id == loss_policy_id)
            .filter(LossPolicy.organization_id == organization_id)
            .filter(LossPolicy.submission_id == submission_id)
            .first_or_404()
        )
        loss_policy = loss_policy_schema.load(body, instance=existing_loss_policy)
    else:
        body["id"] = str(uuid4())
        body["submission_id"] = submission_id
        loss_policy = loss_policy_schema.load(body)

    if not is_update or (body.get("raw_line_of_business") != existing_loss_policy.raw_line_of_business):
        normalize_loss_policy_lob_fields(loss_policy)

    db.session.add(loss_policy)

    if duplicate := get_loss_policy_duplicate(loss_policy):
        error_message: str = "Duplicate loss_policy detected"
        logger.warning(error_message, requested_loss_policy=loss_policy, duplicate_loss_policy=duplicate)

        db.session.rollback()

        raise Conflict(error_message)

    db.session.commit()

    if not is_update:
        logger.info("Created policy from put_loss_policy", loss_policy=loss_policy)

    return loss_policy_schema.dump(loss_policy), HTTPStatus.OK if is_update else HTTPStatus.CREATED


def get_loss_policies(submission_id: str) -> dict:
    SubmissionDAO.get_minimal_submission_or_404(submission_id)
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission_id):
        flask.abort(403)
    loss_runs_enabled = (
        current_user.applicable_settings is not None and current_user.applicable_settings.loss_runs_enabled
    )
    if not loss_runs_enabled and not current_user.is_internal_machine_user:
        return []

    loss_policies = db.session.query(LossPolicy).filter(LossPolicy.submission_id == submission_id).all()

    return loss_policy_schema.dump(loss_policies, many=True)


def get_losses_summary(submission_id: str) -> dict:
    submission = get_light_submission(submission_id)
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission.id):
        flask.abort(403)
    loss_runs_enabled = (
        current_user.applicable_settings is not None and current_user.applicable_settings.loss_runs_enabled
    )
    if (
        not loss_runs_enabled and not current_user.is_internal_machine_user
    ):  # need this so behave passes w/ loss runs off
        summary_of_losses = SummaryOfLosses(
            number_of_claims=0,
            total_loss_value=0.0,
            carrier_groupings=[],
            lob_groupings=[],
            location_groupings=[],
            year_groupings=[],
        )
        return summary_of_losses_schema.dump(summary_of_losses)

    required_fields = [
        "loss_date",
        "sum_of_total_net_incurred",
        "carrier",
        "line_of_business",
        "original_line_of_business",
        "loss_address",
        "claim_number",
    ]
    query = (
        db.session.query(Loss)
        .filter(Loss.submission_id == submission_id)
        .join(File, File.id == Loss.file_id)
        .filter(File.sensible_status.not_in([SensibleStatus.INVALID, SensibleStatus.PARTIALLY_COMPLETE]))
        .filter(Loss.is_duplicate.is_not(True))
        .filter(
            and_(
                Loss.claim_number.is_not(None),
                Loss.claim_date.is_not(None),
                Loss.loss_date.is_not(None),
                Loss.merged_parent_id.is_(None),
            )
        )
        .options(load_only(*required_fields))
    )
    loss_runs = query.all()
    summary_of_losses = _summarize_loss_runs(loss_runs)

    return summary_of_losses_schema.dump(summary_of_losses)


def _summarize_loss_runs(loss_runs: list[LossSchema]) -> SummaryOfLosses:
    carrier_groupings_dict: defaultdict[str, float] = defaultdict(float)
    lob_groupings_dict: defaultdict[str, float] = defaultdict(float)
    location_groupings_dict: defaultdict[str, float] = defaultdict(float)
    year_groupings_dict: defaultdict[str, float] = defaultdict(float)
    unique_loss_runs: set[tuple[str, str]] = set()

    total_loss_value: float = 0
    unique_loss_run_count: int = len(loss_runs)
    for loss_run in loss_runs:
        carrier = re.sub(r"\d", "", loss_run.carrier).rstrip() if loss_run.carrier else None
        if (loss_run.claim_number, carrier) in unique_loss_runs:
            unique_loss_run_count -= 1
        else:
            unique_loss_runs.add((loss_run.claim_number, carrier))

        loss_value: float = loss_run.sum_of_total_net_incurred
        total_loss_value += loss_value

        carrier_grouping_key: str = carrier if carrier else _SUMMARY_OF_LOSSES_UNKNOWN_FIELD
        carrier_groupings_dict[carrier_grouping_key] += loss_value

        lob_grouping_key: str = __get_lob_str_value(loss_run)
        lob_groupings_dict[lob_grouping_key] += loss_value

        location_grouping_key: str = (
            loss_run.loss_address if loss_run.loss_address else _SUMMARY_OF_LOSSES_UNKNOWN_FIELD
        )
        location_groupings_dict[location_grouping_key] += loss_value

        year_grouping_key: str = (
            str(loss_run.loss_date.year)
            if loss_run.loss_date and str(loss_run.loss_date.year)
            else _SUMMARY_OF_LOSSES_UNKNOWN_FIELD
        )
        year_groupings_dict[year_grouping_key] += loss_value

    return SummaryOfLosses(
        number_of_claims=unique_loss_run_count,
        total_loss_value=total_loss_value,
        carrier_groupings=__map_dict_to_summary_of_losses_grouping(carrier_groupings_dict),
        lob_groupings=__map_dict_to_summary_of_losses_grouping(lob_groupings_dict),
        location_groupings=__map_dict_to_summary_of_losses_grouping(location_groupings_dict),
        year_groupings=__map_dict_to_summary_of_losses_grouping(year_groupings_dict),
    )


def __get_lob_str_value(loss_run: LossSchema) -> str:
    if loss_run.line_of_business:
        return loss_run.line_of_business.value
    elif loss_run.original_line_of_business:
        return loss_run.original_line_of_business.strip().title()
    else:
        return LineOfBusinessType.UNKNOWN.value


def __map_dict_to_summary_of_losses_grouping(groupings_dict: dict[str, float]) -> list[SummaryOfLossesGrouping]:
    return list(
        map(
            lambda grouping: SummaryOfLossesGrouping(group_name=grouping[0], group_value=grouping[1]),
            groupings_dict.items(),
        )
    )


def invoke_loss_run_processing(submission_id: str):
    # Triggers processing of all CLASSIFIED loss runs
    submission = get_submission_or_404(submission_id)
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission.id):
        flask.abort(403)
    loss_runs_enabled = (
        current_user.applicable_settings is not None and current_user.applicable_settings.loss_runs_enabled
    )
    loss_runs_manual = (
        current_user.applicable_settings is not None and current_user.applicable_settings.loss_runs_manual
    )

    if (not loss_runs_enabled or not loss_runs_manual) and not current_user.is_internal_machine_user:
        return None, 200

    cache_key: str = get_manual_processing_cache_key(submission_id)
    if manual_processing_cache_exists(cache_key):
        if hasattr(current_user, "id"):
            try_add_notifyee_to_manual_processing_cache(cache_key, current_user.id)
        logger.info(
            "Loss run processing was requested for a submission that is already processing loss runs",
            submission_id=submission_id,
        )
        return None, 200

    files_to_upload = get_loss_run_files_to_upload(submission.files)

    file_ids_to_process = {str(file.id) for file in files_to_upload}
    if hasattr(current_user, "id"):
        add_manual_processing_cache_entry(
            cache_key, users_to_notify={current_user.id}, file_ids_to_process=file_ids_to_process
        )

    invoke_loss_run_processing_internal(submission, files_to_upload)

    return None, 200


def process_loss_run_files(submission_id: str):
    # Reprocesses loss run pdfs
    submission = get_submission_or_404(submission_id)
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission.id):
        flask.abort(403)
    loss_runs_enabled = (
        current_user.applicable_settings is not None and current_user.applicable_settings.loss_runs_enabled
    )
    if loss_runs_enabled:
        files_to_upload = [
            file
            for file in submission.files
            if file.file_type == FileType.LOSS_RUN
            and file.name.lower().endswith(".pdf")
            and not (file.name and "_pages_" in file.name.lower())
        ]

        for file in files_to_upload:
            file.sensible_status = None
            file.processing_state = FileProcessingState.PROCESSING
            File.query.filter(File.parent_file_id == file.id).delete()
            Loss.query.filter(Loss.file_id == file.id).delete()
            db.session.commit()
            logger.info(
                "Starting copilot-workers process file (Loss Run PDF)",
                file_id=str(file.id),
                submission_id=str(submission.id),
            )
            flask.current_app.workflows_client.invoke_copilot_workers_process_file(submission.id, file.id)
    return None, 200


def identify_first_party_fields(body: dict):
    fields = first_party_field_schema.load(body, many=True)
    logger.error("This endpoint should not be called", issue_ping="dawid")
    return {"first_party_fields": first_party_field_schema.dump(fields, many=True)}


def get_submission_history(submission_id: str, action_type: SubmissionActionType | None = None):
    submission = get_light_submission(submission_id)
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission.id):
        flask.abort(403)
    submission_history_schema = SubmissionHistorySchema()
    if action_type:
        history = load_submission_history(submission_id, action_type=action_type, parent_type=None)
    else:
        history = load_submission_history(submission_id)

    return {"submission_history": submission_history_schema.dump(history, many=True)}


def create_quality_audit_question(submission_id: str, body: dict):
    if not current_user.can_audit:
        flask.abort(403)
    SubmissionDAO.get_minimal_submission_or_404(submission_id)

    quality_audit_question_schema = QualityAuditQuestionSchema()
    new_question: QualityAuditQuestion = quality_audit_question_schema.load(body)
    db.session.add(new_question)
    db.session.commit()
    return quality_audit_question_schema.dump(new_question), 201


def get_quality_audit_questions(submission_id: str):
    if not current_user.can_audit:
        flask.abort(403)
    SubmissionDAO.get_minimal_submission_or_404(submission_id)
    questions = QualityAuditQuestion.query.filter_by(submission_id=submission_id).all()
    return QualityAuditQuestionSchema().dump(questions, many=True)


def delete_fact_subtype(submission_id: str, fact_subtype_id: str):
    submission = get_light_submission(submission_id)
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission.id):
        flask.abort(403)
    try:
        current_app.facts_client.delete_fact_subtype(submission_id, fact_subtype_id)
    except Exception as e:
        logger.exception("Cannot remove fact subtype id", fact_subtype_id=fact_subtype_id, submission_id=submission_id)
        raise ValidationError(f"Cannot delete this fact subtype - {e}")
    return None, 204


def add_client_submission_id(submission_id: str, body: dict):
    submission = _get_submission_or_raise_if_missing_or_not_authorized(submission_id)
    submission_client_id = SubmissionClientIdSchema().load(body)
    _process_and_add_new_sub_client_id(submission, submission_client_id)
    return SubmissionClientIdSchema().dump(submission_client_id), 201


def bulk_add_client_submission_ids(submission_id: str, body: dict):
    submission = _get_submission_or_raise_if_missing_or_not_authorized(submission_id)
    submission_client_ids = SubmissionClientIdSchema(many=True).load(body)

    if submission.report.organization_id == ExistingOrganizations.Paragon.value:
        process_and_add_new_sub_clients_ids_for_paragon(submission, submission_client_ids)
    else:
        for submission_client_id in submission_client_ids:
            _process_and_add_new_sub_client_id(submission, submission_client_id)

    return SubmissionClientIdSchema(many=True).dump(submission_client_ids), 201


def process_and_add_new_sub_clients_ids_for_paragon(
    submission: Submission, submission_client_ids: list[SubmissionClientId]
) -> None:
    _verify_paragon_sub_has_no_control_numbers(submission)
    _verify_incoming_paragon_client_ids_are_numbers(submission_client_ids)

    paragon_ims_client: ParagonIMSAPIClient | None = get_logged_in_paragon_ims_client()
    if not paragon_ims_client:
        logger.error("Paragon IMS API is disabled in the AWS secret but in CAPI it must be enabled all the time.")
        flask.abort(500, "Paragon IMS API is disabled in the AWS secret but in CAPI it must be enabled all the time")

    ims_sub_info_map: dict[SubmissionClientId, SubmissionInformation] = {}
    for client_id in submission_client_ids:
        control_number = int(client_id.client_submission_id)
        ims_sub_info: SubmissionInformation = paragon_ims_client.get_submission_information(control_number)
        if not ims_sub_info:
            flask.abort(400, f"Paragon IMS has no data for control number {control_number}")
        ims_sub_info_map[client_id] = ims_sub_info

        if ims_sub_info.lob not in IMS_COVERAGE_TO_COPILOT_PROGRAM_MAPPING:
            flask.abort(
                400, f"This control number is associated with {ims_sub_info.lob} which is unsupported for this program"
            )

        if IMS_COVERAGE_TO_COPILOT_PROGRAM_MAPPING[ims_sub_info.lob] != submission.organization_group:
            flask.abort(
                400,
                (
                    f"This control number is associated with {ims_sub_info.lob}, but this Copilot submission is for"
                    f" {submission.organization_group}"
                ),
            )

        mapped_coverage = IMS_TO_COPILOT_COVERAGE_MAPPING[ims_sub_info.lob]
        is_different_coverage = all(
            coverage.coverage_type != mapped_coverage.type or coverage.coverage.name != mapped_coverage.name
            for coverage in submission.coverages
        )

        if is_different_coverage:
            flask.abort(400, "This submission has different coverage than the one in Paragon IMS")

    # Anything that is Paragon but not E&S with currently 2 coverages (GL + XS)
    # must have exactly 1 control number; we can skip all other incoming numbers
    if not has_one_primary_and_one_excess_liabilities(submission):
        _process_and_add_new_sub_client_id(submission, submission_client_ids[0])
        return

    split_subs, _ = _split_paragon_excess_and_surplus_sub_without_ims_correlations(submission)
    if not split_subs or len(split_subs["submission_ids"]) != 2:
        logger.error("Cannot split Paragon submission", split_subs=split_subs)
        flask.abort(500, "Cannot split Paragon submission")

    gl_sub = _get_submission_or_raise_if_missing_or_not_authorized(split_subs["submission_ids"][0])
    xs_sub = _get_submission_or_raise_if_missing_or_not_authorized(split_subs["submission_ids"][1])

    for client_id in ims_sub_info_map.keys():
        control_number = int(client_id.client_submission_id)
        ims_sub_info = ims_sub_info_map[client_id]

        if ims_sub_info.lob == ImsCoverage.CASUALTY_PRIMARY:
            target_sub = gl_sub
        elif ims_sub_info.lob == ImsCoverage.CASUALTY_EXCESS_SUPPORTED:
            target_sub = xs_sub
        else:
            logger.error(
                "Unsupported Paragon IMS LOB for E&S split",
                control_number=control_number,
                lob=ims_sub_info.lob,
            )
            continue

        client_id.submission_id = target_sub.id
        _process_and_add_new_sub_client_id(target_sub, client_id)


def _verify_paragon_sub_has_no_control_numbers(submission: Submission) -> None:
    # Incremental adding of control numbers to Paragon submission is not supported.
    # Existing control number must be deleted first to allow to add(replace into) new one.
    existing_client_id = SubmissionClientId.query.filter_by(submission_id=submission.id).first()
    if existing_client_id:
        flask.abort(409, "Paragon submission already has client ID assigned")


def _verify_incoming_paragon_client_ids_are_numbers(submission_client_ids: list[SubmissionClientId]) -> None:
    # Validate if incoming client IDs are integers (Paragon control numbers)
    try:
        _control_numbers = [int(item.client_submission_id) for item in submission_client_ids]
    except ValueError as e:
        logger.warning(f"Error parsing Paragon client ID to integer (as control number): {e}")
        flask.abort(400, "Paragon client IDs must be integers")


def _get_submission_or_raise_if_missing_or_not_authorized(submission_id: str) -> Submission:
    submission_id = UUID(submission_id)
    if not (
        current_user.is_internal_machine_user
        or current_user.has_submission_permission(PermissionType.EDITOR, submission_id)
    ):
        flask.abort(403)
    submission: Submission = SubmissionDAO.get_minimal_submission(
        submission_id,
        raise_404=True,
        include_report=True,
        include_coverages=True,
        coverages_additional_fields=[
            "coverage_id",
            "coverage_type",
            "source",
            "source_details",
            "estimated_premium",
            "quoted_premium",
            "bound_premium",
            "total_premium",
        ],
        report_additional_fields=["organization_id", "shadow_type"],
    )
    return submission


def _process_and_add_new_sub_client_id(submission: Submission, submission_client_id: SubmissionClientId) -> None:
    if submission.report.organization_id == ExistingOrganizations.AdmiralInsuranceGroup.value:
        submission_client_id.client_submission_id = submission_client_id.client_submission_id.replace(" ", "")

    existing_client_id = SubmissionClientId.query.filter_by(
        submission_id=submission.id, client_submission_id=submission_client_id.client_submission_id
    ).first()

    if existing_client_id:
        flask.abort(409, "Client ID already assigned to this submission")
    if submission.report.organization_id in set(get_sync_config_map().keys()).difference(RESYNC_EXCLUDE):
        cleanup_after_manual_client_id_assignment(
            submission_client_id.client_submission_id, submission.report.organization_id
        )

    db.session.add(submission_client_id)
    if submission.report.shadow_type == ReportShadowType.HAS_ACTIVE_SHADOW:
        shadow_report_id = ReportDAO.get_shadow_report_id(submission.report.id)
        shadow_submission = SubmissionDAO.get_minimal_submission(report_id=shadow_report_id)
        shadow_submission_client_id = SubmissionClientId(
            submission_id=shadow_submission.id,
            client_submission_id=submission_client_id.client_submission_id,
            source=submission_client_id.source,
        )
        db.session.add(shadow_submission_client_id)

    db.session.commit()


def delete_client_submission_id(submission_id: str, client_submission_id: str) -> tuple[dict | None, int]:
    submission_id = UUID(submission_id)
    submission = SubmissionDAO.get_minimal_submission(submission_id, report_additional_fields=["organization_id"])
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission_id):
        flask.abort(403)

    search_by_contains = submission.report.organization_id == ExistingOrganizations.Paragon.value
    was_deleted: bool = SubmissionClientIdDAO.delete_submission_client_id(
        submission_id=submission_id,
        submission_client_id=client_submission_id,
        organization_id=submission.report.organization_id,
        search_by_contains=search_by_contains,
    )
    if was_deleted:
        db.session.commit()
    return None, 204


def get_client_submission_id(client_submission_id: str, organization_id: int):
    submission_client_id = (
        SubmissionClientId.query.filter_by(client_submission_id=client_submission_id)
        .join(Submission, SubmissionClientId.submission_id == Submission.id)
        .join(User, Submission.owner_id == User.id)
        .filter(User.organization_id == organization_id)
        .order_by(SubmissionClientId.created_at.desc())
        .first()
    )
    if not submission_client_id:
        flask.abort(404)
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission_client_id.submission_id):
        flask.abort(403)
    return SubmissionClientIdSchema().dump(submission_client_id), 200


def expire_submissions():
    with no_expire_on_commit():
        _expire_submissions()
        _expire_quoted_submissions()


def _expire_quoted_submissions():
    if not current_user.is_internal_machine_user:
        flask.abort(403)
    threshold = datetime.utcnow() - relativedelta(months=6)
    orgs_to_expire = [ExistingOrganizations.AdmiralInsuranceGroup.value]

    # Move quoted submissions that were not bound to lost.
    query = db.session.query(Submission).join(Submission.report).filter(ReportV2.organization_id.in_(orgs_to_expire))
    query = query.filter(ReportV2.created_at < threshold, Submission.stage == SubmissionStage.QUOTED)
    query = query.options(
        load_only(Submission.stage, Submission.id),
        lazyload(Submission.assigned_underwriters),
        joinedload(Submission.report).load_only(ReportV2.organization_id).lazyload(ReportV2.report_bundle),
        selectinload(Submission.businesses),
    )
    quoted_submissions = SubmissionDAO.wrap_with_only_verified(query).limit(500).all()
    logger.info("Found quoted submissions to mark as lost", count=len(quoted_submissions))

    all_ids = {x.id for x in quoted_submissions}
    db.session.query(Submission).filter(Submission.id.in_(all_ids)).update(
        {
            Submission.stage: SubmissionStage.QUOTED_LOST,
            Submission.stage_details: {"skip_transitions_restriction": True},
        },
        synchronize_session="evaluate",
    )
    db.session.commit()

    payload = []
    for sub in quoted_submissions:
        if os.environ.get("INTERACT_WITH_EXTERNAL_RESOURCES", True) is True:
            create_external_resource_snapshots_if_needed(sub, refresh_existing_snapshots=True)
        payload.append(
            StageChangePayload(
                str(sub.id),
                SubmissionStage.QUOTED,
                SubmissionStage.EXPIRED,
                [],
                None,
                current_user.id,
                current_user.email,
                sub.organization_id,
            )
        )
    for chunk in chunks(payload, 60):
        KalepaEventsHandler.send_submission_stage_changed_event_bulk(chunk)

    return None, 204


def _expire_submissions():
    if not current_user.is_internal_machine_user:
        flask.abort(403)
    threshold = datetime.utcnow() - relativedelta(weeks=4)

    stages_not_to_expire = [*list(SubmissionStage.terminal_stages()), SubmissionStage.QUOTED]
    orgs_not_to_expire = [
        ExistingOrganizations.FCCIGroup.value,
        ExistingOrganizations.KalepaAutomatedTests.value,
        ExistingOrganizations.KalepaDemo.value,
        ExistingOrganizations.KalepaNewDemo.value,
        ExistingOrganizations.KalepaMGA.value,
        ExistingOrganizations.CNA.value,
    ]
    org_groups_not_to_expire = [
        OrganizationGroups.CONIFER_CANNABIS_SELECT.value,
    ]

    # allow at least a week in Copilot + 24 hours for loading
    threshold_date = datetime.utcnow() - relativedelta(days=8)
    past_effective = (
        SubmissionDAO.wrap_with_only_verified(
            Submission.query.join(Submission.report)
            .filter(
                Submission.stage.not_in(stages_not_to_expire),
                Submission.proposed_effective_date < threshold,
                ReportV2.organization_id.notin_(orgs_not_to_expire),
                or_(ReportV2.org_group.notin_(org_groups_not_to_expire), ReportV2.org_group.is_(None)),
                or_(ReportV2.created_at < threshold_date, Submission.is_verified_shell.is_(True)),
            )
            .options(
                load_only(Submission.stage, Submission.id),
                lazyload(Submission.assigned_underwriters),
                joinedload(Submission.report).load_only(ReportV2.organization_id).lazyload(ReportV2.report_bundle),
            )
        )
        .limit(100)
        .all()
    )
    logger.info("Found past effective submissions to mark as expired", count=len(past_effective))

    threshold_date = datetime.utcnow() - relativedelta(months=4)
    old = (
        SubmissionDAO.wrap_with_only_verified(
            Submission.query.join(Submission.report)
            .filter(
                Submission.stage.not_in(stages_not_to_expire),
                Submission.proposed_effective_date == None,
                ReportV2.created_at < threshold_date,
                ReportV2.organization_id.notin_(orgs_not_to_expire),
                or_(ReportV2.org_group.notin_(org_groups_not_to_expire), ReportV2.org_group.is_(None)),
            )
            .options(
                load_only(Submission.stage, Submission.id),
                lazyload(Submission.assigned_underwriters),
                joinedload(Submission.report).load_only(ReportV2.organization_id).lazyload(ReportV2.report_bundle),
            )
        )
        .limit(100)
        .all()
    )

    logger.info("Found old submission to mark as expired", count=len(old))

    all_submissions = past_effective + old
    all_ids = {x.id for x in all_submissions}
    submission_id_to_old_stage = {s.id: s.stage for s in all_submissions}
    db.session.query(Submission).filter(Submission.id.in_(all_ids)).update(
        {Submission.stage: SubmissionStage.EXPIRED, Submission.stage_details: {"skip_transitions_restriction": True}},
        synchronize_session=False,
    )
    db.session.commit()
    payload = []

    for sub in all_submissions:
        payload.append(
            StageChangePayload(
                str(sub.id),
                submission_id_to_old_stage[sub.id],
                SubmissionStage.EXPIRED,
                [],
                None,
                current_user.id,
                current_user.email,
                sub.organization_id,
            )
        )
    for chunk in chunks(payload, 60):
        KalepaEventsHandler.send_submission_stage_changed_event_bulk(chunk)

    return None, 204


def store_submission_level_extracted_data(submission_id: str, file_id: str):
    processed_file = (
        ProcessedFile.query.options(joinedload(ProcessedFile.file, innerjoin=True).load_only(File.file_type))
        .filter(ProcessedFile.file_id == file_id)
        .order_by(ProcessedFile.created_at.desc())  # type: ignore
        .first()
    )
    if not processed_file:
        flask.abort(HTTPStatus.NOT_FOUND)
    submission = Submission.query.get_or_404(submission_id)
    extracted_data = extract_submission_level_data(processed_file, submission)
    if records := [
        SubmissionLevelExtractedData(
            submission_id=submission.id, file_id=file_id, field=field, value=json.dumps(value, default=str)
        )
        for field, value in extracted_data.items()
    ]:
        db.session.add_all(records)
        commit_ignore_unique_violation()


def create_or_replace_submission_level_extracted_data(body: dict):
    submission_level_extracted_data = SubmissionLevelExtractedDataSchema().load(body)
    lock_name = (
        f"sled_{submission_level_extracted_data.file_id}_{submission_level_extracted_data.field}_"
        f"{submission_level_extracted_data.source_details}_{submission_level_extracted_data.generation_method}"
    )
    logger.info("Acquiring lock", lock=lock_name)
    try:
        with redis_lock.Lock(current_app.locks_client, name=lock_name, expire=10):
            existing_data = SubmissionLevelExtractedData.query.filter_by(
                submission_id=submission_level_extracted_data.submission_id,
                file_id=submission_level_extracted_data.file_id,
                field=submission_level_extracted_data.field,
                source_details=submission_level_extracted_data.source_details,
                generation_method=submission_level_extracted_data.generation_method,
            ).first()
            to_add = SubmissionLevelExtractedDataSchema().load(body, instance=existing_data)
            db.session.add(to_add)
            db.session.commit()
    except redis_lock.NotAcquired:
        logger.warning("Lock already expired", lock=lock_name)
    return SubmissionLevelExtractedDataSchema().dump(to_add), 201


def _is_just_changing_primary_naics_code(body: dict) -> bool:
    if len(body) == 1 and (
        body.get("primary_naics_code", None) is not None or body.get("is_naics_verified", None) is not None
    ):
        return True
    return False


def _is_just_changing_gl_iso_code(body: dict) -> bool:
    if len(body) == 1 and body.get("iso_gl_code", None) is not None:
        return True
    return False


def _is_just_updating_clearing_assignee(body: dict) -> bool:
    return len(body) == 1 and "clearing_assignee_id" in body


def _is_client_stage_update(body: dict) -> bool:
    if len(body) == 3 and "stage" in body and "client_stage_id" in body and "client_stage_comment" in body:
        return True
    return False


def _is_prevent_clearing_updates_update(body: dict) -> bool:
    if len(body) == 1 and "prevent_clearing_updates" in body:
        return True
    return False


def _is_auditing(body: dict) -> bool:
    if body.get("audited_at", None) is not None:
        return True
    return False


@retry(stop_max_attempt_number=3, wait_fixed=2000)
def _try_delete_submission_business(submission_business: SubmissionBusiness) -> None:
    try:
        db.session.delete(submission_business)
        db.session.commit()
    except:
        logger.warning("Failed to delete submission business", submission_business_id=submission_business.id)
        db.session.rollback()
        raise


def upsert_user_submission_notifications(body: dict, id: str):
    user_id = current_user.id
    submission_id = id

    body["user_id"] = user_id
    body["submission_id"] = submission_id

    existing = UserSubmissionNotification.query.filter_by(user_id=user_id, submission_id=submission_id).first()

    data = UserSubmissionNotificationSchema().load(body, instance=existing)

    db.session.add(data)
    db.session.commit()

    return None


def get_users_submission_notifications(id: str):
    user_id = current_user.id
    submission_id = id

    notifications = UserSubmissionNotification.query.filter_by(user_id=user_id, submission_id=submission_id).first()

    correspondence_id = (
        db.session.query(ReportV2.correspondence_id).join(ReportV2.submission).filter(Submission.id == submission_id)
    )

    email_count = (
        db.session.query(Email)
        .filter(Email.correspondence_id == correspondence_id, Email.is_deleted.isnot(True))
        .count()
    )
    notes_count = db.session.query(SubmissionNote).filter(SubmissionNote.submission_id == submission_id).count()

    return {
        "seen_emails": notifications.seen_emails if notifications else 0,
        "seen_notes": notifications.seen_notes if notifications else 0,
        "email_count": email_count,
        "notes_count": notes_count,
    }


def get_submission_coverage_history(submission_id: str):
    submission = get_light_submission(submission_id)
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission.id):
        flask.abort(403)

    submission_coverage_history_actions = [
        SubmissionActionType.SUBMISSION_COVERAGE_ADDED,
        SubmissionActionType.SUBMISSION_COVERAGE_DELETED,
        SubmissionActionType.SUBMISSION_COVERAGE_UPDATED,
    ]

    submission_history_records = SubmissionHistory.query.filter(
        and_(
            SubmissionHistory.submission_id == submission_id,
            SubmissionHistory.submission_action_type.in_(submission_coverage_history_actions),
        )
    ).all()
    submission_history_schema = SubmissionHistorySchema()

    return {"submission_coverage_history": submission_history_schema.dump(submission_history_records, many=True)}


def get_client_submission_stage_config(organization_id: int | None = None):
    if organization_id is not None and not current_user.is_internal_machine_user:
        flask.abort(403)

    org_id = organization_id or current_user.organization_id

    data = (
        ClientSubmissionStageConfig.query.filter_by(organization_id=org_id)
        .order_by(ClientSubmissionStageConfig.id)
        .all()
    )

    return ClientSubmissionStageConfigSchema().dump(data, many=True)


def get_submission_shareholders(submission_id: str):
    shareholders = (
        db.session.query(Shareholder)
        .join(Shareholder.file)
        .options(contains_eager(Shareholder.file).selectinload(File.parent_file))
        .outerjoin(Shareholder.submission_business)
        .filter(or_(SubmissionBusiness.hide == False, SubmissionBusiness.hide.is_(None)))
        .filter(Shareholder.submission_id == submission_id)
        .filter(File.file_type == FileType.SUPPLEMENTAL_FORM)
        .all()
    )
    shareholders_response = shareholder_schema_many.dump(shareholders)

    return shareholders_response


# GET /api/v3.0/submissions/{submission_id}/files-data
def get_submission_files_data(submission_id: str | UUID) -> tuple[dict, int]:
    submission_id = convert_to_uuid(submission_id)
    submission_files_data = get_submission_data(submission_id)
    response = submission_files_data_schema.dump(submission_files_data)

    return response


def get_submissions_by_client_ids(client_submission_ids: list[str], organization_id: int, verified_only: bool = False):
    if not client_submission_ids:
        return []

    only = {
        "id",
        "created_at",
        "is_verified",
        "client_submission_ids",
        "report_ids",
        "is_renewal",
        "broker_id",
        "broker",
        "businesses",
    }
    columns_to_load = {a.key for a in inspect(Submission).attrs if isinstance(a, ColumnProperty) if a.key in only}
    columns_to_load.add("report_id")

    submissions_query = (
        db.session.query(Submission)
        .join(Submission.client_submission_ids)
        .join(Submission.report)
        .join(
            SubmissionBusiness,
            and_(
                SubmissionBusiness.submission_id == Submission.id,
                SubmissionBusiness.named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
            ),
            isouter=True,
        )
        .filter(ReportV2.organization_id == organization_id)
        .filter(SubmissionClientId.client_submission_id.in_(client_submission_ids))
        .filter(Submission.is_deleted == False)
        .options(
            lazyload(Submission.assigned_underwriters),
            joinedload(Submission.client_submission_ids),
            joinedload(Submission.broker),
            load_only(*adjust_submission_fields(columns_to_load)),
            contains_eager(Submission.businesses),
        )
        .populate_existing()
    )

    if verified_only:
        submissions_query = submissions_query.filter(
            or_(Submission.is_verified == True, Submission.is_verified_shell == True)
        )

    submissions = submissions_query.all()
    deduplicated_subs = {sub.id: sub for sub in submissions}
    return SubmissionSchema(many=True, only=only).dump(list(deduplicated_subs.values()))


# GET /api/v3.0/submissions/{submission_id}/shadow-info
def get_submission_shadow_info(submission_id: str) -> dict:
    submission_id = UUID(submission_id)
    shadow_dependencies = ReportDAO.get_completed_shadow_dependencies(submission_id)

    shadowed_by_submission = None
    if shadow_dependencies:
        query = db.session.query(Submission).options(load_only(Submission.id))
        query = query.filter(
            Submission.report_id.in_([rsd.shadow_report_id for rsd in shadow_dependencies]),
            Submission.is_deleted.is_not(True),
        )
        query = query.options(lazyload(Submission.assigned_underwriters))
        shadowed_by_submission = query.one_or_none()
    return {
        "shadowed_by_submission": {"submission_id": str(shadowed_by_submission.id)} if shadowed_by_submission else None
    }


# PUT /submissions/{submission_id}/identifiers
def set_submission_identifiers(submission_id: str, body: dict):
    submission = get_light_submission(submission_id)
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission.id):
        flask.abort(403)

    identifiers = set_submission_identifiers_req_schema.load(body)

    if not identifiers:
        return None, 204

    try:
        patch_identifiers(submission, identifiers)
        db.session.commit()
    except IntegrityError as e:
        db.session.rollback()
        if isinstance(e.orig, psycopg2.errors.UniqueViolation):  # type: ignore
            logger.warning("Error when updating submission identifiers", exc=e)
        else:
            logger.error("IntegrityError when updating submission identifiers", exc=e)
        flask.abort(409, "Cannot update submission identifiers at this time. Please try again later.")

    current_app.event_service.handle_submission_event(SubmissionEvent.SUBMISSION_UPDATED, submission)

    return None, 204


def delete_submission_identifier(submission_id: str, body: dict):
    submission = get_light_submission(submission_id)
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission.id):
        flask.abort(403)

    identifier = delete_submission_identifier_req_schema.load(body)

    if not identifier:
        return None, 204

    existing_identifier = SubmissionIdentifier.query.filter(
        SubmissionIdentifier.submission_id == submission_id,
        SubmissionIdentifier.identifier_type == identifier.type,
        SubmissionIdentifier.identifier == identifier.identifier,
    ).first()

    if not existing_identifier:
        return None, 204

    db.session.delete(existing_identifier)

    if Organization.is_bishop_conifer_for_id(current_user.organization_id):
        client_ids = submission.client_submission_ids

        for client_id in client_ids:
            SubmissionClientIdDAO.delete_submission_client_id(
                submission_id=submission_id,
                submission_client_id=client_id.client_submission_id,
                organization_id=submission.report.organization_id,
            )

    db.session.commit()

    current_app.event_service.handle_submission_event(SubmissionEvent.SUBMISSION_UPDATED, submission)

    return None, 204


def get_active_organizational_submissions(organization_id: int):
    submissions = SubmissionDAO.get_active_submissions_for_organization(organization_id)
    return {"active_submissions": [{"submission_id": str(sub.id)} for sub in submissions]}


def get_descriptions_of_operations(submission_id: str):
    if not current_user.has_submission_permission(PermissionType.VIEWER, UUID(submission_id)):
        flask.abort(403)

    descriptions = (
        db.session.query(SubmissionLevelExtractedData.value)
        .filter(SubmissionLevelExtractedData.field == EntityInformation.DESCRIPTION.value)
        .filter(SubmissionLevelExtractedData.submission_id == submission_id)
        .filter(SubmissionLevelExtractedData.is_valid.is_(True))
        .all()
    )
    return [json.loads(d[0]) for d in descriptions if d[0]]


# POST /api/v3.0/submissions/{submission_id}/action
def execute_submission_action(submission_id: str, body: dict):
    submission_id = UUID(submission_id)
    log = logger.bind(submission_id=submission_id)
    log.info("Executing submission action", body=body)

    if not current_user.is_internal_machine_user:
        flask.abort(403)

    action: SubmissionAction = PolymorphicSubmissionActionSchema().load(body)
    action_executor = SubmissionActionExecutorFactory.get_executor(action.submission_action_type, submission_id, log)
    result = action_executor.execute(action)

    return result, 200


def _fetch_assigned_client_ids(rows: list, organization_id: int) -> set:
    client_ids = [row["client_id"] for row in rows]
    assigned = SubmissionClientIdDAO.get_submission_client_ids(
        client_submission_ids=client_ids,
        organization_id=organization_id,
        exclude_deleted_reports=True,
        verified_only=True,
    )
    return {aci.client_submission_id for aci in assigned}


# GET /api/v3.0/submissions/{submission_id}/identifier-suggestions
def get_identifier_suggestions(
    submission_id: str,
    as_of: str | None = None,
    exclude_assigned_identifiers: bool = False,
):
    submission = get_light_submission(submission_id)
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission.id):
        flask.abort(403)

    subquery = (
        db.session.query(
            SubmissionIdentifiersSuggestion.id,
            SubmissionIdentifiersSuggestion.submission_sync_id,
            SubmissionSync.policy_number.label("client_id"),
            func.row_number()
            .over(
                partition_by=(
                    SubmissionIdentifiersSuggestion.submission_id,
                    SubmissionIdentifiersSuggestion.submission_sync_id,
                ),
                order_by=desc(SubmissionIdentifiersSuggestion.created_at),
            )
            .label("row_num"),
        )
        .join(SubmissionSync, SubmissionIdentifiersSuggestion.submission_sync_id == SubmissionSync.id)
        .filter(
            SubmissionIdentifiersSuggestion.submission_id == submission_id,
        )
    )

    if as_of:
        created_after = parser.parse(as_of)
        subquery = subquery.filter(SubmissionIdentifiersSuggestion.created_at >= created_after)
    subquery = subquery.subquery()

    rows = db.session.query(subquery).filter(subquery.c.row_num == 1).all()
    assigned_client_ids = _fetch_assigned_client_ids(rows, submission.organization_id)

    if exclude_assigned_identifiers:
        rows = [row for row in rows if row["client_id"] not in assigned_client_ids]

    ids = [row["id"] for row in rows]

    suggestions: list[SubmissionIdentifiersSuggestion] = (
        SubmissionIdentifiersSuggestion.query.filter(SubmissionIdentifiersSuggestion.id.in_(ids))
        .order_by(SubmissionIdentifiersSuggestion.confidence.desc())
        .limit(5)
        .all()
    )

    result = [
        SubmissionIdentifiersSuggestionResponse(
            created_at=s.created_at,
            account_name=s.submission_sync.submission_name,
            client_id=s.submission_sync.policy_number,
            quote_numbers=s.submission_sync.quote_numbers,
            policy_numbers=s.submission_sync.policy_numbers,
            confidence=s.confidence,
            is_assigned=s.submission_sync.policy_number in assigned_client_ids,
        )
        for s in suggestions
    ]

    return SubmissionIdentifiersSuggestionResponseSchema().dump(result, many=True)


def get_submission_level_extracted_data_by_report_id(report_id: str | UUID):
    uuid_report_id = UUID(report_id) if not isinstance(report_id, UUID) else report_id

    if not current_user.has_report_permission(PermissionType.VIEWER, uuid_report_id):
        flask.abort(403)

    submission_id = db.session.query(Submission.id).filter(Submission.report_id == uuid_report_id)

    submission_level_extracted_data = (
        db.session.query(SubmissionLevelExtractedData)
        .filter(SubmissionLevelExtractedData.submission_id == submission_id)
        .all()
    )
    return SubmissionLevelExtractedDataSchema().dump(submission_level_extracted_data, many=True)


def align_submissions_client_stages() -> tuple[dict | None, int]:
    if not current_user.is_internal_machine_user:
        flask.abort(403)
    now = datetime.utcnow().replace(tzinfo=pytz.UTC)
    twenty_four_hours_ago = text("now() - interval '24 hours'")

    submissions_and_stages: list[tuple[Submission, SubmissionStage]] = (
        db.session.query(Submission, ClientSubmissionStageConfig.copilot_stage)
        .join(ClientSubmissionStageConfig, Submission.client_stage_id == ClientSubmissionStageConfig.id)
        .filter(
            or_(Submission.is_verified.is_(True), Submission.is_verified_shell.is_(True)),
            Submission.stage != ClientSubmissionStageConfig.copilot_stage,
            Submission.created_at > twenty_four_hours_ago,
        )
        .all()
    )

    for submission, stage in submissions_and_stages:
        if (
            submission.is_verified_shell
            and submission.is_boss
            and now - submission.created_at.replace(tzinfo=pytz.UTC) < timedelta(hours=NATIONWIDE_ALIGN_STAGES_HOURS)
        ):
            logger.info("Skipping alignment for Nationwide submission", submission_id=submission.id)
            continue
        submission.stage = stage
        db.session.add(submission)
    db.session.commit()

    return None, 204


# GET /api/v3.0/submissions/{id}/premises_data
def get_submission_premises_data(id: str) -> tuple[dict | None, int]:
    submission = get_light_submission(id)

    if not current_user.has_submission_permission(PermissionType.VIEWER, submission.id):
        flask.abort(403)

    submission_premises_data = (
        db.session.query(SubmissionPremises)
        .filter(
            SubmissionPremises.submission_id == submission.id,
            SubmissionPremises.organization_id == submission.organization_id,
        )
        .all()
    )

    return submission_premises_schema.dump(submission_premises_data, many=True), 200
