from uuid import UUID
import re

from flask import current_app
from infrastructure_common.logging import get_logger
from infrastructure_common.utils.error_utils import suppress_exceptions
from sqlalchemy import and_, desc, func
from static_common.enums.emails import EmailType
from static_common.enums.organization import (
    EMAIL_ACCOUNT_PER_ORG,
    ExistingOrganizations,
)
import redis_lock

from copilot.logic.users.underwriter_assigner import (
    BaseUnderwriterAssigner,
    UnderwriterAssignerConfig,
)
from copilot.models import BrokerageEmployee, Submission, User, db
from copilot.models.admiral_uw_mappings import AdmiralAssignmentLog, AdmiralUWMapping
from copilot.models.emails import Email
from copilot.models.reports import ReportShadowDependency, ReportV2, SubmissionUser

logger = get_logger()

CASUALTY_IL_EMAIL = "<EMAIL>"
PILOT_UNDERWRITER_EMAILS = []


class AdmiralUnderwriterAssigner(BaseUnderwriterAssigner):
    FALLBACK_UW_EMAIL = "<EMAIL>"
    _default_config = UnderwriterAssignerConfig(delete_other_assigned=True, already_assigned_error=False)

    def auto_assign_underwriters(
        self, submission: Submission, config: UnderwriterAssignerConfig | None = None
    ) -> list[SubmissionUser]:
        """
        Automatically assigns underwriters to a submission using a round-robin strategy at the producer level.

        Args:
            submission (Submission): The submission to assign underwriters to.
            config (UnderwriterAssignerConfig | None): Configuration for the assignment process.

        Returns:
            list[SubmissionUser]: List of created submission user assignments.
        """
        config = UnderwriterAssignerConfig.determine_config(config, self._default_config)
        log = logger.bind(submission_id=submission.id, broker_id=submission.broker_id, is_renewal=submission.is_renewal)

        if self._was_shadowed_dependency_assigned(submission, log=log):
            # In this case we should just copy UW from the parent submission. It's possible the UW has been changed
            # manually so we need to respect that.
            log.info("Shadowed dependency was already assigned, skipping auto assignment.")
            return []

        existing_assignment = self.find_existing_assignment(submission, log=log)
        if existing_assignment:
            return self.handle_existing_assignment(existing_assignment, submission, config, log=log)

        email_based_assignment = self._try_email_based_assignment(submission, config, log=log)
        if email_based_assignment:
            return email_based_assignment

        uw_mapping, producer_no = self.get_mapping_and_producer(submission, log=log)
        if not uw_mapping or not producer_no:
            log.info(
                "No broker mapping found, falling back to default underwriter",
                fallback_uw_email=self.FALLBACK_UW_EMAIL,
            )
            return self.assign_fallback_underwriter(submission, config, log=log)

        log = log.bind(producer_no=producer_no, broker_email=uw_mapping.broker_email, uw_mapping_id=uw_mapping.id)

        with redis_lock.Lock(
            current_app.locks_client,
            name=f"admiral_uw_assigner_{producer_no}",
            expire=5,
            auto_renewal=True,
        ):
            return self._execute_auto_assign_underwriters(submission, config, producer_no, uw_mapping, log=log)

    def _execute_auto_assign_underwriters(
        self,
        submission: Submission,
        config: UnderwriterAssignerConfig,
        producer_no: str,
        uw_mapping: AdmiralUWMapping,
        *,
        log=logger,
    ) -> list[SubmissionUser]:
        uw_assignments = uw_mapping.renewal_uws if submission.is_renewal else uw_mapping.non_renewal_uws
        uw_emails = [uw.user_email for uw in uw_assignments]

        if not uw_emails:
            log.warning("No underwriters found in mapping, falling back to default underwriter")
            return self.assign_fallback_underwriter(submission, config, log=log)

        log = log.bind(uw_emails=uw_emails, is_renewal=submission.is_renewal)

        selected_uw_id, selected_uw_email = self.get_next_valid_underwriter_id(producer_no, uw_emails, log=log)
        if not selected_uw_id or not selected_uw_email:
            log.warning("No valid underwriters found in rotation, falling back to default underwriter")
            return self.assign_fallback_underwriter(submission, config, log=log)

        log = log.bind(selected_uw_id=selected_uw_id, selected_uw_email=selected_uw_email)

        self.log_assignment(
            submission_id=submission.id,
            producer_no=producer_no,
            broker_email=submission.broker_email,
            uw_email=selected_uw_email,
        )

        db.session.commit()
        log.info("Assigning auto-selected underwriter to Admiral submission")

        return self.assign_underwriters([selected_uw_id], submission, config=config)

    def _was_shadowed_dependency_assigned(self, submission: Submission, *, log=logger) -> bool:
        if submission.is_shadow_processed:
            log.info("Submission is shadow processed, checking also for shadowed report dependencies")
            shadow_parent_assignment = AdmiralAssignmentLog.query.filter(
                AdmiralAssignmentLog.submission_id.in_(
                    Submission.query.join(
                        ReportShadowDependency,
                        and_(
                            ReportShadowDependency.report_id == Submission.report_id,
                            ReportShadowDependency.shadow_report_id == submission.report.id,
                            ReportShadowDependency.is_active.is_(True),
                        ),
                    ).with_entities(Submission.id)
                ),
            ).first()

            if shadow_parent_assignment:
                log.info("Found existing assignment for shadowed dependency")
                return True
        return False

    def find_existing_assignment(self, submission: Submission, *, log=logger) -> AdmiralAssignmentLog | None:
        report: ReportV2 = submission.report
        bundled_reports = set(report.report_bundle.reports) if report.report_bundle else set()
        bundled_reports.discard(report)
        if bundled_reports:
            bundled_submission_ids = {r.submission.id for r in bundled_reports}
            log.info(
                "Submission is part of a bundle, checking for existing assignments in the bundle",
                bundle_size=len(bundled_reports),
                bundled_report_ids=[r.id for r in bundled_reports],
                bundled_submission_ids=bundled_submission_ids,
            )
            bundled_assignment = AdmiralAssignmentLog.query.filter(
                AdmiralAssignmentLog.submission_id.in_(bundled_submission_ids)
            ).first()

            if bundled_assignment:
                log.info("Found existing assignment in bundle", bundled_assignment=bundled_assignment)
                return bundled_assignment

        return AdmiralAssignmentLog.query.filter(AdmiralAssignmentLog.submission_id == submission.id).first()

    def handle_existing_assignment(
        self,
        assignment: AdmiralAssignmentLog,
        submission: Submission,
        config: UnderwriterAssignerConfig,
        *,
        log=logger,
    ) -> list[SubmissionUser]:
        log.info("Found existing assignment for submission", assignment=assignment)
        user_id = self.find_user_id_by_email(assignment.assigned_underwriter_email)
        if user_id:
            return self.assign_underwriters([user_id], submission, config=config)
        log.warning("Previously assigned underwriter no longer exists", uw_email=assignment.assigned_underwriter_email)
        return self.assign_fallback_underwriter(submission, config, log=log)

    def get_mapping_and_producer(self, submission: Submission, *, log=logger) -> (AdmiralUWMapping | None, str | None):
        uw_mapping = None

        if submission.broker_id:
            uw_mapping = self.find_broker_mapping_for_broker_id(submission.broker_id)
            if uw_mapping:
                log.info(
                    "Found ID-overridden broker mapping", uw_mapping_id=uw_mapping.id, broker_id=submission.broker_id
                )

        if not uw_mapping and submission.broker:
            broker_emails = self.extract_broker_emails(submission.broker)
            while not uw_mapping and broker_emails:
                uw_mapping = self.find_broker_mapping_for_broker_email(broker_emails.pop())
            if not uw_mapping:
                log.info(
                    "No broker mapping found, trying brokerage contact",
                    broker_emails=self.extract_broker_emails(submission.broker),
                )

        if not uw_mapping and submission.brokerage_contact:
            if submission.brokerage_contact_email != submission.broker_email:
                brokerage_contact_emails = self.extract_broker_emails(submission.brokerage_contact)
                while not uw_mapping and brokerage_contact_emails:
                    uw_mapping = self.find_broker_mapping_for_broker_email(brokerage_contact_emails.pop())

        if not uw_mapping:
            log.warning("No broker mapping found")
            return None, None

        return uw_mapping, uw_mapping.producer_no

    def get_next_valid_underwriter_id(
        self, producer_no: str, available_uw_emails: list[str], *, log=logger
    ) -> (int | None, str | None):
        sorted_uws = sorted(available_uw_emails)
        start_idx = self.get_starting_index(producer_no, sorted_uws)

        # Try each underwriter in rotation until we find a valid one
        # noinspection PyTypeChecker
        for i in range(len(sorted_uws)):
            idx = (start_idx + i) % len(sorted_uws)
            uw_email = sorted_uws[idx]
            uw_id = self.find_user_id_by_email(uw_email)

            if not uw_id:
                log.error("Underwriter not found in database", uw_email=uw_email)
                continue

            return uw_id, uw_email

        return None, None

    def get_starting_index(self, producer_no: str, available_uws: list[str]) -> int:
        last_assignment = self.find_last_producer_assignment(producer_no)
        if not last_assignment:
            return 0

        try:
            current_idx = available_uws.index(last_assignment.assigned_underwriter_email)
            return (current_idx + 1) % len(available_uws)
        except ValueError:
            return 0

    def find_last_producer_assignment(self, producer_no: str) -> AdmiralAssignmentLog | None:
        return (
            AdmiralAssignmentLog.query.filter(AdmiralAssignmentLog.producer_no == producer_no)
            .order_by(desc(AdmiralAssignmentLog.sequence_number))
            .first()
        )

    @suppress_exceptions()
    def log_assignment(self, submission_id: UUID, producer_no: str, broker_email: str, uw_email: str) -> None:
        log_record = AdmiralAssignmentLog(
            submission_id=submission_id,
            producer_no=producer_no,
            broker_email=broker_email,
            assigned_underwriter_email=uw_email,
        )
        db.session.add(log_record)
        db.session.commit()

    def find_user_id_by_email(self, user_email: str) -> int | None:
        if not user_email:
            return None
        user = User.query.filter(
            and_(
                func.lower(User.email) == user_email.lower().strip(),
                User.organization_id == ExistingOrganizations.AdmiralInsuranceGroup.value,
            )
        ).first()
        if user:
            return user.id
        return None

    def assign_fallback_underwriter(
        self, submission: Submission, config: UnderwriterAssignerConfig | None = None, *, log=logger
    ) -> list[SubmissionUser]:
        fallback_user = self.find_user_id_by_email(self.FALLBACK_UW_EMAIL)
        if not fallback_user:
            log.error("Fallback user not found in the database, will not auto assign any underwriter")
            return []
        return self.assign_underwriters([fallback_user], submission, config=config)

    def find_broker_mapping_for_broker_id(self, broker_id: UUID) -> AdmiralUWMapping | None:
        if not broker_id:
            return None
        return AdmiralUWMapping.query.filter(AdmiralUWMapping.broker_id == broker_id).first()

    def find_broker_mapping_for_broker_email(self, broker_email: str) -> AdmiralUWMapping | None:
        if not broker_email:
            return None
        return AdmiralUWMapping.query.filter(func.lower(AdmiralUWMapping.broker_email) == broker_email.lower()).first()

    def extract_broker_emails(self, broker: BrokerageEmployee) -> list[str]:
        return [broker.email] + [alias for alias in (broker.aliases or []) if "@" in alias]

    def _try_email_based_assignment(
        self, submission: Submission, config: UnderwriterAssignerConfig, *, log=logger
    ) -> list[SubmissionUser] | None:
        if not self._is_new_submission(submission):
            log.info("Submission is follow-up correspondence, skipping email-based assignment")
            return None

        individual_uw_email = self._get_individual_underwriter_from_email(submission, log=log)
        if not individual_uw_email:
            log.info("No underwriter email found, proceeding with normal assignment")
            return None

        if not self._is_pilot_underwriter(individual_uw_email):
            log.info("Underwriter not in pilot list, proceeding with normal assignment", uw_email=individual_uw_email)
            return None

        uw_user_id = self.find_user_id_by_email(individual_uw_email)
        if not uw_user_id:
            log.warning("Underwriter not found in database", uw_email=individual_uw_email)
            return None

        log.info("Assigning submission to individual underwriter from email", uw_email=individual_uw_email)
        return self.assign_underwriters([uw_user_id], submission, config=config)

    def _is_new_submission(self, submission: Submission) -> bool:
        if not submission.report.correspondence_id:
            return True

        email_count = (
            db.session.query(Email)
            .filter(Email.correspondence_id == submission.report.correspondence_id)
            .count()
        )

        return email_count == 1

    def _get_individual_underwriter_from_email(self, submission: Submission, *, log=logger) -> str | None:
        if not submission.report.correspondence_id:
            return None

        admiral_forward_email = EMAIL_ACCOUNT_PER_ORG[ExistingOrganizations.AdmiralInsuranceGroup.value]

        root_email = (
            db.session.query(Email)
            .filter(
                Email.correspondence_id == submission.report.correspondence_id,
                Email.type == EmailType.ROOT
            )
            .first()
        )

        if (
            root_email
            and root_email.email_to
            and CASUALTY_IL_EMAIL.lower() in root_email.email_to.strip().lower()
            and root_email.email_from
            and admiral_forward_email.lower() not in root_email.email_from.strip().lower()
            and "@admiralins.com" in root_email.email_from.strip().lower()
        ):
            from_email = root_email.email_from.strip()
            email_match = re.search(r"<([^>]+)>|^([^\s<>]+@[^\s<>]+)$", from_email)
            if email_match:
                extracted_email = email_match.group(1) or email_match.group(2)
                log.info("Found individual underwriter email", uw_email=extracted_email)
                return extracted_email

        return None

    def _is_pilot_underwriter(self, uw_email: str) -> bool:
        if not PILOT_UNDERWRITER_EMAILS:
            return True
        return uw_email.lower().strip() in [email.lower() for email in PILOT_UNDERWRITER_EMAILS]
