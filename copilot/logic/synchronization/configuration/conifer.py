_DATE_DISTANCE_MAP = {
    "0-14": 0.0,
    "14-28": 0.05,
    "28-42": 0.1,
    "42-60": 0.15,
    "60-80": 0.2,
    "80-100": 0.25,
    "100-120": 0.3,
    "120-180": 0.5,  # we may want the result to appear in close matches
    "180-240": 0.8,
    "240-99999": 1.0,
}

CONIFER_SYNC_CONFIG = {
    "handlers": [
        {"config": {}, "handler_type": "POLICY_HANDLER"},
        {
            "config": {
                "allow_backwards_status_update": False,
                "not_allowed_stage_transitions": [
                    {
                        "to_stages": ["ON_MY_PLATE"],
                        "from_stages": [
                            "INDICATED",
                            "WAITING_FOR_OTHERS",
                            "EXPIRED",
                            "DECLINED",
                            "QUOTED",
                            "QUOTED_BOUND",
                            "QUOTED_LOST",
                            "BLOCKED",
                            "CANCELED",
                        ],
                    },
                    {
                        "to_stages": ["INDICATED", "WAITING_FOR_OTHERS"],
                        "from_stages": [
                            "EXPIRED",
                            "DECLINED",
                            "QUOTED",
                            "QUOTED_BOUND",
                            "QUOTED_LOST",
                            "BLOCKED",
                            "CANCELED",
                        ],
                    },
                    {
                        "to_stages": ["QUOTED"],
                        "from_stages": [
                            "QUOTED_BOUND",
                            "DECLINED",
                            "QUOTED_LOST",
                            "BLOCKED",
                            "CANCELED",
                        ],
                    },
                ],
            },
            "handler_type": "SUBMISSION_HANDLER",
        },
        {
            "config": {
                "removal_strategy": "ALL",
                "remove_non_matching_coverages": True,
            },
            "handler_type": "COVERAGE_HANDLER",
        },
        {
            "config": {
                "default_primary_naics": {},
                "remove_previous_assignees": False,
                "only_assign_if_no_assignees": True,
            },
            "handler_type": "UNDERWRITER_HANDLER",
        },
        {"config": {}, "handler_type": "SUBMISSION_IDENTIFIERS_HANDLER"},
        {"config": {}, "handler_type": "REPORT_HANDLER"},
        {"config": {}, "handler_type": "RENEWAL_RELATION_HANDLER"},
        {
            "config": {
                "process_only_sync_shells": True,
            },
            "handler_type": "BROKERAGE_HANDLER",
        },
    ],
    "max_attempts": 10,
    "submission_producer_config": {
        "filters": [
            {"config": {"cutoff_threshold": 0.7}, "filter_type": "BROKERAGE_FILTER"},
            {"config": {"cutoff_threshold": 0.8}, "filter_type": "BROKER_FILTER"},
        ],
        "lookup_config": {
            "scoring_overrides": {
                "use_broker_info": True,
            },
            "scorers": [
                {
                    "config": {
                        "matcher_types": ["FUZZY_MATCHER", "NAME_SIMILARITY_MATCHER"],
                        "date_distance_map": _DATE_DISTANCE_MAP,
                    },
                    "scorer_type": "SUBMISSION_NAME_SCORER",
                },
                {
                    "config": {
                        "score_boost": 0.15,
                        "matcher_types": ["EMAIL_MATCHER"],
                        "date_distance_map": _DATE_DISTANCE_MAP,
                    },
                    "scorer_type": "EMAIL_SCORER",
                },
                {
                    "config": {
                        "matcher_types": ["RELATED_POLICY_MATCHER"],
                        "date_distance_map": _DATE_DISTANCE_MAP,
                    },
                    "scorer_type": "RELATED_POLICY_SCORER",
                },
                {
                    "config": {
                        "matcher_types": ["BUSINESS_NAME_MATCHER"],
                        "calculate_score_penalty": True,
                        "date_distance_map": _DATE_DISTANCE_MAP,
                    },
                    "scorer_type": "BUSINESS_NAME_SCORER",
                },
                {
                    "config": {
                        "matcher_types": ["ERS_BUSINESS_ID_MATCHER"],
                        "date_distance_map": _DATE_DISTANCE_MAP,
                    },
                    "scorer_type": "ERS_BUSINESS_ID_SCORER",
                },
            ],
            "scoring_policy": "HIGHEST_SCORE",
            "direct_matchers": [
                {"matcher_type": "POLICY_MATCHER"},
                {"matcher_type": "SUBMISSION_IDENTIFIER_MATCHER", "config": {"identifier_type": "quote_number"}},
            ],
            "indirect_matchers": [
                # {
                #     "config": {"additional_sub_sync_fields": ["broker", "brokerage", "effective_date"]},
                #     "matcher_type": "RELATED_POLICY_MATCHER",
                # },
                # {
                #     "config": {
                #         "fuzzy_matching_level": 4,
                #         "created_at_days_range": 110,
                #     },
                #     "matcher_type": "FUZZY_MATCHER",
                # },
                # {"config": {"created_at_days_range": 110}, "matcher_type": "EMAIL_MATCHER"},
                # {"config": {"created_at_days_range": 110}, "matcher_type": "NAME_SIMILARITY_MATCHER"},
                # {
                #     "config": {"query_similarity_threshold": 0.55, "created_at_days_range": 110},
                #     "matcher_type": "BUSINESS_NAME_MATCHER",
                # },
                # {"config": {"created_at_days_range": 110}, "matcher_type": "ERS_BUSINESS_ID_MATCHER"},
            ],
            "try_renewal_match": False,
            "matching_score_threshold": 0.85,
            "standout_submission_enabled": False,
            "use_grouping": True,
        },
        "shell_owner_email": "<EMAIL>",
        "duplicate_submissions": False,
        "original_max_days_old": 6,
        "original_min_days_old": 2,
        "enable_multiple_matches": True,
        "create_shell_submissions": True,
        "always_ready_after_num_days": 9999,
        "shell_submissions_days_passed": 7,
        "renewal_days_from_effective_date_for_shells": 60,
        "shell_creation_rules": {
            "group_children_stages": ["QUOTED_BOUND"],
            "individual_sync_requests_stages": ["QUOTED_BOUND"],
        },
    },
}
