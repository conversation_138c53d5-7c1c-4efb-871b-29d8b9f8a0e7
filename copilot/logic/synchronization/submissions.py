from collections import OrderedDict
from datetime import datetime, timed<PERSON>ta
from uuid import UUID
import json
import os

from infrastructure_common.logging import get_logger
from static_common.enums.match import MatchType
from static_common.enums.origin import Origin
from static_common.enums.submission import SubmissionStage
import flask
import pytz

from copilot.logic.dao.submission_dao import SubmissionDAO, SubmissionSyncDAO
from copilot.logic.synchronization.submission_lookup import SubmissionLookup
from copilot.logic.synchronization.submission_match_filters import (
    SubmissionMatchFilter,
    SubmissionMatchFilterFactory,
)
from copilot.logic.synchronization.synchronization_handlers import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Coverage<PERSON>andler,
    PolicyHandler,
    RenewalRelationHandler,
    ReportHandler,
    SubmissionHandler,
    SubmissionIdentifiersHandler,
    SubmissionPremisesHandler,
    UnderwriterHandler,
)
from copilot.models import Submission, User, db
from copilot.models.reports import SubmissionUser
from copilot.models.submission_sync import SubmissionSync
from copilot.models.synchronization.submissions import (
    CloseMatch,
    MatchingInformation,
    SubmissionChanges,
    SubmissionMatch,
    SubmissionMatcherResult,
    SubmissionProducerConfiguration,
    SubmissionProducerResult,
    SubmissionSyncProcessorConfiguration,
    SynchronizationHandlerType,
    SynchronizationResult,
)
from copilot.schemas.synchronization.submissions import SubmissionProducerResultSchema
from copilot.v3.controllers.reports import create_report, delete_report

logger = get_logger()

HANDLER_MAPPINGS = OrderedDict(
    [
        (SynchronizationHandlerType.POLICY_HANDLER, PolicyHandler),
        (SynchronizationHandlerType.REPORT_HANDLER, ReportHandler),
        (SynchronizationHandlerType.UNDERWRITER_HANDLER, UnderwriterHandler),
        (SynchronizationHandlerType.SUBMISSION_HANDLER, SubmissionHandler),
        (SynchronizationHandlerType.COVERAGE_HANDLER, CoverageHandler),
        (SynchronizationHandlerType.RENEWAL_RELATION_HANDLER, RenewalRelationHandler),
        (SynchronizationHandlerType.SUBMISSION_IDENTIFIERS_HANDLER, SubmissionIdentifiersHandler),
        (SynchronizationHandlerType.BROKERAGE_HANDLER, BrokerageHandler),
        (SynchronizationHandlerType.SUBMISSION_PREMISES_HANDLER, SubmissionPremisesHandler),
    ]
)

producer_result_schema = SubmissionProducerResultSchema()

IS_TEST = os.environ.get("TESTING_SCOPE", "false").lower() == "true"


class SubmissionProducer:
    # 2023-06-01
    ORIGINAL_CUTOFF_DATE = datetime(2023, 6, 1, tzinfo=pytz.utc)

    def __init__(
        self,
        config: SubmissionProducerConfiguration,
        organization_id: int,
    ):
        self.log = logger.bind(organization_id=organization_id)
        owner_user: User = User.query.filter(User.email == config.shell_owner_email).one_or_none()
        if not owner_user:
            self.log.error("Invalid SubmissionMatcher configuration. User not found", email=config.shell_owner_email)
            flask.abort(500)

        if owner_user.organization_id != organization_id:
            self.log.error(
                "Shell owner email belongs to a different organization than the matcher is configured for",
                shell_owner_organization_id=owner_user.organization_id,
                marcher_organization_id=organization_id,
            )
            flask.abort(500)

        self._shell_owner_email = config.shell_owner_email
        self._create_shell_submissions = config.create_shell_submissions
        self._duplicate_submissions = config.duplicate_submissions
        self._org_id = organization_id
        self._shell_min_days_before = datetime.now() - timedelta(days=config.shell_submissions_days_passed)
        self._original_min_days_old = config.original_min_days_old
        self._original_max_days_old = config.original_max_days_old
        self._enable_multiple_matches = config.enable_multiple_matches
        self._skip_readiness_check = config.skip_submission_readiness_check
        self._min_eff_date_shell = datetime.now() + timedelta(
            days=config.create_shell_submissions_days_before_effective_date
        )
        self._submission_lookup = SubmissionLookup(config.lookup_config)
        self._always_ready_before_date = datetime.now().replace(tzinfo=pytz.utc) - timedelta(
            days=config.always_ready_after_num_days
        )
        self._filters: list[SubmissionMatchFilter] = [
            SubmissionMatchFilterFactory.create_filter(fd.filter_type, fd.config) for fd in config.filters
        ]
        self._duplicate_subs_from_matchers = config.allowed_matchers_to_duplicate
        self._renewals_min_eff_date = datetime.now() + timedelta(
            days=config.renewal_days_from_effective_date_for_shells
        )
        self._remove_assignees_after_duplication = config.remove_assignees_after_duplication
        self._shells_received_date_cutoff = config.shells_received_date_cutoff or datetime(1900, 1, 1)
        self._shells_eff_date_cutoff = config.shells_eff_date_cutoff or datetime(1900, 1, 1)
        self._shell_creation_rules = config.shell_creation_rules

    def allow_submission_duplication(self, allow: bool) -> None:
        self._duplicate_submissions = allow

    def allow_shell_creation(self, allow: bool) -> None:
        self._create_shell_submissions = allow

    def _create_submission_matcher_result(
        self,
        submission_match: SubmissionMatch,
        match_type: MatchType,
        submission_ids: list[UUID] | None = None,
        found_shell: SubmissionMatch | None = None,
        close_matches: list[CloseMatch] | None = None,
    ) -> SubmissionProducerResult:
        is_shell = self._is_submission_shell(submission_match.submission)
        is_submission_ready = self._is_submission_ready(submission_match.submission)
        return SubmissionProducerResult(
            SubmissionMatcherResult(
                match_type=match_type,
                submission_id=submission_match.submission.id,
                submission_ids=submission_ids,
                submission_name=submission_match.submission.name,
                submission_stage=submission_match.submission.stage,
                report_id=submission_match.submission.report_id,
                is_shell=is_shell,
                is_submission_ready=is_submission_ready,
                submission=submission_match.submission,
                matching_information=MatchingInformation(submission_match.matcher, submission_match.matching_score),
                close_matches=close_matches,
            ),
            found_shell,
        )

    def _is_original_before_cutoff_date(self, submission: Submission) -> bool:
        created = submission.created_at
        if created.tzinfo is not None and created.tzinfo.utcoffset(created) is not None:
            # created_at is timezone aware
            return created < self.ORIGINAL_CUTOFF_DATE
        return created < self.ORIGINAL_CUTOFF_DATE.replace(tzinfo=None)

    def _has_always_ready_threshold_passed(self, submission: Submission) -> bool:
        created = submission.created_at
        if created.tzinfo is not None and created.tzinfo.utcoffset(created) is not None:
            # created_at is timezone aware
            return submission.created_at < self._always_ready_before_date
        return submission.created_at < self._always_ready_before_date.replace(tzinfo=None)

    @staticmethod
    def _is_submission_shell(submission: Submission) -> bool:
        return bool(not submission.businesses)

    def _is_submission_ready(self, submission: Submission) -> bool:
        return bool(
            self._skip_readiness_check
            or (submission.is_shell_from_sync or submission.is_verified or submission.is_verified_shell)
        )

    @staticmethod
    def __delete_assigned_uws(submission_id: UUID) -> None:
        SubmissionUser.query.filter(SubmissionUser.submission_id == submission_id).delete(synchronize_session="fetch")

    def __create_duplicate_submission(
        self,
        req: SubmissionSync,
        submission_match: SubmissionMatch,
        match_type: MatchType = MatchType.EXACT_MATCH,
        matched_submission_ids: list[UUID] | None = None,
        found_shell: SubmissionMatch | None = None,
        close_matches: list[CloseMatch] | None = None,
    ) -> SubmissionProducerResult:
        submission = submission_match.submission
        created_at_ref = req.submission_created_at_reference or datetime.now()
        # this was used before we started matching only verified submissions. The idea was to
        # wait for a certain amount of time to make sure the submission is loaded.
        # The lower bound makes sense, but the upper bound does not, if the submission is verified
        original_max_date = datetime.now().replace(tzinfo=pytz.utc)
        original_min_date = created_at_ref.replace(tzinfo=pytz.utc) - timedelta(days=180)
        if not submission.is_verified and not submission.is_verified_shell:
            # if the submission is not verified in any way, we should wait for the submission to be "loaded"
            original_max_date = original_max_date - timedelta(days=self._original_min_days_old)
            original_min_date = created_at_ref.replace(tzinfo=pytz.utc) - timedelta(days=self._original_max_days_old)

        if not self._duplicate_submissions or submission_match.matcher not in self._duplicate_subs_from_matchers:
            return self.__create_shell_submission(req, found_shell, close_matches)

        # before comparing check if submission.created_at is timezone aware. If it isn't
        # make original_min_date and original_max_date timezone naive
        if submission.created_at.tzinfo is None:
            original_min_date = original_min_date.replace(tzinfo=None)
            original_max_date = original_max_date.replace(tzinfo=None)

        if not (
            original_min_date <= submission.created_at <= original_max_date
        ) or self._is_original_before_cutoff_date(submission):
            return self.__create_shell_submission(req, found_shell, close_matches)

        # if we already found a shell and the matched submission is a shell, just return the found shell
        # no point to duplicate a shell
        if found_shell and self._is_submission_shell(submission):
            return self._create_submission_matcher_result(
                found_shell,
                MatchType.EXACT_MATCH,
                found_shell=None,
                close_matches=close_matches,
            )

        old_policy_number = submission.client_submission_ids[0].client_submission_id
        new_policy_number = req.policy_number
        # special logic for Conifer
        if req.policy_number.startswith("DERIVED-"):
            old_policy_number = (submission.policy_number or submission.quote_number or "").split(", ")[0]
            new_policy_number = req.sync_identifiers.first_policy_number or req.sync_identifiers.first_quote_number
        original_report_id = submission.report_ids[0]
        is_shell = self._is_submission_shell(submission)
        is_submission_ready = self._is_submission_ready(submission)

        if not is_submission_ready:
            return SubmissionProducerResult(
                SubmissionMatcherResult(
                    match_type=MatchType.UNMATCHED,
                    is_submission_ready=False,
                    close_matches=close_matches,
                ),
                found_shell,
            )

        body: dict = {
            "original_report_id": str(original_report_id),
            "pds": is_shell,
            "origin": Origin.SYNC.value,
            "external_id": req.policy_number,
        }
        if submission.businesses:
            body["target_stage"] = req.stage
            body["additional_data"] = {
                "stage_details": {"update_from_sync": True, "skip_transitions_restriction": True}
            }
            if req.stage == SubmissionStage.DECLINED:
                body["additional_data"]["declined_date"] = req.declined_date.isoformat() if req.declined_date else None

        try:
            report_id = create_report(json.dumps(body), external=False, from_sync=True)
        except:
            db.session.rollback()
            self.log.exception("Error creating duplicate submission", policy=req, submission_id=submission.id)
            return SubmissionProducerResult()

        report_name_no_policy = submission.reports[0].name.replace(f" : {old_policy_number}", "")
        if f" : {old_policy_number}" not in submission.name:
            original_name = f"{report_name_no_policy} : {old_policy_number}"
            submission.name = original_name
            submission.reports[0].name = original_name
        new_name = f"{report_name_no_policy} : {new_policy_number}"
        # do not update updated_at of the original because it will appear
        # in the support queue if it is not verified
        submission.updated_at = Submission.__table__.c.updated_at
        db.session.add(submission)
        self.log.info("Submission duplicated for client", submission_id=submission.id, external_id=req.policy_number)
        new_submission = SubmissionSyncDAO.get_submission(report_id=report_id)
        new_submission.reports[0].name = new_name
        new_submission.name = new_name
        new_submission.origin = Origin.SYNC
        new_submission.created_at = submission.created_at
        new_submission.report.created_at = submission.report.created_at
        new_submission.is_auto_processed = False
        new_submission.quoted_date = req.quoted_date
        new_submission.bound_date = req.bound_date
        if self._remove_assignees_after_duplication:
            self.__delete_assigned_uws(new_submission.id)
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            logger.error("Cannot commit changes", error=e)
            raise e
        return SubmissionProducerResult(
            SubmissionMatcherResult(
                match_type=match_type,
                submission_id=new_submission.id,
                submission_ids=matched_submission_ids,
                submission_name=new_name,
                submission_stage=new_submission.stage,
                report_id=report_id,
                original_report_id=original_report_id,
                original_submission_id=submission.id,
                is_duplicate=True,
                is_shell=is_shell,
                is_submission_ready=is_submission_ready,
                submission=new_submission,
                matching_information=MatchingInformation(submission_match.matcher, submission_match.matching_score),
                close_matches=close_matches,
            ),
            found_shell,
        )

    def __create_shell_submission(
        self,
        req: SubmissionSync,
        found_shell: SubmissionMatch,
        close_matches: list[CloseMatch] | None = None,
    ) -> SubmissionProducerResult:
        received_date_cutoff = self._shells_received_date_cutoff.replace(tzinfo=None)
        eff_date_cutoff = self._shells_eff_date_cutoff.replace(tzinfo=None)
        if found_shell:
            return self._create_submission_matcher_result(
                found_shell, MatchType.EXACT_MATCH, found_shell=None, close_matches=close_matches
            )

        if not self._create_shell_submissions:
            return SubmissionProducerResult(
                SubmissionMatcherResult(match_type=MatchType.UNMATCHED, close_matches=close_matches), None
            )

        if req.is_group_child and req.stage not in self._shell_creation_rules.group_children_stages:
            return SubmissionProducerResult(
                SubmissionMatcherResult(match_type=MatchType.UNMATCHED, close_matches=close_matches), None
            )

        if req.is_group_parent and req.stage not in self._shell_creation_rules.group_parents_stages:
            return SubmissionProducerResult(
                SubmissionMatcherResult(match_type=MatchType.UNMATCHED, close_matches=close_matches), None
            )

        if req.is_individual_reqeust and req.stage not in self._shell_creation_rules.individual_sync_requests_stages:
            return SubmissionProducerResult(
                SubmissionMatcherResult(match_type=MatchType.UNMATCHED, close_matches=close_matches), None
            )

        created_at_ref = req.submission_created_at_reference or datetime.now()
        created_or_received = req.created_date or req.received_date

        if created_at_ref < received_date_cutoff:
            return SubmissionProducerResult(
                SubmissionMatcherResult(match_type=MatchType.UNMATCHED, close_matches=close_matches), None
            )

        if req.effective_date and req.effective_date < eff_date_cutoff:
            return SubmissionProducerResult(
                SubmissionMatcherResult(match_type=MatchType.UNMATCHED, close_matches=close_matches), None
            )

        # in the cases where we use only eff. date
        if created_or_received is not None:
            # if its a renewal and the eff_date is greater than the min eff date for renewals -> unmatched
            # if we do not create shells -> unmatched
            # if not enough days have passed since the received_date -> unmatched
            if (
                req.is_renewal and req.effective_date > self._renewals_min_eff_date
            ) or created_at_ref > self._shell_min_days_before:
                return SubmissionProducerResult(
                    SubmissionMatcherResult(match_type=MatchType.UNMATCHED, close_matches=close_matches),
                    None,
                )
        else:
            # we rely only on effective date
            if req.effective_date > self._min_eff_date_shell:
                return SubmissionProducerResult(
                    SubmissionMatcherResult(match_type=MatchType.UNMATCHED, close_matches=close_matches),
                    None,
                )

        try:
            res = create_report(
                json.dumps(
                    {
                        "user_email": self._shell_owner_email,
                        "name": req.submission_name,
                        "origin": Origin.SYNC.value,
                        "pds": True,
                        "external_id": req.policy_number,
                    }
                ),
                adjust_coverages=False,
            )
        except:
            self.log.exception("Error creating shell submission", policy=req)
            return SubmissionProducerResult()
        created_report_dict = res[0]
        submission_id = UUID(created_report_dict["submissions"][0]["id"])
        submission_name = created_report_dict["submissions"][0]["name"]
        self.log.info(
            "Created shell Submission for client id",
            submission_id=submission_id,
            external_id=req.policy_number,
        )

        submission = SubmissionDAO.get_minimal_submission(
            submission_id,
            additional_fields=["created_at"],
            include_report=True,
            report_additional_fields=["created_at"],
        )
        submission.created_at = created_at_ref.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)
        submission.report.created_at = submission.created_at
        db.session.add(submission)
        db.session.commit()

        return SubmissionProducerResult(
            SubmissionMatcherResult(
                match_type=MatchType.SHELL_SUBMISSION,
                submission_id=submission_id,
                submission_name=submission_name,
                report_id=UUID(created_report_dict["id"]),
                is_shell=True,
                is_submission_ready=True,
                submission=submission,
                close_matches=close_matches,
            ),
            None,
        )

    def __apply_filters(self, req: SubmissionSync, matches: list[SubmissionMatch]) -> list[SubmissionMatch]:
        filtered_matches = matches
        for f in self._filters:
            filtered_matches = f.apply_filter(req, matches)
            if len(filtered_matches) == 1:
                return filtered_matches
        return filtered_matches

    def match_submission(self, req: SubmissionSync) -> SubmissionProducerResult:
        if req.replaced_by_client_id:
            # request is being replaced by another one
            # there is no need to match and sync this request. Just return REPLACED
            logger.info(
                "Sync request replaced by another one",
                submission_sync_id=req.id,
                replaced_by=req.replaced_by_client_id,
                client_id=req.policy_number,
            )
            return SubmissionProducerResult(
                SubmissionMatcherResult(match_type=MatchType.REPLACED, close_matches=[]), None
            )

        if req.is_deleted:
            # request is deleted
            logger.info(
                "Sync request is deleted",
                submission_sync_id=req.id,
                client_id=req.policy_number,
            )
            return SubmissionProducerResult(
                SubmissionMatcherResult(match_type=MatchType.DELETED, close_matches=[]), None
            )

        lookup_result = self._submission_lookup.lookup(req)
        matched_submissions = lookup_result.matches
        close_matches = lookup_result.close_matches
        found_shell = lookup_result.found_sync_shell
        self.log.info(
            "Found matching submissions for Client",
            submission_count=len(matched_submissions),
            submission_ids=[ms.submission.id for ms in matched_submissions],
            external_id=req.policy_number,
            submission_name=req.submission_name,
        )

        matches_without_client_id = [ms for ms in matched_submissions if not ms.submission.client_submission_ids]

        # no matches
        if len(matched_submissions) == 0:
            return self.__create_shell_submission(req, found_shell, close_matches)

        # we matched by client_id
        if matched_submissions[0].submission.has_client_submission_id(req.policy_number):
            return self._create_submission_matcher_result(
                matched_submissions[0], MatchType.EXACT_MATCH, found_shell=found_shell, close_matches=close_matches
            )

        # we matched by client_id, but this request replaces another
        if req.replaces_client_id and matched_submissions[0].submission.has_client_submission_id(
            req.replaces_client_id
        ):
            return self._create_submission_matcher_result(
                matched_submissions[0], MatchType.EXACT_MATCH, found_shell=found_shell, close_matches=close_matches
            )

        # in the matched submissions only one is without client_id. We are returning that
        if len(matches_without_client_id) == 1:
            # delete the found shell
            return self._create_submission_matcher_result(
                matches_without_client_id[0],
                MatchType.EXACT_MATCH,
                found_shell=found_shell,
                close_matches=close_matches,
            )

        # there are more than one matched submission without client_id. We need to filter them
        if len(matches_without_client_id) > 1:
            matches_without_client_id = self.__apply_filters(req, matches_without_client_id)
            # after filtering zero matches. Try and create shell.
            if len(matches_without_client_id) == 0:
                return self.__create_shell_submission(req, found_shell, close_matches)
            # after filtering only one match left. Return it
            if len(matches_without_client_id) == 1:
                return self._create_submission_matcher_result(
                    matches_without_client_id[0],
                    MatchType.EXACT_MATCH,
                    found_shell=found_shell,
                    close_matches=close_matches,
                )
            # after filtering multiple matches left. Return the first
            if len(matches_without_client_id) > 1:
                if self._enable_multiple_matches:
                    submission_ids = [mwci.submission.id for mwci in matches_without_client_id]
                    return self._create_submission_matcher_result(
                        matches_without_client_id[0],
                        MatchType.MULTIPLE_MATCHES,
                        submission_ids,
                        found_shell=found_shell,
                        close_matches=close_matches,
                    )
                return self.__create_shell_submission(req, found_shell, close_matches)

        # we matched only one submission, but it has a client_id
        if len(matched_submissions) == 1:
            return self.__create_duplicate_submission(
                req,
                matched_submissions[0],
                found_shell=found_shell,
                close_matches=close_matches,
            )

        # we matched multiple submissions, but they all have client_id
        if len(matched_submissions) > 1:
            return self.__create_duplicate_submission(
                req,
                matched_submissions[0],
                MatchType.MULTIPLE_MATCHES,
                matched_submission_ids=[m.submission.id for m in matched_submissions],
                found_shell=found_shell,
                close_matches=close_matches,
            )

        logger.warning("Condition missed", submission_sync_id=req.id)
        return self.__create_shell_submission(req, found_shell, close_matches)


class SubmissionSyncProcessor:
    def __init__(self, configuration: SubmissionSyncProcessorConfiguration, organization_id: int):
        self._producer = SubmissionProducer(configuration.submission_producer_config, organization_id)
        self._org_id = organization_id
        self._max_attempts = configuration.max_attempts
        self._continue_on_handler_error = configuration.continue_on_handler_error
        self._handlers = []
        self.log = logger.bind(organization_id=organization_id)
        if not configuration.handlers:
            self.log.error("Handler cannot be created due to invalid configuration")

        # Parse through known handler types in specific order
        for handler_type in HANDLER_MAPPINGS:
            for handler in configuration.handlers:
                if handler.handler_type not in HANDLER_MAPPINGS:
                    self.log.error("Handler type not found in mapping", handler_type=handler.handler_type)
                    flask.abort(500)
                if handler.handler_type == handler_type:
                    handler_cls = HANDLER_MAPPINGS[handler.handler_type]
                    self._handlers.append(handler_cls(handler.config))

    def process_sync_request(
        self,
        req: SubmissionSync,
        run_id: str,
    ) -> SynchronizationResult:
        if self._org_id != req.organization_id:
            self.log.error(
                "Configuration mismatch for submission request",
                matcher_organization_id=self._org_id,
                request_organization_id=req.organization_id,
            )
            return SynchronizationResult(req.id, req.policy_number, req.organization_id, matching_error=True)

        if req.applied:
            self.log.info(
                "SubmissionSync has already been applied and there are no pending changes",
                submission_sync_id=req.id,
                created_at=req.created_at,
            )
            return SynchronizationResult(req.id, req.policy_number, req.organization_id, already_synchronized=True)

        if 0 < self._max_attempts <= req.attempt:
            self.log.warning(
                "SubmissionSync max attempts reached. Moving to the next change",
                submission_sync_id=req.id,
                created_at=req.created_at,
            )
            req.applied = True
            db.session.add(req)
            db.session.commit()
            db.session.refresh(req)
            return SynchronizationResult(req.id, req.policy_number, req.organization_id, already_synchronized=True)

        req.attempt = req.attempt + 1

        result = req.get_sync_matching_result(run_id)

        if IS_TEST:
            # this is a test and results must be available here
            logger.info("Test run. Using existing results", submission_sync_id=req.id, run_id=run_id)
            producer_result = producer_result_schema.load(result.matching_results)
        elif result and result.is_processed and result.matching_results:
            logger.info(
                "Using sync matching results",
                submission_sync_id=req.id,
                run_id=run_id,
                organization_id=req.organization_id,
            )
            producer_result = producer_result_schema.load(result.matching_results)
        else:
            logger.info(
                "Running matching for submission",
                submission_sync_id=req.id,
                run_id=run_id,
                organization_id=req.organization_id,
            )
            # run matching as backup
            producer_result = self._producer.match_submission(req)
        shell_to_delete = producer_result.shell_to_delete
        match_result = producer_result.match_result

        if shell_to_delete:
            try:
                delete_report(str(shell_to_delete.report_id), from_sync=True)
            except:
                db.session.rollback()
                logger.exception(
                    "Cannot delete shell submission",
                    submission_id=shell_to_delete.submission.id,
                    report_id=shell_to_delete.report_id,
                )

        if match_result and match_result.is_submission_ready is False:
            self.log.info(
                "Skipping Submission because the PDS process is not complete yet",
                submission_id=match_result.submission_id,
            )
            match_result.match_type = MatchType.SUBMISSION_NOT_READY
            return SynchronizationResult(
                req.id,
                req.policy_number,
                req.organization_id,
                matching_error=(match_result is None),
                match_result=match_result,
            )

        if not match_result:
            self.log.error("No match result returned from producer", submission_sync_id=req.id)
            return SynchronizationResult(
                req.id,
                req.policy_number,
                req.organization_id,
                matching_error=True,
                match_result=match_result,
            )

        if match_result.match_type in MatchType.types_without_results():
            self.log.info(
                "No submission was matched",
                submission_sync_id=req.id,
                created_at=req.created_at,
                match_type=match_result.match_type,
            )
            return SynchronizationResult(
                req.id,
                req.policy_number,
                req.organization_id,
                matching_error=False,
                match_result=match_result,
            )

        if req.is_group_child and match_result.match_type not in MatchType.types_without_results():
            # mark the parent sync as deleted. Then we have a job that will do the deletion
            parent_sync = SubmissionSync.query.filter(
                SubmissionSync.policy_number == req.parent_client_id,
                SubmissionSync.organization_id == req.organization_id,
            ).one_or_none()
            if parent_sync:
                parent_sync.is_deleted = True
                parent_sync.applied = False
                db.session.add(parent_sync)
                db.session.commit()
                self.log.info(
                    "Marked parent sync as deleted",
                    submission_sync_id=parent_sync.id,
                    parent_client_id=req.parent_client_id,
                    organization_id=req.organization_id,
                )

        submission_changes = SubmissionChanges()
        for handler in self._handlers:
            can_process = handler.can_process_match(match_result)
            if not can_process:
                continue
            handler_error = False
            state_before_handler = handler.get_submission_state(match_result.submission_id)
            submission_changes.before_sync.update(state_before_handler)
            run_handler = handler.has_changes_to_apply(req, state_before_handler)

            if run_handler:
                handler_result = handler.handle_sync_request(match_result, req)
                submission_changes.handler_outputs.append(handler_result)
                handler_error = bool(handler_result.error)
                state_after_handler = handler.get_submission_state(match_result.submission_id)
                submission_changes.after_sync.update(state_after_handler)
            else:
                submission_changes.after_sync.update(state_before_handler)
            if handler_error and not self._continue_on_handler_error:
                break

        if submission_changes.has_sync_error:
            db.session.add(req)
            db.session.commit()
            return SynchronizationResult(
                req.id,
                req.policy_number,
                req.organization_id,
                match_result=match_result,
                synchronization_error=True,
                submission_changes=submission_changes,
            )

        req.applied = True
        db.session.add(req)
        db.session.commit()

        return SynchronizationResult(
            req.id,
            req.policy_number,
            req.organization_id,
            match_result=match_result,
            submission_changes=submission_changes,
        )
