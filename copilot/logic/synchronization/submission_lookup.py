from common.utils.collections import filter_none
from infrastructure_common.logging import get_logger

from copilot.logic.synchronization.match_scorer import MatchScorerFactory
from copilot.logic.synchronization.submission_matcher import SubmissionMatcherFactory
from copilot.models import db
from copilot.models.submission_sync import (
    SubmissionIdentifiersSuggestion,
    SubmissionSync,
)
from copilot.models.synchronization.submissions import (
    LookupConfig,
    MatcherType,
    ScoringPolicy,
    SubmissionLookupResult,
    SubmissionMatch,
)

logger = get_logger()


class SubmissionLookup:
    AUTO_DISQUALIFY_WORDS = ["[ARCH CLEARING]"]

    def __init__(
        self,
        config: LookupConfig,
    ):
        self._config = config
        self._matching_score_threshold = config.matching_score_threshold
        self._direct_matchers = [
            SubmissionMatcherFactory.create_direct_submission_matcher(dm.matcher_type, dm.config)
            for dm in config.direct_matchers
        ]
        self._indirect_matchers = [
            SubmissionMatcherFactory.create_indirect_submission_matcher(idm.matcher_type, idm.config)
            for idm in config.indirect_matchers
        ]
        self._scorers = [
            MatchScorerFactory.create_match_scorer(sd.scorer_type, sd.config, config.scoring_overrides)
            for sd in config.scorers
        ]

    def lookup(self, req: SubmissionSync) -> SubmissionLookupResult:
        result = SubmissionLookupResult()
        direct_matchers = self._direct_matchers
        indirect_matchers = self._indirect_matchers

        if self._config.use_grouping:
            # check if the request is part of a group or group parent
            if req.parent_client_id:
                # if it is a part of a group we want to use only the direct matchers
                indirect_matchers = []
            if req.submission_sync_children:
                # if its a group parent we use only the policy matcher and indirect matchers
                direct_matchers = [dm for dm in direct_matchers if dm.matcher_type == MatcherType.POLICY_MATCHER]

        for dm in direct_matchers:
            matched_subs = dm.match(req)
            # The matched results from direct matches should always have length of one
            if matched_subs:
                if len(matched_subs) != 1:
                    logger.error(
                        "Direct matchers should return exactly one submission", matcher=dm.matcher_type, sync_id=req.id
                    )
                    return result
                result.matches.extend(
                    SubmissionMatch(
                        submission_id=sub.id,
                        report_id=sub.report_id,
                        submission=sub,
                        matcher=dm.matcher_type,
                        matching_score=1.0,
                    )
                    for sub in matched_subs
                )
                break

        if req.direct_match_only:
            return result

        # we want to retry matching if the request is renewal and try_renewal_match is enabled and
        # the matched submission is a shell from sync
        # it all could be written in one condition, but it could be confusing
        if result.matches:
            if req.is_renewal and self._config.try_renewal_match and result.matches[0].submission.is_shell_from_sync:
                result.found_sync_shell = result.matches[0]
                result.matches = []
                # we want to continue with the matching in this case
            else:
                return result

        indirect_matches = []
        for im in indirect_matchers:
            matched_subs = im.match(req, self._config.reference_date_type)
            indirect_matches.extend(
                [
                    SubmissionMatch(submission_id=s.id, report_id=s.report_id, submission=s, matcher=im.matcher_type)
                    for s in matched_subs
                ]
            )

        self._calculate_scores(indirect_matches, req)

        above_threshold, bellow_threshold = self._filter_submissions(indirect_matches)

        if self._config.create_suggestions:
            self._save_identifier_suggestions(above_threshold + bellow_threshold, req)

        result.matches.extend(above_threshold)
        result.discarded_matches.extend(bellow_threshold)

        # check if we have a standout match
        if len(result.matches) == 0 and len(result.discarded_matches) > 0 and self._config.standout_submission_enabled:
            # check if the first submission in the discarded matches is a standout submission
            first_score = result.discarded_matches[0].matching_score
            second_score = result.discarded_matches[1].matching_score if len(result.discarded_matches) > 1 else 0
            if (first_score - second_score) >= self._config.standout_submission_threshold:
                logger.info("Standout submission found", sync_id=req.id, score=first_score)
                result.matches.append(result.discarded_matches.pop(0))

        return result

    def _filter_submissions(
        self, submission_matches: list[SubmissionMatch]
    ) -> tuple[list[SubmissionMatch], list[SubmissionMatch]]:
        ids = set()
        above_threshold = []
        bellow_threshold = []
        filtered_matches: list[SubmissionMatch] = list(
            filter(lambda x: x.matching_score is not None, submission_matches)
        )
        # filter out submissions that have any of the disqualifying words in their name
        filtered_matches = list(
            filter(
                lambda x: all(word not in x.submission.name for word in self.AUTO_DISQUALIFY_WORDS), filtered_matches
            )
        )
        filtered_matches = sorted(filtered_matches, key=lambda x: x.adjusted_score, reverse=True)
        filtered_matches = [x for x in filtered_matches if x.adjusted_score > 0]
        for sm in filtered_matches:
            if sm.submission.id in ids:
                continue

            ids.add(sm.submission.id)

            if sm.matching_score >= self._matching_score_threshold:
                above_threshold.append(sm)
            else:
                bellow_threshold.append(sm)
        return above_threshold, bellow_threshold

    def _calculate_scores(self, matches: list[SubmissionMatch], req: SubmissionSync) -> None:
        for m in matches:
            scores = [
                scorer.score_submission_match(m, req, self._config.reference_date_type) for scorer in self._scorers
            ]
            m.matching_score = self._calculate_final_score(filter_none(scores))

    def _calculate_final_score(self, scores: list[float]) -> float:
        if not scores:
            return 0.0

        if self._config.scoring_policy == ScoringPolicy.HIGHEST_SCORE:
            return max(scores)
        if self._config.scoring_policy == ScoringPolicy.LOWEST_SCORE:
            return min(scores)
        if self._config.scoring_policy == ScoringPolicy.AVERAGE_SCORE:
            return sum(scores) / len(scores)
        logger.error("Invalid policy for already scored matches", policy=self._config.scoring_policy)
        return 0.0

    @staticmethod
    def _save_identifier_suggestions(submission_matches: list[SubmissionMatch], req: SubmissionSync) -> None:
        for sm in submission_matches:
            suggestion = SubmissionIdentifiersSuggestion()
            suggestion.submission_id = sm.submission_id
            suggestion.submission_sync_id = req.id
            suggestion.confidence = sm.matching_score
            db.session.add(suggestion)
        if submission_matches:
            db.session.commit()
