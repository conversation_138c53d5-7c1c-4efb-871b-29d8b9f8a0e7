from typing import TYPE_CHECKING
import json

from flask_login import current_user
from infrastructure_common.logging import get_logger
from sqlalchemy.orm import Session
from static_common.enums.caller_context import CallerContext
from static_common.enums.entity import EntityInformation
from static_common.enums.organization import ExistingOrganizations
from static_common.mappings.icc import AIG_ISO_GL_TO_ICC_CODE_MAPPING
from static_common.models.submission_level_data import SourceDetails
from static_common.taxonomies.industry_classification import ICCCode

from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.models import db
from copilot.models.submission_level_extracted_data import SubmissionLevelExtractedData
from copilot.models.types import SubmissionTaxonomySource
from copilot.utils import get_request_caller_context
from copilot.v3.utils.support_users import current_support_user

if TYPE_CHECKING:
    from structlog.stdlib import BoundLogger

    from copilot.models.reports import Submission, SubmissionFieldSource

logger = get_logger()


TAXONOMY_SOURCE_PRIORITY = {
    SubmissionTaxonomySource.MANUAL: 1,
    SubmissionTaxonomySource.TAXONOMY_SYNC: 2,
    SubmissionTaxonomySource.AUTO: 3,
}


def get_current_context_source(current_session: Session | None = None) -> SubmissionTaxonomySource:
    caller_context = get_request_caller_context()
    if caller_context == CallerContext.SUBMISSION_SYNC:
        return SubmissionTaxonomySource.MANUAL
    elif caller_context == CallerContext.TAXONOMY_SYNC:
        return SubmissionTaxonomySource.TAXONOMY_SYNC
    elif (
        (current_user and (current_user.is_internal_machine_user or current_user.is_nationwide_machine_user))
        # Consider support users as the ones having priority AUTO
        or current_support_user(db_session=current_session) is not None
    ):
        return SubmissionTaxonomySource.AUTO
    else:
        return SubmissionTaxonomySource.MANUAL


def _get_submission_data_source(
    submission: "Submission", taxonomy_field: str, missing_source: SubmissionTaxonomySource
) -> tuple[SubmissionTaxonomySource, "SubmissionFieldSource"]:
    sources: list["SubmissionFieldSource"] = submission.field_sources

    field_sources = [field_source for field_source in sources if field_source.field_name == taxonomy_field]
    field_sources.sort(key=lambda x: x.updated_at or x.created_at, reverse=True)

    field_source = field_sources[0] if field_sources else None
    if not field_source:
        from copilot.models.reports import SubmissionFieldSource

        field_source = SubmissionFieldSource(
            submission_id=submission.id, field_name=taxonomy_field, source=missing_source
        )
        db.session.add(field_source)

    return SubmissionTaxonomySource(field_source.source), field_source


def _should_update_based_on_priority(
    current_context_source: SubmissionTaxonomySource,
    submission_data_source: SubmissionTaxonomySource,
) -> bool:
    return TAXONOMY_SOURCE_PRIORITY[current_context_source] <= TAXONOMY_SOURCE_PRIORITY[submission_data_source]


def _log_if_updated_against_priority(
    bnd_log: "BoundLogger",
    current_context_source: SubmissionTaxonomySource,
    submission_data_source: SubmissionTaxonomySource,
):
    if _should_update_based_on_priority(current_context_source, submission_data_source):
        # Field is updated with the respect to the priority
        return

    bnd_log.warning("Taxonomy field updated against the priority", process_name="taxonomy-priority")


def handle_field_update_with_priority(
    submission: "Submission",
    new_value: str,
    field_name: str,
) -> None:
    current_context_source = get_current_context_source()
    submission_data_source, data_source_db = _get_submission_data_source(
        submission=submission, taxonomy_field=field_name, missing_source=current_context_source
    )

    if getattr(submission, field_name, None) == new_value and submission_data_source == current_context_source:
        # Neither the field nor the source has changed, so we don't need to update anything
        return

    log = logger.bind(
        submission_id=submission.id,
        report_id=submission.report_id,
        field_name=field_name,
        new_value=new_value,
        current_value=getattr(submission, field_name, None),
        context_source=current_context_source,
        submission_data_source=submission_data_source,
        context_source_priority=TAXONOMY_SOURCE_PRIORITY[current_context_source],
        submission_data_source_priority=TAXONOMY_SOURCE_PRIORITY[submission_data_source],
    )

    if _should_update_based_on_priority(
        current_context_source, submission_data_source
    ) or not FeatureFlagsClient.is_feature_enabled_for_request_user(feature=FeatureType.USE_PRIORITY_FOR_TAXONOMIES):
        if current_support_user() is not None and field_name == "primary_naics_code":
            sled = SubmissionLevelExtractedData(
                field=EntityInformation.NAICS_CODES.value,
                value=json.dumps({new_value: 1.0}),
                submission_id=submission.id,
                is_selected=True,
                selected_by_user=current_user.email,
                is_valid=True,
                validation_details="Set by support user",
                source_details=SourceDetails.MANUAL,
                generation_method=f"{current_user.email}",
            )
            db.session.add(sled)

        _log_if_updated_against_priority(log, current_context_source, submission_data_source)
        setattr(submission, field_name, new_value)
        data_source_db.source = current_context_source
        log.info(f"Updating field {field_name}")
    else:
        log.info(f"Not updating field {field_name}, because of the sources priority")


def update_submission_naics_if_needed(submission: "Submission", new_naics: str | None) -> None:
    handle_field_update_with_priority(submission, new_naics, "primary_naics_code")


def update_submission_sic_if_needed(submission: "Submission", new_sic: str | None) -> None:
    handle_field_update_with_priority(submission, new_sic, "sic_code")


def _handle_aig_icc_code_mapping(submission: "Submission", new_gl_code: str) -> None:
    mapped_icc_code = AIG_ISO_GL_TO_ICC_CODE_MAPPING.get(new_gl_code)
    if not mapped_icc_code:
        logger.warning("Missing AIG ICC code mapping for ISO GL code", gl_code=new_gl_code)
        return

    if mapped_icc_code not in ICCCode:
        logger.error("Invalid ICC code in AIG mapping", gl_code=new_gl_code, icc_code=mapped_icc_code)
        return

    update_submission_icc_code_if_needed(submission, mapped_icc_code)


def update_submission_gl_code_if_needed(submission: "Submission", new_gl_code: str | None) -> None:
    handle_field_update_with_priority(submission, new_gl_code, "iso_gl_code")

    if new_gl_code and submission.organization_id == ExistingOrganizations.AIG.value:
        _handle_aig_icc_code_mapping(submission, new_gl_code)


def update_submission_icc_code_if_needed(submission: "Submission", new_icc_code: str | None) -> None:
    handle_field_update_with_priority(submission, new_icc_code, "icc_code")


def synchronize_field_source_between_submissions(
    from_submission: "Submission",
    to_submission: "Submission",
    field_name: str,
) -> None:
    from_source, _ = _get_submission_data_source(from_submission, field_name, SubmissionTaxonomySource.AUTO)
    _, to_source_db = _get_submission_data_source(to_submission, field_name, SubmissionTaxonomySource.AUTO)

    to_source_db.source = from_source
