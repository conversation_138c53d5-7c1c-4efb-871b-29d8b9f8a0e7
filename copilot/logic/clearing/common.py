from collections.abc import Callable
from dataclasses import dataclass
from uuid import UUID

from sqlalchemy.orm import Session
from structlog.stdlib import BoundLogger

from copilot.models import ReportV2, Submission
from copilot.models.clearing.external import FinishExternalClearingRequest
from copilot.models.submission_premises import SubmissionPremises

FinishExternalClearingHandler = Callable[[UUID, FinishExternalClearingRequest], None]

MatchingPremisesFetcher = Callable[[Submission, Session, BoundLogger], list[SubmissionPremises]]
MatchingPremisesHandler = Callable[[Submission, list[SubmissionPremises], BoundLogger], list[tuple[ReportV2, str]]]


@dataclass
class PremisesClearingHandler:
    matching_premises_fetcher: MatchingPremisesFetcher
    matching_premises_handler: MatchingPremisesHandler
