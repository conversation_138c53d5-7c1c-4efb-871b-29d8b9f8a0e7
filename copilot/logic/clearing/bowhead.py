from infrastructure_common.logging import get_logger
from sqlalchemy import desc
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>

from copilot.models import ReportV2, Submission, db
from copilot.models.submission_premises import SubmissionPremises
from copilot.models.types import SubmissionPremisesType
from copilot.services.orgs.bowhead_service import (
    BowheadRelationResult,
    BowheadRelationType,
    BowheadService,
)

logger = get_logger()

SAME_BROKER_CONFLICT_LABEL = "Same broker already holds in-force policy"
DIFFERENT_BROKER_CONFLICT_LABEL = "Different broker already holds in-force policy"


def handle_submissions_matched_by_premises_for_bowhead(
    current_submission: Submission,
    matched_submission_premises: list[SubmissionPremises],
    bound_log: BoundLogger | None = None,
) -> list[tuple[ReportV2, str]]:
    bound_log = _get_bound_logger(bound_log, current_submission.id)

    relations = BowheadService.analyze_relations(current_submission, matched_submission_premises)

    if _should_skip_processing(relations):
        bound_log.info("BowheadClearing: New account detected, no clearing conflicts")
        return []

    _copy_premises_data_if_needed(current_submission, relations, bound_log)
    return _process_relations(current_submission, relations, bound_log)


def _get_bound_logger(bound_log: BoundLogger | None, submission_id: int) -> BoundLogger:
    if bound_log is None:
        bound_log = logger
    return bound_log.bind(submission_id=submission_id)


def _should_skip_processing(relations: list[BowheadRelationResult]) -> bool:
    return not relations or relations[0].relation_type == BowheadRelationType.NO_CONFLICT_AND_NEW_ACCOUNT


def _copy_premises_data_if_needed(
    current_submission: Submission, relations: list[BowheadRelationResult], log: BoundLogger
) -> None:
    sync_result = _find_first_sync_result(relations)
    if sync_result:
        log.info(
            "BowheadClearing: Copying data from highest confidence 'SYNC' submission premises",
            matched_submission_id=sync_result.matched_submission_premises.submission_id,
            matched_premises_id=sync_result.matched_submission_premises.premises_id,
        )
        _copy_premises_data(current_submission, sync_result.matched_submission_premises)
    else:
        log.info("BowheadClearing: No 'SYNC' submission premises found, skipping copy")


def _find_first_sync_result(relations: list[BowheadRelationResult]) -> BowheadRelationResult | None:
    for result in relations:
        if (
            result.matched_submission_premises
            and result.matched_submission_premises.submission_premises_type == SubmissionPremisesType.SYNC
        ):
            return result
    return None


def _copy_premises_data(current_submission: Submission, source_premises: SubmissionPremises) -> None:
    premises = _get_or_create_copy_premises(current_submission.id)

    premises.named_insured = source_premises.named_insured
    premises.submission_id = current_submission.id
    premises.submission_premises_type = SubmissionPremisesType.COPY
    premises.address = source_premises.address
    premises.organization_id = source_premises.organization_id
    premises.additional_data = source_premises.additional_data
    premises.premises_id = source_premises.premises_id

    db.session.add(premises)


def _get_or_create_copy_premises(submission_id: int) -> SubmissionPremises:
    existing_premises = (
        db.session.query(SubmissionPremises)
        .filter(
            SubmissionPremises.submission_id == submission_id,
            SubmissionPremises.submission_premises_type == SubmissionPremisesType.COPY,
        )
        .order_by(desc(SubmissionPremises.created_at))
        .first()
    )
    return existing_premises or SubmissionPremises()


def _process_relations(
    current_submission: Submission, relations: list[BowheadRelationResult], log: BoundLogger
) -> list[tuple[ReportV2, str]]:
    result = []

    for relation in relations:
        conflict_label = _get_conflict_label(relation.relation_type)
        if conflict_label:
            log.info(
                "BowheadClearing: Marking report as in conflict",
                conflict_report_id=relation.matched_submission.report.id,
                conflict_label=conflict_label,
            )
            result.append((relation.matched_submission.report, conflict_label))
        elif relation.relation_type == BowheadRelationType.RENEWAL:
            log.info("BowheadClearing: Marking current submission as renewal")
            current_submission.is_renewal = True

    return result


def _get_conflict_label(relation_type: BowheadRelationType) -> str | None:
    conflict_mapping = {
        BowheadRelationType.CONFLICT_FOR_SAME_BROKER: SAME_BROKER_CONFLICT_LABEL,
        BowheadRelationType.CONFLICT_FOR_DIFFERENT_BROKER: DIFFERENT_BROKER_CONFLICT_LABEL,
    }
    return conflict_mapping.get(relation_type)
