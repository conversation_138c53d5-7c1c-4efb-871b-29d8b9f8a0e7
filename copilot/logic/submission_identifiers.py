from infrastructure_common.logging import get_logger
from sqlalchemy import or_
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.submission_client_id import SubmissionClientIdSource
import flask

from copilot.models import Organization, ReportV2, db
from copilot.models.reports import (
    AdditionalIdentifier,
    Submission,
    SubmissionClientId,
    SubmissionIdentifier,
)
from copilot.models.submission_sync import SubmissionSync, SubmissionSyncIdentifiers
from copilot.models.types import AdditionalIdentifierType

ADMIRAL_NON_RENEWAL_POLICY_SUFFIX = "-01"

logger = get_logger()


def _handle_breakdown_of_sync_grouping(
    submission: Submission, identifier: SubmissionIdentifier | AdditionalIdentifier
) -> None:
    org_id = submission.organization_id
    if not Organization.is_bishop_conifer_for_id(org_id):
        return

    if not identifier or not identifier.identifier:
        return

    # for <PERSON><PERSON> it can happen that a submission has multiple quote number and only one of them is valid
    # so in these cases, <PERSON><PERSON> will update the quote num ber of the submission to be the valid one
    # in such cases, we need to update the submission sync and the client_id as well
    # we need to look into submission_sync_identifiers, join with submission_sync and assign
    # the corresponding client_id to the submission

    # first try to find the submission_sync_identifiers with the quote number
    sync_req: SubmissionSync = (
        SubmissionSync.query.join(
            SubmissionSyncIdentifiers, SubmissionSync.id == SubmissionSyncIdentifiers.submission_sync_id
        )
        .filter(
            SubmissionSync.organization_id == org_id,
            SubmissionSyncIdentifiers.quote_numbers == [identifier.identifier],
            SubmissionSync.is_deleted.is_(False),
        )
        .first()
    )

    if not sync_req:
        return

    sync_req.attempt = max(0, sync_req.attempt - 1)
    sync_req.applied = False
    db.session.add(sync_req)

    if not submission.client_submission_ids:
        from copilot.v3.controllers.submissions import add_client_submission_id

        add_client_submission_id(
            submission_id=str(submission.id),
            body={
                "client_submission_id": sync_req.policy_number,
                "submission_id": str(submission.id),
                "source": SubmissionClientIdSource.MANUAL.value,
            },
        )
    else:
        client_id: SubmissionClientId = submission.client_submission_ids[0]
        client_id.client_submission_id = sync_req.policy_number
        db.session.add(client_id)
        db.session.commit()
        db.session.refresh(submission)


def _handle_existing_quote_number(
    submission: Submission, identifier: SubmissionIdentifier | AdditionalIdentifier
) -> None:
    org_id = submission.organization_id
    if not Organization.is_bishop_conifer_for_id(org_id):
        return

    # check if quote_number is already assigned to a sub
    #   If yes, check if shell:
    #       if no: raise conflict
    #       if yes:
    #           remove existing quote number, move existing client_ids, remove shell,
    #           reset rows in sync (based on client id and quote number)

    existing_quote_number = (
        db.session.query(SubmissionIdentifier)
        .join(Submission)
        .join(Submission.report)
        .filter(
            SubmissionIdentifier.identifier == identifier.identifier,
            SubmissionIdentifier.identifier_type == AdditionalIdentifierType.QUOTE_NUMBER.value,
            Submission.is_deleted.is_not(True),
            ReportV2.is_deleted.is_not(True),
            ReportV2.organization_id == org_id,
        )
        .first()
    )
    if not existing_quote_number:
        return

    log = logger.bind(submission_id=submission.id, quote_number=identifier.identifier)
    conflicting_submission: Submission = Submission.query.get(existing_quote_number.submission_id)
    if not conflicting_submission.is_shell_from_sync:
        log.warning("Quote number conflict")
        flask.abort(409, "Quote number is already assigned to a different submission")

    # if yes: remove existing quote number, move existing client_ids, remove shell,
    #         reset rows in sync (based on client id and quote number)
    log.info("Found shell with requested quote number")
    db.session.delete(existing_quote_number)
    client_submission_ids_to_reset_sync_for = []
    for sci in conflicting_submission.client_submission_ids:
        sci.submission_id = submission.id
        sci.source = SubmissionClientIdSource.MANUAL
        client_submission_ids_to_reset_sync_for.append(sci.client_submission_id)
    conflicting_submission.report.is_deleted = True
    conflicting_submission.is_deleted = True
    db.session.query(SubmissionSync).filter(
        SubmissionSync.organization_id == org_id,
        or_(
            SubmissionSync.policy_number.in_(client_submission_ids_to_reset_sync_for),
            SubmissionSync.id.in_(
                db.session.query(SubmissionSyncIdentifiers.submission_sync_id).filter(
                    SubmissionSyncIdentifiers.quote_numbers.any(identifier.identifier)
                )
            ),
        ),
    ).update({SubmissionSync.applied: False}, synchronize_session=False)


def patch_identifiers(
    submission: Submission, additional_identifiers: list[SubmissionIdentifier | AdditionalIdentifier]
):
    submission_id: str = str(submission.id)
    for additional_identifier in additional_identifiers:
        identifier_type = AdditionalIdentifierType.OTHER
        identifier = additional_identifier.identifier
        if isinstance(additional_identifier, SubmissionIdentifier):
            identifier_type = AdditionalIdentifierType(additional_identifier.identifier_type)
        if isinstance(additional_identifier, AdditionalIdentifier):
            identifier_type = additional_identifier.type or identifier_type

        if identifier_type == AdditionalIdentifierType.QUOTE_NUMBER:
            _handle_existing_quote_number(submission, additional_identifier)
            _handle_breakdown_of_sync_grouping(submission, additional_identifier)

        should_delete = bool(not identifier)
        existing_identifier = SubmissionIdentifier.query.filter(
            SubmissionIdentifier.submission_id == submission_id,
            SubmissionIdentifier.identifier_type == identifier_type,
        ).first()

        if existing_identifier:
            if should_delete:
                if submission.identifiers == [existing_identifier] and not submission.client_submission_ids:
                    submission_sync_ids = db.session.query(SubmissionSyncIdentifiers.submission_sync_id).filter(
                        or_(
                            SubmissionSyncIdentifiers.policy_numbers.any(existing_identifier.identifier),
                            SubmissionSyncIdentifiers.quote_numbers.any(existing_identifier.identifier),
                        ),
                    )
                    db.session.query(SubmissionSync).filter(
                        SubmissionSync.id.in_(submission_sync_ids),
                        SubmissionSync.organization_id == submission.organization_id,
                    ).update(
                        {"applied": False},
                        synchronize_session=False,
                    )
                    db.session.commit()
                db.session.delete(existing_identifier)
            else:
                existing_identifier.identifier = identifier
        elif identifier:
            db.session.add(
                SubmissionIdentifier(
                    submission_id=submission_id,
                    identifier_type=identifier_type.value,
                    identifier=identifier,
                )
            )

        # special Admiral logic
        if (
            submission.organization_id == ExistingOrganizations.AdmiralInsuranceGroup.value
            and identifier_type == AdditionalIdentifierType.POLICY_NUMBER
            and identifier
            and not identifier.endswith(ADMIRAL_NON_RENEWAL_POLICY_SUFFIX)
        ):
            submission.is_renewal = True
