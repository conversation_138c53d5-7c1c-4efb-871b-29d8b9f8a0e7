from dataclasses import dataclass
from datetime import datetime
from enum import IntEnum

from dateutil.relativedelta import relativedelta
from infrastructure_common.logging import get_logger
from sqlalchemy import func
from sqlalchemy.orm import Session
from structlog.stdlib import BoundLogger

from copilot.logic.dao.submission_dao import SubmissionDAO
from copilot.models import Submission, db
from copilot.models.submission_premises import SubmissionPremises

logger = get_logger()


DAYS_WINDOW = 90


class BowheadRelationType(IntEnum):
    RENEWAL = 1
    """
    Same broker, insured and address but with eff date minus 1 year +-90 days
    """
    CONFLICT_FOR_SAME_BROKER = 2
    """
    Same broker, insured and address and eff date +- 90 days
    """
    CONFLICT_FOR_DIFFERENT_BROKER = 3
    """
    Different broker, insured and address and eff date +- 90 days
    """
    NO_CONFLICT_BUT_EXISTING_ACCOUNT = 4
    """
    Insured and address exist but with eff date outside of +- days
    """
    NO_CONFLICT_AND_NEW_ACCOUNT = 5
    """
    Completely new insured and address
    """


@dataclass
class BowheadRelationResult:
    current_submission: Submission
    matched_submission: Submission | None
    matched_submission_premises: SubmissionPremises | None
    relation_type: BowheadRelationType


class BowheadService:
    @staticmethod
    def analyze_relations(
        current_submission: Submission,
        matched_submission_premises: list[SubmissionPremises],
        bound_log: BoundLogger | None = None,
    ) -> list[BowheadRelationResult]:
        # This method goes over every unique submission in matched (by premises) and determines relation to
        # currently analyzed submission (let's say this is newly verified and cleared submission).
        #
        # The rules are:
        #
        # 1. No other matching subs -> this is no conflict and new account (new insured name and address)
        # 2. There is matching submission with same broker (and insured and address) but with effective date
        #    -1 year (from current submission eff date) in the past +- 90 days -> current is renewal
        # 3. There is matching submission with same insured, address and broker with eff date +- 90 days ->
        #    we have a conflict but for the same broker.
        # 4. There is matching submission with same insured, address but different broker with eff date +- 90 days ->
        #    we have a conflict but for different broker.
        # 5. No other point above applied -> we already have insured and address but with eff date outside
        #    +- 90 days (broker matching or not doesn't matter) -> no conflict but for existing account

        log = BowheadService._get_logger(bound_log).bind(
            submission_id=str(current_submission.id), report_id=str(current_submission.report.id)
        )

        if not matched_submission_premises:
            log.info("BowheadService: No matched submission premises, returning NO_CONFLICT_BUT_EXISTING_ACCOUNT")
            return [
                BowheadService._create_result(
                    current_submission, None, None, BowheadRelationType.NO_CONFLICT_AND_NEW_ACCOUNT
                )
            ]

        visited_subs = set()
        result = []

        for match in matched_submission_premises:
            matched_sub_id = str(match.submission_id)
            if matched_sub_id in visited_subs:
                continue
            visited_subs.add(matched_sub_id)

            matched_sub = SubmissionDAO.get_minimal_submission(
                matched_sub_id,
                raise_404=True,
                include_report=True,
                additional_fields=["name"],
                report_additional_fields=["name", "is_deleted"],
            )

            if matched_sub.is_deleted or matched_sub.report.is_deleted:
                log.info("BowheadService: Report is deleted, skipping")
                continue

            relation_type = BowheadService._determine_relation_type(current_submission, matched_sub, log)
            result.append(BowheadService._create_result(current_submission, matched_sub, match, relation_type))

        return result

    @staticmethod
    def get_matching_submission_premises(
        submission: Submission,
        db_session: Session | None = None,
        bound_log: BoundLogger | None = None,
    ) -> list[SubmissionPremises]:
        db_session = BowheadService._get_db_session(db_session)
        log = BowheadService._get_logger(bound_log).bind(
            submission_id=str(submission.id), report_id=str(submission.report.id)
        )

        fni_premises = submission.fni_premises
        if not fni_premises:
            log.info("Submission has no FNI premises")
            return []

        fni_premises_id = fni_premises.premises_id
        fni_business_name = fni_premises.named_insured

        similarity_threshold = 0.65

        matching_submission_premises = (
            db_session.query(SubmissionPremises)
            .filter(
                SubmissionPremises.premises_id == str(fni_premises_id),
                SubmissionPremises.submission_id != submission.id,
                func.similarity(SubmissionPremises.named_insured, fni_business_name) > similarity_threshold,
            )
            .order_by(func.similarity(SubmissionPremises.named_insured, fni_business_name).desc())
            .all()
        )

        if len(matching_submission_premises) == 0:
            log.info("No matching submission premises found")
        else:
            matched_submission_details = {
                (
                    str(sub_premises.submission_id),
                    str(sub_premises.id),
                    sub_premises.submission_premises_type,
                    sub_premises.address,
                    sub_premises.named_insured,
                )
                for sub_premises in matching_submission_premises
            }
            log.info("Found matching submission premises", details=matched_submission_details)

        return matching_submission_premises

    @staticmethod
    def _create_result(
        current_submission: Submission,
        matched_submission: Submission | None,
        matched_premises: SubmissionPremises | None,
        relation_type: BowheadRelationType,
    ) -> BowheadRelationResult:
        return BowheadRelationResult(
            current_submission=current_submission,
            matched_submission=matched_submission,
            matched_submission_premises=matched_premises,
            relation_type=relation_type,
        )

    @staticmethod
    def _determine_relation_type(
        current_submission: Submission, matched_submission: Submission, bound_log: BoundLogger
    ) -> BowheadRelationType:
        if BowheadService._is_renewal_of(current_submission, matched_submission):
            bound_log.info("BowheadService: Renewal detected, returning RENEWAL")
            return BowheadRelationType.RENEWAL
        elif BowheadService._is_in_conflict(current_submission, matched_submission, same_broker=True):
            bound_log.info("BowheadService: Conflict for same broker, returning CONFLICT_FOR_SAME_BROKER")
            return BowheadRelationType.CONFLICT_FOR_SAME_BROKER
        elif BowheadService._is_in_conflict(current_submission, matched_submission, same_broker=False):
            bound_log.info("BowheadService: Conflict for different broker, returning CONFLICT_FOR_DIFFERENT_BROKER")
            return BowheadRelationType.CONFLICT_FOR_DIFFERENT_BROKER
        else:
            bound_log.info("BowheadService: No conflict detected, returning NO_CONFLICT_BUT_EXISTING_ACCOUNT")
            return BowheadRelationType.NO_CONFLICT_BUT_EXISTING_ACCOUNT

    @staticmethod
    def _within_days(d1: datetime, d2: datetime, days: int | None = None) -> bool:
        if days is None:
            days = DAYS_WINDOW
        return abs((d1 - d2).days) <= days

    @staticmethod
    def _is_renewal_of(submission: Submission, other: Submission) -> bool:
        if submission.broker_id != other.broker_id:
            return False

        sub_date, old_date = submission.proposed_effective_date, other.proposed_effective_date
        if not (sub_date and old_date):
            return False

        anniversary = old_date + relativedelta(years=1)
        return BowheadService._within_days(sub_date, anniversary)

    @staticmethod
    def _is_in_conflict(submission: Submission, other: Submission, same_broker: bool) -> bool:
        broker_condition = (
            (submission.broker_id == other.broker_id) if same_broker else (submission.broker_id != other.broker_id)
        )

        # Explicitly check for missing dates and return False
        if not (submission.proposed_effective_date and other.proposed_effective_date):
            return False

        return broker_condition and BowheadService._within_days(
            submission.proposed_effective_date, other.proposed_effective_date
        )

    @staticmethod
    def _get_db_session(db_session: Session) -> Session:
        if db_session is None:
            db_session = db.session
        return db_session

    @staticmethod
    def _get_logger(bound_log: BoundLogger | None) -> BoundLogger:
        if bound_log is None:
            bound_log = logger
        return bound_log
