from dataclasses import dataclass, field
from datetime import datetime
from uuid import UUID

from dataclasses_json import dataclass_json
from intervaltree import Interval
from static_common.enums.enum import StrEnum
from static_common.enums.match import MatchType
from static_common.enums.origin import Origin
from static_common.enums.submission import SubmissionStage

from copilot.models import Submission
from copilot.models.types import CoverageRemovalStrategyType


class SynchronizationHandlerType(StrEnum):
    POLICY_HANDLER = "POLICY_HANDLER"
    SUBMISSION_HANDLER = "SUBMISSION_HANDLER"
    COVERAGE_HANDLER = "COVERAGE_HANDLER"
    UNDERWRITER_HANDLER = "UNDERWRITER_HANDLER"
    RENEWAL_RELATION_HANDLER = "RENEWAL_RELATION_HANDLER"
    SUBMISSION_IDENTIFIERS_HANDLER = "SUBMISSION_IDENTIFIERS_HANDLER"
    REPORT_HANDLER = "REPORT_HANDLER"
    BROKERAGE_HANDLER = "BROKERAGE_HANDLER"
    SUBMISSION_PREMISES_HANDLER = "SUBMISSION_PREMISES_HANDLER"


class MatcherType(StrEnum):
    POLICY_MATCHER = "POLICY_MATCHER"
    RELATED_POLICY_MATCHER = "RELATED_POLICY_MATCHER"
    FUZZY_MATCHER = "FUZZY_MATCHER"
    EMAIL_MATCHER = "EMAIL_MATCHER"
    NAME_SIMILARITY_MATCHER = "NAME_SIMILARITY_MATCHER"
    BUSINESS_NAME_MATCHER = "BUSINESS_NAME_MATCHER"
    ERS_BUSINESS_ID_MATCHER = "ERS_BUSINESS_ID_MATCHER"
    SUBMISSION_IDENTIFIER_MATCHER = "SUBMISSION_IDENTIFIER_MATCHER"


class ScoringPolicy(StrEnum):
    HIGHEST_SCORE = "HIGHEST_SCORE"
    LOWEST_SCORE = "LOWEST_SCORE"
    AVERAGE_SCORE = "AVERAGE_SCORE"


class ScorerType(StrEnum):
    SUBMISSION_NAME_SCORER = "SUBMISSION_NAME_SCORER"
    EMAIL_SCORER = "EMAIL_SCORER"
    RELATED_POLICY_SCORER = "RELATED_POLICY_SCORER"
    BUSINESS_NAME_SCORER = "BUSINESS_NAME_SCORER"
    ERS_BUSINESS_ID_SCORER = "ERS_BUSINESS_ID_SCORER"


class SubmissionMatchFilterType(StrEnum):
    BROKERAGE_FILTER = "BROKERAGE_FILTER"
    BROKER_FILTER = "BROKER_FILTER"


class ReferenceDateType(StrEnum):
    CREATED_AT = "CREATED_AT"
    EFFECTIVE_DATE = "EFFECTIVE_DATE"


@dataclass_json
@dataclass
class SyncHandlerConfig:
    handler_type: SynchronizationHandlerType
    config: dict = field(default_factory=dict)


@dataclass_json
@dataclass
class SubmissionMatcherConfig:
    # days to look back in the bast
    created_at_days_range: int = 60
    # days to look forward in the future
    created_at_upper_bound_days: int = 15
    effective_date_days_range: int = 30
    effective_date_upper_bound_days: int = 15
    query_similarity_threshold: float = 0.45
    exclude_sync_origin: bool = False
    fuzzy_matching_level: int = 0
    exclude_submissions_with_client_ids: bool = True
    # Changes the upper bound for created at date for a submission
    renewal_received_date_days_before: int = 0
    additional_sub_sync_fields: list[str] = field(default_factory=list)
    additional_dba_splitters: list[str] = field(default_factory=list)
    identifier_type: str | None = None


@dataclass_json
@dataclass
class ScoringOverrides:
    use_broker_info: bool | None = None


@dataclass_json
@dataclass
class ScorerConfig:
    score_boost: float = 0.0
    calculate_score_penalty: bool = False
    # if the final score is bellow the threshold do not calculate penalty
    penalty_calculation_threshold: float = 0.7
    matcher_types: set[MatcherType] = field(default_factory=set)
    additional_dba_splitters: list[str] = field(default_factory=list)
    use_broker_info: bool = False
    date_distance_map: dict[str, float] = field(
        default_factory=lambda: {
            "0-7": 0.0,
            "7-14": 0.05,
            "14-21": 0.1,
            "21-30": 0.15,
            "30-40": 0.2,
            "40-50": 0.25,
            "50-60": 0.3,
            "60-90": 0.5,
            "90-120": 0.8,
            "120-99999": 1.0,
        }
    )

    @property
    def date_interval_distance_map(self) -> dict[Interval, float]:
        result = {}
        for interval, distance in self.date_distance_map.items():
            start, end = interval.split("-")
            result[Interval(int(start), int(end))] = distance
        return result


@dataclass_json
@dataclass
class MatcherDefinition:
    matcher_type: MatcherType
    config: SubmissionMatcherConfig = field(default_factory=lambda: SubmissionMatcherConfig())


@dataclass_json
@dataclass
class ScorerDefinition:
    config: ScorerConfig
    scorer_type: ScorerType


@dataclass_json
@dataclass
class LookupConfig:
    # some clients send renewal submissions before we receive it in copilot
    # in that case, a shell will be created for the renewal submission. When matching the shell will be returned
    # this flag will be used to determine if we should still try and match to real submission despite the shell
    try_renewal_match: bool = False
    matching_score_threshold: float = 0.7
    # if we managed to match only one submission or the score difference between the first and the second is more
    # than the threshold, we should return the first submission as a match
    standout_submission_enabled: bool = False
    standout_submission_threshold: float = 0.55
    scoring_policy = ScoringPolicy.HIGHEST_SCORE
    reference_date_type: ReferenceDateType = ReferenceDateType.CREATED_AT
    create_suggestions: bool = False
    # if this is True, we will apply special logic when we encounter grouped sync requests
    # if we are synchronizing the parent of a group, we will run only the policy matcher
    # When running the sync for the children we would run only the direct matchers. If it happens that
    # one of the children has a match and the parent does not or is matched to a shell, we delete the parent
    use_grouping: bool = True
    direct_matchers: list[MatcherDefinition] = field(
        default_factory=lambda: [MatcherDefinition(matcher_type=MatcherType.POLICY_MATCHER)]
    )
    indirect_matchers: list[MatcherDefinition] = field(
        default_factory=lambda: [
            MatcherDefinition(matcher_type=MatcherType.RELATED_POLICY_MATCHER),
            MatcherDefinition(matcher_type=MatcherType.FUZZY_MATCHER),
            MatcherDefinition(matcher_type=MatcherType.EMAIL_MATCHER),
            MatcherDefinition(matcher_type=MatcherType.NAME_SIMILARITY_MATCHER),
            MatcherDefinition(matcher_type=MatcherType.BUSINESS_NAME_MATCHER),
        ]
    )
    scoring_overrides: ScoringOverrides = field(default_factory=lambda: ScoringOverrides())
    scorers: list[ScorerDefinition] = field(
        default_factory=lambda: [
            ScorerDefinition(
                config=ScorerConfig(
                    matcher_types={MatcherType.FUZZY_MATCHER, MatcherType.NAME_SIMILARITY_MATCHER},
                    calculate_score_penalty=True,
                ),
                scorer_type=ScorerType.SUBMISSION_NAME_SCORER,
            ),
            ScorerDefinition(
                config=ScorerConfig(matcher_types={MatcherType.EMAIL_MATCHER}),
                scorer_type=ScorerType.EMAIL_SCORER,
            ),
            ScorerDefinition(
                config=ScorerConfig(matcher_types={MatcherType.RELATED_POLICY_MATCHER}),
                scorer_type=ScorerType.RELATED_POLICY_SCORER,
            ),
            ScorerDefinition(
                config=ScorerConfig(
                    matcher_types={MatcherType.BUSINESS_NAME_MATCHER},
                    calculate_score_penalty=True,
                ),
                scorer_type=ScorerType.BUSINESS_NAME_SCORER,
            ),
        ]
    )


@dataclass_json
@dataclass
class SubmissionMatchFilterConfig:
    cutoff_threshold: float


@dataclass_json
@dataclass
class ShellCreationRules:
    # statuses for which we want to create shells.
    # there are technically three groups of sync requests
    # 1. group parents
    # 2. group children
    # 3. individual sync requests
    group_parents_stages: set[SubmissionStage] = field(default_factory=lambda: {s for s in SubmissionStage})
    group_children_stages: set[SubmissionStage] = field(default_factory=lambda: {s for s in SubmissionStage})
    individual_sync_requests_stages: set[SubmissionStage] = field(default_factory=lambda: {s for s in SubmissionStage})


@dataclass_json
@dataclass
class SubmissionMatchFilterDefinition:
    config: SubmissionMatchFilterConfig
    filter_type: SubmissionMatchFilterType


@dataclass_json
@dataclass
class SubmissionProducerConfiguration:
    shell_owner_email: str
    lookup_config: LookupConfig = field(default_factory=lambda: LookupConfig())
    duplicate_submissions: bool = False
    original_min_days_old: int = 2
    original_max_days_old: int = 6
    create_shell_submissions: bool = False
    enable_multiple_matches: bool = True
    shell_submissions_days_passed: int = 7
    skip_submission_readiness_check: bool = False
    remove_assignees_after_duplication: bool = True
    # number of days after Submission.created_at after which not ready subs are synced
    always_ready_after_num_days: int = 7300  # 20 years
    filters: list[SubmissionMatchFilterDefinition] = field(default_factory=list)
    # Only create sync shells for renewals if effective date is within x days of current date.
    # Set to big number so we always create the shell
    renewal_days_from_effective_date_for_shells: int = 1000
    allowed_matchers_to_duplicate: set[MatcherType] = field(
        default_factory=lambda: {MatcherType.RELATED_POLICY_MATCHER}
    )
    # Days before effective date to create shell submissions if the submission is not found
    create_shell_submissions_days_before_effective_date: int = 2
    shells_received_date_cutoff: datetime | None = None
    shells_eff_date_cutoff: datetime | None = None
    shell_creation_rules: ShellCreationRules = field(default_factory=lambda: ShellCreationRules())


@dataclass
class MatchingInformation:
    matcher_type: MatcherType
    matching_score: float


@dataclass
class SubmissionHandlerOutput:
    handler_type: SynchronizationHandlerType
    error: str | None = None


@dataclass
class SubmissionChanges:
    handler_outputs: list[SubmissionHandlerOutput] = field(default_factory=list)
    before_sync: dict = field(default_factory=dict)
    after_sync: dict = field(default_factory=dict)

    @property
    def has_sync_error(self) -> bool:
        return any(handler_output.error for handler_output in self.handler_outputs)


@dataclass
class CloseMatch:
    submission_id: UUID
    report_id: UUID
    submission_name: str
    matching_score: float


@dataclass
class SubmissionMatcherResult:
    match_type: MatchType
    submission_id: UUID | None = None
    submission_ids: list[UUID] | None = None
    submission_name: str | None = None
    submission_stage: SubmissionStage | None = None
    report_id: UUID | None = None
    original_report_id: UUID | None = None
    original_submission_id: UUID | None = None
    is_shell: bool = False
    is_duplicate: bool = False
    is_submission_ready: bool = True
    submission: Submission | None = None
    matching_information: MatchingInformation | None = None
    close_matches: list[CloseMatch] | None = None

    @property
    def all_matches(self) -> list[CloseMatch]:
        result = []
        if self.submission_id and not self.is_shell:
            found_match = CloseMatch(
                self.submission_id,
                self.report_id,
                self.submission_name,
                self.matching_information.matching_score,
            )
            result.append(found_match)
        result.extend(self.close_matches or [])
        return result


@dataclass
class SynchronizationResult:
    request_id: UUID | str
    policy_number: str
    organization_id: int
    match_result: SubmissionMatcherResult | None = None
    matching_error: bool = False
    synchronization_error: bool = False
    already_synchronized: bool = False
    synced_at: datetime = field(default_factory=datetime.now)
    submission_changes: SubmissionChanges | None = None
    run_id: str | None = None


# for now keeping it simple, but this could evolve in rules whether to process the matched submission or not
# as in process only if submission.is_shell_from_sync is true
# or combine rules such as submission is in certain stage and it contains businesses. Something similar to
# recommendation rules, but hopefully simpler.
@dataclass_json
@dataclass
class HandlerConfig:
    process_only_sync_shells: bool = False


@dataclass_json
@dataclass
class UnderwriterHandlerConfig(HandlerConfig):
    remove_previous_assignees: bool = False
    default_primary_naics: dict = field(default_factory=dict)
    only_assign_if_no_assignees: bool = False
    always_run_handler: bool = False
    # If export row has 'null' UW and this is True, we will unassign UW from the submission
    treat_null_as_unassigned: bool = False
    # If export row has UW which is not in Copilot and this is True, we will unassign UW from the submission
    unassign_if_not_in_copilot: bool = False


@dataclass_json
@dataclass
class StageTransition:
    from_stages: set[SubmissionStage] = field(default_factory=set)
    to_stages: set[SubmissionStage] = field(default_factory=set)

    def contains_transition(self, from_stage: SubmissionStage, to_stage: SubmissionStage):
        return from_stage in self.from_stages and to_stage in self.to_stages


@dataclass_json
@dataclass
class StageTransitions:
    allowed_stage_transitions: list[StageTransition] = field(default_factory=list)
    not_allowed_stage_transitions: list[StageTransition] = field(default_factory=list)

    def is_stage_transition_allowed(self, from_stage: SubmissionStage | None, to_stage: SubmissionStage) -> bool:
        if not from_stage:
            return True

        if not to_stage:
            return False

        if self.allowed_stage_transitions:
            return any(t.contains_transition(from_stage, to_stage) for t in self.allowed_stage_transitions)

        if self.not_allowed_stage_transitions:
            return not any(t.contains_transition(from_stage, to_stage) for t in self.not_allowed_stage_transitions)

        return True


@dataclass_json
@dataclass
class CoverageHandlerConfig(HandlerConfig):
    removal_strategy: CoverageRemovalStrategyType | None = None
    coverages: list[str] | None = None
    allow_no_coverages: bool = True
    stage_transitions: StageTransitions | None = None


@dataclass_json
@dataclass
class SubmissionHandlerConfig(HandlerConfig):
    allow_backwards_status_update: bool = True
    # if any of these lists are not empty, the 'allow_backwards_status_update' setting is ignored
    allowed_stage_transitions: list[StageTransition] = field(default_factory=list)
    not_allowed_stage_transitions: list[StageTransition] = field(default_factory=list)


@dataclass_json
@dataclass
class SubmissionSyncProcessorConfiguration:
    max_attempts: int = 0
    continue_on_handler_error: bool = True
    submission_producer_config: SubmissionProducerConfiguration = field(
        default_factory=lambda: SubmissionProducerConfiguration(shell_owner_email="")
    )
    handlers: list[SyncHandlerConfig] = field(default_factory=list)


@dataclass_json
@dataclass
class MatcherDryRunRequest:
    ids: list[UUID]
    lookup_config: LookupConfig


@dataclass
class RunSyncRequest:
    ids: list[UUID]
    run_id: str | None = None


@dataclass
class DeleteSyncRequests:
    client_ids: list[str]
    organization_id: int


@dataclass
class SubmissionMatch:
    submission_id: UUID
    report_id: UUID
    submission: Submission
    matcher: MatcherType | None = None
    matching_score: float | None = None

    @property
    def adjusted_score(self) -> float:
        # we want to sort by score.
        # if scores are same we want to prioritize related policy matches
        # if scores and matcher are same we want to have submission with Sync origin last
        if not self.matching_score:
            return 0.0
        score = self.matching_score
        if self.matcher == MatcherType.RELATED_POLICY_MATCHER:
            score = score + 0.01
        if self.submission.origin == Origin.SYNC:
            score = score - 0.0001
        return score


@dataclass
class SubmissionLookupResult:
    found_sync_shell: SubmissionMatch | None = None
    matches: list[SubmissionMatch] = field(default_factory=list)
    discarded_matches: list[SubmissionMatch] = field(default_factory=list)

    @property
    def close_matches(self) -> list[CloseMatch]:
        close_matches = [
            cm for cm in self.discarded_matches if not cm.submission.client_submission_ids and cm.matching_score > 0
        ]

        close_submission_matches = [
            CloseMatch(
                submission_match.submission.id,
                submission_match.submission.report_id,
                submission_match.submission.name,
                submission_match.matching_score,
            )
            for submission_match in close_matches
        ]
        return close_submission_matches


@dataclass
class SubmissionProducerResult:
    match_result: SubmissionMatcherResult | None = None
    shell_to_delete: SubmissionMatch | None = None
