from dataclasses import dataclass, field
from datetime import datetime, timedelta
from functools import cached_property
from operator import and_
from typing import Optional
from uuid import UUID
import hashlib
import json

from dataclasses_json import dataclass_json
from entity_resolution_service_client import Premises as PremisesV1
from entity_resolution_service_client_v3 import (
    Entity,
    EntityNameRequest,
    EntityPremisesRequest,
    EntityRequest,
    EntitySearchRequest,
)
from entity_resolution_service_client_v3 import Premises as PremisesV3
from entity_resolution_service_client_v3 import SearchEntityResults
from infrastructure_common.logging import get_logger
from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    Enum,
    Float,
    ForeignKey,
    Index,
    Integer,
    String,
    sql,
)
from sqlalchemy.dialects import postgresql
from sqlalchemy.dialects.postgresql import ARRAY, JSONB
from sqlalchemy.dialects.postgresql import UUID as SA_UUID
from sqlalchemy.ext.mutable import MutableList
from sqlalchemy.orm import foreign, relationship, remote
from static_common.enums.contractor import ContractorSubmissionType
from static_common.enums.insurance import ProjectInsuranceType
from static_common.enums.submission import SubmissionStage
from static_common.enums.submission_sync import SyncMatchingRunStatus
import flask

from copilot.clients.ers import ERSClient
from copilot.clients.ers_v3 import ERSClientV3
from copilot.logic.synchronization.configuration import get_sync_config_map
from copilot.models import BaseModel
from copilot.models._private import ImmutableBaseModel
from copilot.models.synchronization.submissions import (
    MatcherType,
    SubmissionProducerConfiguration,
)
from copilot.models.types import CoverageType

logger = get_logger()


class SubmissionSyncReport(BaseModel):
    __tablename__ = "submission_sync_report"
    org_group = Column(String, nullable=True)
    submission_sync_id = Column(
        SA_UUID(as_uuid=True),
        ForeignKey("submission_sync.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    submission_sync = relationship("SubmissionSync", uselist=False, back_populates="sync_report")


class SubmissionSyncIdentifiers(BaseModel):
    __tablename__ = "submission_sync_identifiers"
    quote_numbers = Column(MutableList.as_mutable(ARRAY(String)), nullable=False)
    policy_numbers = Column(MutableList.as_mutable(ARRAY(String)), nullable=False)
    submission_sync_id = Column(
        SA_UUID(as_uuid=True),
        ForeignKey("submission_sync.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    submission_sync = relationship("SubmissionSync", uselist=False, back_populates="sync_identifiers")

    @property
    def quote_numbers_as_string(self) -> str:
        return ", ".join(sorted(self.quote_numbers))

    @property
    def policy_numbers_as_string(self) -> str:
        return ", ".join(sorted(self.policy_numbers))

    @property
    def first_quote_number(self) -> str:
        return self.quote_numbers[0] if self.quote_numbers else ""

    @property
    def first_policy_number(self) -> str:
        return self.policy_numbers[0] if self.policy_numbers else ""


class SubmissionSyncMatcherData(BaseModel):
    # stores additional matcher data. For now only used by the ERSBusinessMatcher
    __tablename__ = "submission_sync_matcher_data"
    named_insured = Column(String, nullable=False)
    named_insured_address = Column(String, nullable=False)
    submission_sync_id = Column(
        SA_UUID(as_uuid=True),
        ForeignKey("submission_sync.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    additional_data = Column(JSONB, nullable=True)
    submission_sync = relationship("SubmissionSync", uselist=False, back_populates="matcher_data")

    @cached_property
    def premises(self) -> PremisesV3 | PremisesV1 | None:
        entity = self.entity
        if entity:
            return entity.premises[0].premises

        if not self.named_insured_address:
            return None

        ers_client: ERSClient = flask.current_app.ers_client
        try:
            premises = ers_client.post_premises(self.named_insured_address)
            return premises
        except:
            logger.exception("Cannot post premises in ERS", name=self.named_insured, address=self.named_insured_address)
            return None

    @cached_property
    def entity(self) -> Entity | None:
        ers_client: ERSClientV3 = flask.current_app.ers_client_v3
        if not self.named_insured or not self.named_insured_address:
            return None
        try:
            search_results: list[SearchEntityResults] = ers_client.search(
                EntitySearchRequest(
                    entities=[
                        EntityRequest(
                            names=[EntityNameRequest(value=self.named_insured)],
                            premises=[EntityPremisesRequest(address=self.named_insured_address)],
                        )
                    ],
                    high_confidence_only=True,
                )
            )
            if not search_results or not search_results[0].data:
                return None
            entity = search_results[0].data[0].entity
            return entity
        except:
            logger.exception(
                "Cannot get_or_create_entity from ERS", name=self.named_insured, address=self.named_insured_address
            )
            return None

    @cached_property
    def premises_id(self) -> UUID | None:
        premises = self.premises
        if not premises:
            return None
        return UUID(premises.id)

    @cached_property
    def business_id(self) -> UUID | None:
        entity = self.entity
        if not entity:
            return None
        return UUID(entity.id)


class SubmissionSync(ImmutableBaseModel):
    LAST_CHANGE_PROPERTIES = {
        "stage",
        "client_stage_id",
        "received_date",
    }

    __tablename__ = "submission_sync"

    policy_number = Column(String, nullable=False, index=True, unique=True)
    submission_name = Column(String, nullable=False)
    underwriter_email = Column(String, nullable=True)
    stage = Column(Enum(SubmissionStage), nullable=True)
    received_date = Column(DateTime(), nullable=True)
    effective_date = Column(DateTime(), nullable=True)
    coverage_name = Column(String, nullable=True)
    coverage_type = Column(Enum(CoverageType, native_enum=False), nullable=True)
    quoted_premium = Column(Float, nullable=True)
    bound_premium = Column(Float, nullable=True)
    organization_id = Column(Integer, ForeignKey("organization.id"), nullable=False, index=True)
    applied_changes = Column(MutableList.as_mutable(JSONB))
    pending_changes = Column(MutableList.as_mutable(JSONB))
    applied = Column(Boolean, nullable=False, default=False)
    attempt = Column(Integer, nullable=False, default=0)
    declined_date = Column(DateTime(), nullable=True)
    source = Column(String, nullable=False)
    account_id = Column(String, nullable=True)
    is_renewal = Column(Boolean, nullable=True)
    brokerage = Column(String, nullable=True)
    broker = Column(String, nullable=True)
    coverages = Column(MutableList.as_mutable(JSONB), nullable=False)
    direct_match_only = Column(Boolean, nullable=False, default=False)
    matcher_data = relationship(
        "SubmissionSyncMatcherData",
        uselist=False,
        cascade="all, delete-orphan",
        lazy="joined",
        back_populates="submission_sync",
    )
    created_date = Column(DateTime(), nullable=True)
    quoted_date = Column(DateTime(), nullable=True)
    bound_date = Column(DateTime(), nullable=True)
    contractor_submission_type = Column(Enum(ContractorSubmissionType), nullable=True)
    project_insurance_type = Column(Enum(ProjectInsuranceType), nullable=True)
    client_id_being_renewed = Column(String, nullable=True)
    broker_email = Column(String, nullable=True)
    premium = Column(Float, nullable=True)
    expired_premium = Column(Float, nullable=True)
    primary_state = Column(String, nullable=True)
    client_stage_id = Column(Integer, nullable=True)
    user_groups_to_match = Column(ARRAY(String), nullable=True)
    policy_number_being_renewed = Column(String, nullable=True)
    policy_status = Column(String, nullable=True)
    policy_expiration_date = Column(DateTime(), nullable=True)
    # date in which the sync requests was first seen in an export
    first_seen = Column(DateTime(), nullable=True)
    replaces_client_id = Column(String, nullable=True)
    replaced_by_client_id = Column(String, nullable=True)
    is_deleted = Column(Boolean, nullable=False, default=False, server_default=sql.false())
    deleted_at = Column(DateTime(), nullable=True)
    parent_client_id = Column(String, nullable=True, index=True)
    submission_sync_parent = relationship(
        "SubmissionSync",
        uselist=False,
        primaryjoin=and_(
            remote(policy_number) == foreign(parent_client_id), remote(organization_id) == foreign(organization_id)
        ),
    )
    submission_sync_children = relationship(
        "SubmissionSync",
        uselist=True,
        primaryjoin=and_(
            remote(parent_client_id) == foreign(policy_number), remote(organization_id) == foreign(organization_id)
        ),
        overlaps="submission_sync_parent",
    )
    sync_identifiers = relationship(
        "SubmissionSyncIdentifiers",
        uselist=False,
        cascade="all, delete-orphan",
        lazy="joined",
        back_populates="submission_sync",
    )
    sync_report = relationship(
        "SubmissionSyncReport",
        uselist=False,
        cascade="all, delete-orphan",
        lazy="select",
        back_populates="submission_sync",
    )

    sync_matching_results = relationship(
        "SyncMatchingResult",
        uselist=True,
        cascade="all, delete-orphan",
        lazy="select",
        back_populates="submission_sync",
        primaryjoin="SyncMatchingResult.submission_sync_id==SubmissionSync.id",
        order_by="desc(SyncMatchingResult.created_at)",
    )

    __table_args__ = (
        Index("ix_submission_sync_policy_number_organization_id", "policy_number", "organization_id", unique=True),
    )

    _is_new = False

    @property
    def is_new(self) -> bool:
        return self._is_new

    @is_new.setter
    def is_new(self, value: bool):
        self._is_new = value

    @property
    def replaced_by_submission_sync(self) -> "SubmissionSync | None":
        return SubmissionSync.query.filter(
            SubmissionSync.policy_number == self.replaced_by_client_id,
            SubmissionSync.organization_id == self.organization_id,
        ).one_or_none()

    @property
    def replaces_submission_sync(self) -> "SubmissionSync | None":
        return SubmissionSync.query.filter(
            SubmissionSync.policy_number == self.replaces_client_id,
            SubmissionSync.organization_id == self.organization_id,
        ).one_or_none()

    def get_sync_matching_result(self, run_id: str) -> "SyncMatchingResult | None":
        for result in self.sync_matching_results or []:
            if str(result.sync_matching_run_id) == run_id:
                return result
        return None

    @cached_property
    def is_group_parent(self) -> bool:
        return bool(self.submission_sync_children)

    @property
    def is_group_child(self) -> bool:
        return bool(self.submission_sync_parent)

    @property
    def is_individual_reqeust(self) -> bool:
        return not self.is_group_parent and not self.is_group_child

    @property
    def submission_created_at_reference(self) -> datetime:
        return self.created_date or self.received_date or self.first_seen

    def get_next(self) -> Optional["SubmissionSync"]:
        return self._get_nth_from_pending(0)

    def can_shift(self) -> bool:
        # for now just check if this change has been applied
        return self.applied and self.pending_changes

    def shift(self, force: bool = False) -> bool:
        if not force and not self.can_shift():
            return False
        next_change = self.get_next()
        if not next_change:
            return False
        if self.applied_changes is None:
            self.applied_changes = []
        self.applied_changes.append(self.as_dict())
        self._update_values(next_change)
        self.pending_changes.pop(0)
        return True

    def as_dict(self) -> dict:
        from copilot.schemas.submission_sync import SubmissionSyncSchema

        current_as_dict = SubmissionSyncSchema(exclude=("applied_changes", "pending_changes")).dump(self)
        current_as_dict["hash"] = self.hash
        return current_as_dict

    def _get_nth_from_pending(self, n: int) -> Optional["SubmissionSync"]:
        from copilot.schemas.submission_sync import SubmissionSyncSchema

        return SubmissionSyncSchema().load(self.pending_changes[n]) if self.pending_changes else None

    def _update_values(self, other: "SubmissionSync"):
        self.created_at = other.created_at
        self.submission_name = other.submission_name
        self.underwriter_email = other.underwriter_email
        self.stage = other.stage
        self.received_date = other.received_date
        self.effective_date = other.effective_date
        self.coverage_name = other.coverage_name
        self.coverage_type = other.coverage_type
        self.quoted_premium = other.quoted_premium
        self.bound_premium = other.bound_premium
        self.broker = other.broker
        self.brokerage = other.brokerage
        self.attempt = other.attempt
        self.declined_date = other.declined_date
        self.applied = other.applied
        self.source = other.source
        self.account_id = other.account_id
        self.is_renewal = other.is_renewal
        self.coverages = other.get_coverages_dict_array()
        self.direct_match_only = other.direct_match_only
        self.created_date = other.created_date
        self.quoted_date = other.quoted_date
        self.bound_date = other.bound_date
        self.contractor_submission_type = other.contractor_submission_type
        self.project_insurance_type = other.project_insurance_type
        self.client_id_being_renewed = other.client_id_being_renewed
        self.broker_email = other.broker_email
        self.premium = other.premium
        self.expired_premium = other.expired_premium
        self.primary_state = other.primary_state
        self.client_stage_id = other.client_stage_id
        self.user_groups_to_match = other.user_groups_to_match
        self.policy_number_being_renewed = other.policy_number_being_renewed
        self.policy_status = other.policy_status
        self.policy_expiration_date = other.policy_expiration_date

    @property
    def hash(self) -> str:
        coverages = self.get_coverages_object_array()
        coverages_hash = "-".join(cov.hash for cov in coverages)
        string_to_hash = (
            f"{self.policy_number}-{self.submission_name}-{self.underwriter_email}-{self.stage}-"
            f"{self.received_date}-{self.effective_date}-{coverages_hash}-{self.organization_id}-{self.account_id}-"
            f"{self.is_renewal}"
        )
        # we do not want to change the hash just by adding new properties, we only add them to the
        # hash string if they have value
        if self.created_date is not None:
            string_to_hash = f"{string_to_hash}-{self.created_date}"
        if self.quoted_date is not None:
            string_to_hash = f"{string_to_hash}-{self.quoted_date}"
        if self.bound_date is not None:
            string_to_hash = f"{string_to_hash}-{self.bound_date}"
        if self.project_insurance_type is not None:
            string_to_hash = f"{string_to_hash}-{self.project_insurance_type}"
        if self.contractor_submission_type is not None:
            string_to_hash = f"{string_to_hash}-{self.contractor_submission_type}"
        if self.client_id_being_renewed is not None:
            string_to_hash = f"{string_to_hash}-{self.client_id_being_renewed}"
        if self.broker_email is not None:
            string_to_hash = f"{string_to_hash}-{self.broker_email}"
        if self.premium is not None:
            string_to_hash = f"{string_to_hash}-{self.premium}"
        if self.expired_premium is not None:
            string_to_hash = f"{string_to_hash}-{self.expired_premium}"
        if self.primary_state is not None:
            string_to_hash = f"{string_to_hash}-{self.primary_state}"
        if self.client_stage_id is not None:
            string_to_hash = f"{string_to_hash}-{self.client_stage_id}"
        if self.user_groups_to_match is not None:
            user_groups = set(self.user_groups_to_match)
            user_groups = sorted(user_groups)
            string_to_hash = f"{string_to_hash}-{user_groups}"
        if self.policy_number_being_renewed is not None:
            string_to_hash = f"{string_to_hash}-{self.policy_number_being_renewed}"
        if self.policy_status is not None:
            string_to_hash = f"{string_to_hash}-{self.policy_status}"
        if self.policy_expiration_date is not None:
            string_to_hash = f"{string_to_hash}-{self.policy_expiration_date}"
        if self.replaces_client_id is not None:
            string_to_hash = f"{string_to_hash}-{self.replaces_client_id}"
        if self.replaced_by_client_id is not None:
            string_to_hash = f"{string_to_hash}-{self.replaced_by_client_id}"

        return str(UUID(hashlib.md5(f"{string_to_hash}".encode("utf8", "surrogatepass")).hexdigest()))

    def append_change(self, req: "SubmissionSync"):
        premium_mappings = self.get_coverages_premium_mapping()
        req.merge_coverage_premiums(premium_mappings)
        # before comparing the hashes we need to "merge" the coverage premiums
        hashes_until_now = {prev["hash"] for prev in self.applied_changes or []}
        hashes_until_now.add(self.hash)
        hashes_until_now.add(req.hash)

        new_pending = []
        for pending in self.pending_changes or []:
            if pending["hash"] in hashes_until_now:
                continue
            new_pending.append(pending)
            hashes_until_now.add(pending["hash"])

        if new_pending or self.hash != req.hash:
            new_pending.append(req.as_dict())
        self.pending_changes = new_pending
        if req.sync_identifiers:
            quote_numbers = set(req.sync_identifiers.quote_numbers or [])
            policy_numbers = set(req.sync_identifiers.policy_numbers or [])
            if self.sync_identifiers:
                self.sync_identifiers.quote_numbers = list(set(self.sync_identifiers.quote_numbers) | quote_numbers)
                self.sync_identifiers.policy_numbers = list(set(self.sync_identifiers.policy_numbers) | policy_numbers)
            else:
                self.sync_identifiers = SubmissionSyncIdentifiers(
                    quote_numbers=req.sync_identifiers.quote_numbers,
                    policy_numbers=req.sync_identifiers.policy_numbers,
                )

        if req.matcher_data and not self.matcher_data:
            self.matcher_data = SubmissionSyncMatcherData(
                named_insured=req.matcher_data.named_insured,
                named_insured_address=req.matcher_data.named_insured_address,
            )

        if req.sync_report and not self.sync_report:
            self.sync_report = SubmissionSyncReport(org_group=req.sync_report.org_group)

    def get_created_start_and_end_date(
        self, number_of_days: int, renewal_received_days_ahead: int = 0, days_in_the_future: int | None = None
    ) -> tuple[datetime, datetime]:
        renewal_days_ahead = renewal_received_days_ahead if self.is_renewal else 0
        future_days = days_in_the_future or number_of_days
        difference_in_days = timedelta(days=number_of_days)
        renewal_extra_days = timedelta(days=renewal_days_ahead)
        future_extra_days = timedelta(days=future_days)
        start_date = self.submission_created_at_reference - difference_in_days
        end_date = self.submission_created_at_reference + future_extra_days + renewal_extra_days
        return start_date, end_date

    def get_effective_start_and_end_date(
        self, number_of_days: int, days_in_the_future: int | None = None
    ) -> tuple[datetime, datetime]:
        future_days = days_in_the_future or number_of_days
        difference_in_days = timedelta(days=number_of_days)
        future_extra_days = timedelta(days=future_days)
        start_date = self.effective_date - difference_in_days
        end_date = self.effective_date + future_extra_days
        return start_date, end_date

    def merge_coverage_premiums(self, premium_mappings: dict[str, dict[str, float]]):
        coverages = self.get_coverages_dict_array()
        for cov in coverages:
            cov_name = cov.get("coverage_name")
            cov_type = cov.get("coverage_type")
            key = f"{cov_name}-{cov_type}"
            premiums_dict = premium_mappings.get(key, {})
            cov["quoted_premium"] = (
                cov.get("quoted_premium")
                if cov.get("quoted_premium") is not None
                else premiums_dict.get("quoted_premium")
            )
            cov["bound_premium"] = (
                cov.get("bound_premium") if cov.get("bound_premium") is not None else premiums_dict.get("bound_premium")
            )
            cov["total_premium"] = (
                cov.get("total_premium") if cov.get("total_premium") is not None else premiums_dict.get("total_premium")
            )
            cov["limit"] = cov.get("limit") if cov.get("limit") is not None else premiums_dict.get("limit")
            cov["attachment_point"] = (
                cov.get("attachment_point")
                if cov.get("attachment_point") is not None
                else premiums_dict.get("attachment_point")
            )
        self.coverages = coverages

    def get_coverages_premium_mapping(self) -> dict[str, dict[str, float]]:
        result = {}
        coverages = self.get_coverages_object_array()
        for cov in coverages:
            key = f"{cov.coverage_name}-{cov.coverage_type}"
            premiums_dict = result.get(key, {})
            premiums_dict["quoted_premium"] = cov.quoted_premium
            premiums_dict["bound_premium"] = cov.bound_premium
            premiums_dict["total_premium"] = cov.total_premium
            premiums_dict["limit"] = cov.limit
            premiums_dict["attachment_point"] = cov.attachment_point
            result[key] = premiums_dict

        return result

    # TODO(sudijovski) remove this once sync coverages are migrated to use the coverages list
    def get_coverages_dict_array(self) -> list[dict]:
        coverages = self.coverages or []
        if self.coverage_name and not coverages:
            coverages = [
                {
                    "coverage_name": self.coverage_name,
                    "coverage_type": self.coverage_type,
                    "quoted_premium": self.quoted_premium,
                    "bound_premium": self.bound_premium,
                }
            ]
        return coverages

    def get_coverages_object_array(self) -> list["SubmissionSyncCoverage"]:
        from copilot.schemas.submission_sync import SubmissionSyncCoverageSchema

        return SubmissionSyncCoverageSchema(many=True).load(self.get_coverages_dict_array())

    def get_last_change(self) -> "SubmissionSync":
        from copilot.schemas.submission_sync import SubmissionSyncSchema

        ss_schema = SubmissionSyncSchema()

        all_changes = (self.applied_changes or []) + [self.as_dict()] + (self.pending_changes or [])

        latest: dict = {}
        for change in reversed(all_changes):
            for key in change:
                if key in self.LAST_CHANGE_PROPERTIES and key in latest:
                    # we need this because we want to keep have None as the last value
                    continue
                if key not in latest or (change[key] is not None and latest[key] is None):
                    latest[key] = change[key]

        return ss_schema.load(latest)

    def get_history(self) -> list["SubmissionSync"]:
        from copilot.schemas.submission_sync import SubmissionSyncSchema

        ss_schema = SubmissionSyncSchema(many=True)

        pending_changes = ss_schema.load(self.pending_changes or [])
        applied_changes = ss_schema.load(self.applied_changes or [])

        return [*applied_changes, self, *pending_changes]

    def get_full_coverages(self) -> list["SubmissionSyncCoverage"]:
        history = self.get_history()
        coverage_id_to_coverage: dict[str, SubmissionSyncCoverage] = {}
        coverage_name_to_type: dict[str, CoverageType | None] = {}

        # "building" the coverages starting from the last position and moving backwards
        for ss in reversed(history):
            for cov_obj in ss.get_coverages_object_array():
                # if coverage is in coverage_name_to_type but different type skip it
                # this essentially means that the coverage has been changed
                if (
                    cov_obj.coverage_name in coverage_name_to_type
                    and coverage_name_to_type[cov_obj.coverage_name] != cov_obj.coverage_type
                ):
                    continue
                cov = coverage_id_to_coverage.get(cov_obj.sync_coverage_id, cov_obj)
                cov.quoted_premium = cov.quoted_premium or cov_obj.quoted_premium
                cov.bound_premium = cov.bound_premium or cov_obj.bound_premium
                cov.limit = cov.limit or cov_obj.limit
                cov.attachment_point = cov.attachment_point or cov_obj.attachment_point
                cov.stage = cov.stage or cov_obj.stage
                coverage_id_to_coverage[cov.sync_coverage_id] = cov
                coverage_name_to_type[cov.coverage_name] = cov.coverage_type

        return list(coverage_id_to_coverage.values())

    @property
    def business_name(self) -> str | None:
        if self.matcher_data and self.matcher_data.named_insured:
            return self.matcher_data.named_insured
        return self.submission_name

    @property
    def quote_numbers(self) -> list[str]:
        return self.sync_identifiers.quote_numbers if self.sync_identifiers else []

    @property
    def policy_numbers(self) -> list[str]:
        return self.sync_identifiers.policy_numbers if self.sync_identifiers else []

    @cached_property
    def brokerage_obj(self):
        # first try to get the brokerage by domain
        brokerage = None
        domain = None
        if self.broker_email and "@" in self.broker_email:
            domain = self.broker_email.split("@")[-1]

        if domain:
            from copilot.logic.brokerages.brokerages import get_brokerage_by_domains

            brokerage = get_brokerage_by_domains([domain])

        if self.brokerage and not brokerage:
            from copilot.logic.brokerages.brokerages import (
                get_brokerage_by_name_or_alias,
            )

            brokerage = get_brokerage_by_name_or_alias(self.brokerage, [])

        return brokerage

    @cached_property
    def broker_obj(self):
        if not self.broker and not self.broker_email:
            return None

        if not self.brokerage_obj:
            return None

        from copilot.logic.dao.brokerage_employee_dao import BrokerageEmployeeDAO
        from copilot.models import BrokerageEmployee

        broker = BrokerageEmployeeDAO.get_existing_broker(
            BrokerageEmployee(
                brokerage_id=self.brokerage_obj.id,
                name=self.broker,
                email=self.broker_email,
            )
        )
        return broker[0] if broker else None


@dataclass
class SubmissionSyncCoverage:
    coverage_name: str
    coverage_type: CoverageType | None = None
    quoted_premium: float | None = None
    bound_premium: float | None = None
    total_premium: float | None = None
    limit: float | None = None
    attachment_point: float | None = None
    keep_other_coverage: bool | None = None
    stage: SubmissionStage | None = None

    @property
    def sync_coverage_id(self) -> str:
        return f"{self.coverage_name}-{self.coverage_type}"

    @property
    def is_quoted(self) -> bool:
        return self.quoted_premium is not None and self.quoted_premium > 0

    @property
    def hash(self) -> str:
        coverage_hash = f"{self.coverage_name}-{self.coverage_type}-{self.quoted_premium}-{self.bound_premium}"
        # we do not want to change the hash just by adding new properties, we only add them to the
        # hash string if they have value
        if self.limit is not None:
            coverage_hash = f"{coverage_hash}-{self.limit}"
        if self.attachment_point is not None:
            coverage_hash = f"{coverage_hash}-{self.attachment_point}"
        if self.total_premium is not None:
            coverage_hash = f"{coverage_hash}-{self.total_premium}"
        if self.keep_other_coverage is not None:
            coverage_hash = f"{coverage_hash}-{self.keep_other_coverage}"
        if self.stage is not None:
            coverage_hash = f"{coverage_hash}-{self.stage}"
        return coverage_hash

    def get_limit(self) -> float | None:
        return self.limit if self.coverage_type == CoverageType.EXCESS else None

    def get_attachment_point(self) -> float | None:
        return self.attachment_point if self.coverage_type == CoverageType.EXCESS else None


@dataclass
class DryRunSubmissionMatch:
    id: UUID
    name: str
    created_at: datetime
    matcher_type: MatcherType
    matching_score: float


@dataclass
class MatcherDryRunResult:
    sync_id: UUID
    client_id: str
    received_date: datetime
    name_in_report: str
    submissions: list[DryRunSubmissionMatch]


@dataclass_json
@dataclass
class GetSyncRequestsForClientIdsRequest:
    client_ids: list[str]
    organization_id: int


class SyncMatchingRun(BaseModel):
    __tablename__ = "sync_matching_runs"

    id = Column(SA_UUID(as_uuid=True), primary_key=True)
    producer_config = Column(postgresql.JSONB(astext_type=String(), none_as_null=True))
    organization_id = Column(Integer, ForeignKey("organization.id"), nullable=False)
    sync_matching_results = relationship(
        "SyncMatchingResult",
        uselist=True,
        cascade="all, delete-orphan",
        lazy="select",
        back_populates="sync_matching_run",
    )
    status = Column(
        Enum(SyncMatchingRunStatus, native_enum=False), nullable=False, default=SyncMatchingRunStatus.NOT_STARTED
    )
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    automatically_triggered = Column(Boolean, nullable=False, default=False)

    @property
    def producer_config_obj(self) -> SubmissionProducerConfiguration:
        return SubmissionProducerConfiguration.from_dict(self.producer_config)  # type: ignore


class SyncMatchingResult(BaseModel):
    __tablename__ = "sync_matching_results"

    sync_matching_run_id = Column(
        SA_UUID(as_uuid=True),
        ForeignKey("sync_matching_runs.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    sync_matching_run = relationship("SyncMatchingRun", uselist=False, back_populates="sync_matching_results")
    submission_sync_id = Column(
        SA_UUID(as_uuid=True),
        ForeignKey("submission_sync.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    submission_sync = relationship("SubmissionSync", uselist=False, back_populates="sync_matching_results")
    matching_results = Column(postgresql.JSONB(astext_type=String(), none_as_null=True))
    is_processed = Column(Boolean, nullable=False, default=False)


@dataclass
class SyncMatchingRunRequest:
    run_id: str
    organization_id: int
    submission_sync_ids: list[str] = field(default_factory=list)
    producer_config: dict | None = None
    automatically_triggered: bool | None = None
    assign_client_ids: bool | None = False

    @property
    def config(self) -> dict:
        conf = self.producer_config or get_sync_config_map().get(self.organization_id, {}).get(
            "submission_producer_config"
        )
        try:
            spc = SubmissionProducerConfiguration.from_dict(conf)
            return json.loads(spc.to_json())
        except:
            logger.exception("Invalid producer config", sync_matching_run_request=self)
        return {}


@dataclass
class SubmissionIdentifiersSuggestionResponse:
    created_at: datetime
    account_name: str
    client_id: str
    quote_numbers: list[str]
    policy_numbers: list[str]
    confidence: float
    is_assigned: bool


class SubmissionIdentifiersSuggestion(ImmutableBaseModel):
    __tablename__ = "submission_identifiers_suggestions"

    submission_id = Column(
        SA_UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    submission_sync_id = Column(
        SA_UUID(as_uuid=True), ForeignKey("submission_sync.id", ondelete="CASCADE"), nullable=False, index=True
    )
    submission_sync = relationship("SubmissionSync", uselist=False, lazy="joined")
    confidence = Column(Float, nullable=False)
