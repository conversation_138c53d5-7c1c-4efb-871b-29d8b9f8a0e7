from __future__ import annotations

from static_common.enums.enum import StrEnum
from static_common.enums.submission import SubmissionStage  # noqa


class PredictionResult(StrEnum):
    TRUE_POSITIVE = "TRUE_POSITIVE"
    FALSE_POSITIVE = "FALSE_POSITIVE"
    TRUE_NEGATIVE = "TRUE_NEGATIVE"
    FALSE_NEGATIVE = "FALSE_NEGATIVE"


class SubmissionEvent(StrEnum):
    SUBMISSION_CREATED = "SUBMISSION_CREATED"
    BUSINESS_CONFIRMED = "BUSINESS_CONFIRMED"
    BUSINESS_DISCONFIRMED = "BUSINESS_DISCONFIRMED"
    """Old business id removed and there is no duplicates of that business_id in the submission"""
    BUSINESS_ADDED_TO_SUBMISSION = "BUSINESS_ADDED_TO_SUBMISSION"
    BUSINESS_REMOVED_FROM_SUBMISSION = "BUSINESS_REMOVED_FROM_SUBMISSION"
    SUBMISSION_UPDATED = "SUBMISSION_UPDATED"
    BUSINESS_AUTO_CONFIRMED = "BUSINESS_AUTO_CONFIRMED"
    SUBMISSION_USERS_UPDATED = "SUBMISSION_USERS_UPDATED"
    SUBMISSION_PERMISSIONS_UPDATED = "SUBMISSION_PERMISSIONS_UPDATED"
    SHELL_SUBMISSION_CREATED = "SHELL_SUBMISSION_CREATED"
    BUSINESSES_ADDED_TO_SHELL_SUBMISSION = "BUSINESSES_ADDED_TO_SHELL_SUBMISSION"
    SUBMISSION_DATA_ONBOARDING_FINISHED = "SUBMISSION_DATA_ONBOARDING_FINISHED"
    SUBMISSION_COPIED = "SUBMISSION_COPIED"
    SUBMISSION_CHANGED_REQUESTED_COVERAGE = "SUBMISSION_CHANGED_REQUESTED_COVERAGE"
    SUBMISSION_VERIFIED = "SUBMISSION_VERIFIED"
    SUBMISSION_PROCESSING_DATA_COPIED = "SUBMISSION_PROCESSING_DATA_COPIED"
    EXTERNAL_FACING_DATA_UPDATED = "EXTERNAL_FACING_DATA_UPDATED"
    SUBMISSION_TAXONOMY_UPDATED = "SUBMISSION_TAXONOMY_UPDATED"
    SUBMISSION_AUTO_VERIFIED = "SUBMISSION_AUTO_VERIFIED"
    SUBMISSION_CLIENT_STATUS_UPDATED = "SUBMISSION_CLIENT_STATUS_UPDATED"
    SUBMISSION_CLIENT_DATA_UPDATED = "SUBMISSION_CLIENT_DATA_UPDATED"
    EXTERNAL_CLEARING_FINISHED = "EXTERNAL_CLEARING_FINISHED"


class FileEvent(StrEnum):
    FILE_ADDED = "FILE_ADDED"


class RequestedCoverageEvent(StrEnum):
    REQUESTED_COVERAGE_ADDED = "REQUESTED_COVERAGE_ADDED"


class UserRoleEnum(StrEnum):
    underwriter = "underwriter"
    manager = "manager"
    admin = "admin"
    underwriter_assistant = "underwriter_assistant"


class ActionTypeEnum(StrEnum):
    business_search = "business_search"


class SubmissionActionType(StrEnum):
    # Submission business
    ADDED = "ADDED"
    DELETED = "DELETED"

    # Submission -> saved via trigger, not in code
    ON_MY_PLATE = "ON_MY_PLATE"
    QUOTED = "QUOTED"
    WAITING_FOR_OTHERS = "WAITING_FOR_OTHERS"
    DECLINED = "DECLINED"
    QUOTED_LOST = "QUOTED_LOST"
    QUOTED_BOUND = "QUOTED_BOUND"
    EXPIRED = "EXPIRED"
    CANCELED = "CANCELED"
    INDICATED = "INDICATED"
    COMPLETED = "COMPLETED"
    CLEARING_ISSUE = "CLEARING_ISSUE"
    BLOCKED = "BLOCKED"

    # Report
    SHARED = "SHARED"
    REPORT_DELETED = "REPORT_DELETED"

    # File
    FILE_ADDED = "FILE_ADDED"
    FILE_DELETED = "FILE_DELETED"
    FILE_TYPE_CHANGED = "FILE_TYPE_CHANGED"

    # SubmissionCoverages
    SUBMISSION_COVERAGE_ADDED = "SUBMISSION_COVERAGE_ADDED"
    SUBMISSION_COVERAGE_DELETED = "SUBMISSION_COVERAGE_DELETED"
    SUBMISSION_COVERAGE_UPDATED = "SUBMISSION_COVERAGE_UPDATED"

    # PDS
    PDS_PROCESSING_STARTED = "PDS_PROCESSING_STARTED"
    PDS_PROCESSING_RESTARTED = "PDS_PROCESSING_RESTARTED"
    PDS_REVERTED_TO_DO = "PDS_REVERTED_TO_DO"
    PDS_RECREATED_IN_DO = "PDS_RECREATED_IN_DO"
    PDS_PROCESSING_COMPLETED = "PDS_PROCESSING_COMPLETED"
    PDS_LIGHT_CLEARING_COMPLETED = "PDS_LIGHT_CLEARING_COMPLETED"
    PDS_ENTITY_MAPPING_SKIPPED = "PDS_ENTITY_MAPPING_SKIPPED"
    PDS_ENTITY_MAPPING_STARTED = "PDS_ENTITY_MAPPING_STARTED"
    PDS_ENTITY_MAPPING_COMPLETED = "PDS_ENTITY_MAPPING_COMPLETED"
    PDS_FILES_CLEARING_STARTED = "PDS_FILES_CLEARING_STARTED"
    PDS_FILES_CLEARING_COMPLETED = "PDS_FILES_CLEARING_COMPLETED"
    PDS_BUSINESS_CONFIRMATION_STARTED = "PDS_BUSINESS_CONFIRMATION_STARTED"
    PDS_BUSINESS_CONFIRMATION_COMPLETED = "PDS_BUSINESS_CONFIRMATION_COMPLETED"
    PDS_DATA_ONBOARDING_SKIPPED = "PDS_DATA_ONBOARDING_SKIPPED"
    PDS_DATA_ONBOARDING_STARTED = "PDS_DATA_ONBOARDING_STARTED"
    PDS_DATA_ONBOARDING_COMPLETED = "PDS_DATA_ONBOARDING_COMPLETED"
    PDS_VERIFICATION_STARTED = "PDS_VERIFICATION_STARTED"
    PDS_VERIFICATION_COMPLETED = "PDS_VERIFICATION_COMPLETED"
    PDS_PROCESSING_FAILED = "PDS_PROCESSING_FAILED"
    PDS_PROCESSING_CANCELLED = "PDS_PROCESSING_CANCELLED"
    PDS_AUTO_VERIFICATION_COMPLETED = "PDS_AUTO_VERIFICATION_COMPLETED"
    PDS_MANUAL_VERIFICATION_COMPLETED = "PDS_MANUAL_VERIFICATION_COMPLETED"
    PDS_FILE_REPROCESSING_REQUESTED = "PDS_FILE_REPROCESSING_REQUESTED"
    PDS_SKIP_FILE_CACHE = "PDS_SKIP_FILE_CACHE"

    # NAICS
    PRIMARY_NAICS_UPDATED = "PRIMARY_NAICS_UPDATED"
    NAICS_VERIFIED = "NAICS_VERIFIED"

    # Submission
    STUCK_CHANGED = "STUCK_CHANGED"
    ORG_GROUP_CHANGED = "ORG_GROUP_CHANGED"

    @classmethod
    def values(cls):
        return list(map(lambda c: c.value, cls))

    @classmethod
    def is_pds_type(cls, action_type: str | SubmissionActionType) -> bool:
        if not isinstance(action_type, str):
            action_type = action_type.value
        return action_type.startswith("PDS_")


class SubmissionParentType(StrEnum):
    SUBMISSION_BUSINESS = "SUBMISSION_BUSINESS"
    REPORT = "REPORT"


class SubmissionAuditChangeType(StrEnum):
    # Submission business
    ADDED = "ADDED"
    UPDATED = "UPDATED"
    DELETED = "DELETED"


class PermissionType(StrEnum):
    ADMIN = 5
    OWNER = 4
    EDITOR = 3
    COMMENTER = 2
    VIEWER = 1


class Scopes(StrEnum):
    CREATE_FILES = "create:files"
    CREATE_REPORTS = "create:reports"


class NotebookThreadSubject(StrEnum):
    SUBMISSION = "SUBMISSION"
    SUBMISSION_BUSINESS = "SUBMISSION_BUSINESS"
    DOSSIER_COMPONENT = "DOSSIER_COMPONENT"


class NotificationStatus(StrEnum):
    SENT = "SENT"
    DELIVERED = "DELIVERED"


class FieldType(StrEnum):
    TEXT = "TEXT"
    NUMBER = "NUMBER"
    DATETIME = "DATETIME"
    BOOLEAN = "BOOLEAN"
    TEXT_ARRAY = "TEXT_ARRAY"
    NUMBER_ARRAY = "NUMBER_ARRAY"
    INTEGER = "INTEGER"


class ExecutionEventType(StrEnum):
    STARTED = "STARTED"
    SUCCEEDED = "SUCCEEDED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class LineOfBusinessType(StrEnum):
    PROPERTY = "Property"
    UMBRELLA = "Umbrella"
    MULTIPLE = "Multiple"
    OTHER = "Other"
    UNKNOWN = "Unknown"
    BOILER_MACHINERY = "Boiler & Machinery"
    BUSINESS_AUTO = "Business Auto"
    BUSINESS_OWNERS = "Business Owners"
    INLAND_MARINE = "Inland Marine"
    CRIME = "Crime"
    CYBER_PRIVACY = "Cyber Privacy"
    FIDUCIARY_LIABILITY = "Fiduciary Liability"
    FOREIGN_LIABILITY = "Foreign Liability"
    GARAGE_DEALERS = "Garage & Dealers"
    GENERAL_LIABILITY = "General Liability"
    LIQUOR_LIABILITY = "Liquor Liability"
    PACKAGE = "Package"
    TERRORISM = "Terrorism"
    TRUCKERS = "Truckers"
    WORKERS_COMP = "Workers Comp"
    PROFESSIONAL_LIABILITY = "Professional Liability"
    PRODUCT_LIABILITY = "Product Liability"
    MANAGEMENT_LIABILITY = "Management Liability"
    DIRECTORS_AND_OFFICERS = "Directors and Officers (D&O)"
    CARGO = "Cargo"
    AUTO_LIABILITY = "Auto Liability"
    AUTO_PHYSICAL_DAMAGE = "Auto Physical Damage"


class Sorting(StrEnum):
    CREATED_AT = "CREATED_AT"
    EFFECTIVE_AT = "EFFECTIVE_AT"
    DECLINED_AT = "DECLINED_AT"
    REPORT_NAME = "REPORT_NAME"
    BROKER_NAME = "BROKER_NAME"
    BROKERAGE_NAME = "BROKERAGE_NAME"
    DUE_DATE = "DUE_DATE"
    STAGE = "STAGE"
    RECOMMENDATION_V2 = "RECOMMENDATION_V2"
    RECOMMENDATION_SCORE = "RECOMMENDATION_SCORE"
    BOOKMARKED = "BOOKMARKED"
    READ = "READ"
    NAICS = "NAICS"
    EXPIRED_PREMIUM = "EXPIRED_PREMIUM"
    TARGET_PREMIUM = "TARGET_PREMIUM"
    BOUND_PREMIUM = "BOUND_PREMIUM"
    QUOTED_PREMIUM = "QUOTED_PREMIUM"
    SALES = "SALES"
    AMOUNT_QUOTED = "AMOUNT_QUOTED"
    AMOUNT_BOUND = "AMOUNT_BOUND"
    TOTAL_PREMIUM_OR_BOUND_PREMIUM = "TOTAL_PREMIUM_OR_BOUND_PREMIUM"
    PAYROLL = "PAYROLL"
    X_MOD_SCORES = "X_MOD_SCORES"
    FNI_STATE = "FNI_STATE"
    ESTIMATED_PREMIUM = "ESTIMATED_PREMIUM"
    ADJUSTED_TIV = "ADJUSTED_TIV"
    TIV = "TIV"
    LAST_EMAIL = "LAST_EMAIL"
    ASSIGNEE = "ASSIGNEE"


class PolicyLevelDeductibleType(StrEnum):
    PERCENTAGE = "PERCENTAGE"
    NUMERIC = "NUMERIC"


class ParentType(StrEnum):
    SUBMISSION = "SUBMISSION"
    BUSINESS = "BUSINESS"
    ARBITRARY_PARENT = "ARBITRARY_PARENT"


class FormatType(StrEnum):
    DECIMAL = "DECIMAL"
    PERCENTAGE = "PERCENTAGE"
    INTEGER = "INTEGER"
    DATE = "DATE"


class SourceType(StrEnum):
    USER = "USER"
    SUBMISSION = "SUBMISSION"
    SUBMISSION_BUSINESS = "SUBMISSION_BUSINESS"
    DOSSIER = "DOSSIER"
    ERS = "ERS"


class SendEmailTypes(StrEnum):
    REQUEST_DEMO = "REQUEST_DEMO"
    SUBMISSION_BUSINESS_CHANGED = "SUBMISSION_BUSINESS_CHANGED"


class PresignedUrlMethods(StrEnum):
    get_object = "get_object"
    put_object = "put_object"


class PresignedUrlBucketNames(StrEnum):
    COPILOT_S3_BUCKET_NAME = "COPILOT_S3_BUCKET_NAME"
    INGESTION_S3_BUCKET_NAME = "INGESTION_S3_BUCKET_NAME"


class SubmissionCoverageType(StrEnum):
    PRIMARY = "primary"
    EXCESS = "excess"


class AdditionalIdentifierType(StrEnum):
    QUOTE_NUMBER = "quote_number"
    POLICY_NUMBER = "policy_number"
    OTHER = "other"


class CoverageRemovalStrategyType(StrEnum):
    NONE = "NONE"
    ONE_OF = "ONE_OF"
    ALL = "ALL"


class ReportStatus(StrEnum):
    CLEARED = "cleared"
    DUPLICATE = "duplicate"
    IN_REVIEW = "in review"
    DECLINED = "declined"
    QUOTED = "quoted"
    BOUND = "bound"
    LOST = "lost"


class CopilotWorkerExecutionType(StrEnum):
    REASSIGN_FACT_SUBTYPE = "REASSIGN_FACT_SUBTYPE"
    REASSIGN_PARENT = "REASSIGN_PARENT"
    RESOLVE_BUSINESSES = "RESOLVE_BUSINESSES"
    """Triggers load of first party data for FirstPartyFieldsGroupType other than SOV"""
    LOAD_ADDITIONAL_FIRST_PARTY_DATA = "LOAD_ADDITIONAL_FIRST_PARTY_DATA"
    """
    Triggers merging facts from many business into single facts set where parent is a submission.
    Only predefined facts will be merge (eg fleet related)
    """
    MERGE_BUSINESSES_FACTS = "MERGE_BUSINESSES_FACTS"
    """
    Triggers merging fact from many business into single fact where parent is a submission.
    Run for facts subtype in request (merging of that subtype needs to be predefined in copilot workers)
    """
    MERGE_BUSINESSES_FACT = "MERGE_BUSINESSES_FACT"
    LOAD_VINS = "LOAD_VINS"


class CoverageType(StrEnum):
    PRIMARY = "PRIMARY"
    EXCESS = "EXCESS"


class CardPropertyType(StrEnum):
    STRING = "STRING"
    NUMBER = "NUMBER"
    JSON = "JSON"


class ClearingStatus(StrEnum):
    CLEARED = "CLEARED"
    IN_CLEARING_CONFLICT = "IN_CLEARING_CONFLICT"
    BLOCKED = "BLOCKED"
    PRE_CLEARING = "PRE_CLEARING"
    CLEARING_IN_PROGRESS = "CLEARING_IN_PROGRESS"

    @staticmethod
    def blocking_statuses() -> set[str]:
        return {ClearingStatus.IN_CLEARING_CONFLICT, ClearingStatus.BLOCKED}

    @staticmethod
    def resolved_statuses() -> set[str]:
        return {ClearingStatus.CLEARED, ClearingStatus.BLOCKED}

    @staticmethod
    def pending_clearing_statuses() -> set[str]:
        return {ClearingStatus.PRE_CLEARING, ClearingStatus.IN_CLEARING_CONFLICT}


class DistributionChannelEmployedType(StrEnum):
    AGENCY_AGENT = "AGENCY_AGENT"
    BROKERAGE_BROKER = "BROKERAGE_BROKER"
    BROKERAGE_AGENCY_BROKER_AGENT = "BROKERAGE_AGENCY_BROKER_AGENT"


class EmailTemplateType(StrEnum):
    DECLINE_EMAIL = "DECLINE_EMAIL"
    DECLINE_EMAIL_NOT_ELIGIBLE_PRODUCER = "DECLINE_EMAIL_NOT_ELIGIBLE_PRODUCER"
    OTHER = "OTHER"
    CLEARING_ACCEPTED = "CLEARING_ACCEPTED"
    CLEARING_DECLINED = "CLEARING_DECLINED"
    RECOMMENDATIONS = "RECOMMENDATIONS"
    INACTIVE_BROKER_DECLINE = "INACTIVE_BROKER_DECLINE"
    INCOMPLETE_SUBMISSION = "INCOMPLETE_SUBMISSION"
    INVALID_BROKER_DECLINE = "INVALID_BROKER_DECLINE"
    CLEARING_ACCEPTED_NO_UW = "CLEARING_ACCEPTED_NO_UW"
    BLOCKED_ON_CLEARING = "BLOCKED_ON_CLEARING"
    BLOCKED_FEIN = "BLOCKED_FEIN"
    BLOCKED_BROKER = "BLOCKED_BROKER"
    BLOCKED_ON_CLEARING_BY_DECLINED_SUBMISSION = "BLOCKED_ON_CLEARING_BY_DECLINED_SUBMISSION"
    BLOCKED_ON_CLEARING_PARTIALLY = "BLOCKED_ON_CLEARING_PARTIALLY"
    EFFECTIVE_DATE_TOO_DISTANT = "EFFECTIVE_DATE_TOO_DISTANT"


class AlertType(StrEnum):
    MISSING_NAICS = "MISSING_NAICS"
    MISSING_NAICS_L2 = "MISSING_NAICS_L2"
    MISSING_NAICS_SLA = "MISSING_NAICS_SLA"
    MISSING_FILES = "MISSING_FILES"
    MISSING_PROCESSING = "MISSING_PROCESSING"
    MISSING_VERIFICATION = "MISSING_VERIFICATION"
    MISSING_VERIFICATION_L2 = "MISSING_VERIFICATION_L2"
    MISSING_VERIFICATION_L3 = "MISSING_VERIFICATION_L3"
    MISSING_VERIFICATION_L4 = "MISSING_VERIFICATION_L4"
    MISSING_VERIFICATION_L5 = "MISSING_VERIFICATION_L5"
    MISSING_VERIFICATION_L6 = "MISSING_VERIFICATION_L6"
    MISSING_VERIFICATION_L7 = "MISSING_VERIFICATION_L7"
    PARAGON_WC_BROKER_MAPPING_MISSING = "PARAGON_WC_BROKER_MAPPING_MISSING"
    MISSING_TRIAGE = "MISSING_TRIAGE"
    WEEKEND_SUBMISSION = "WEEKEND_SUBMISSION"
    WEEKEND_SUBMISSION_FOR_SUPPORT_LEAD = "WEEKEND_SUBMISSION_FOR_SUPPORT_LEAD"


class ReportDependencyType(StrEnum):
    SAME_ORG = "SAME_ORG"
    CROSS_ORG = "CROSS_ORG"


class ReportShadowType(StrEnum):
    HAS_ACTIVE_SHADOW = "HAS_ACTIVE_SHADOW"
    IS_ACTIVE_SHADOW = "IS_ACTIVE_SHADOW"
    IS_SHADOW_ORIGIN = "IS_SHADOW_ORIGIN"
    CHAINED_SHADOW = "CHAINED_SHADOW"


class ReportTriageResult(StrEnum):
    ACCEPT = "ACCEPT"
    DECLINE = "DECLINE"
    DECLINE_NO_EMAIL = "DECLINE_NO_EMAIL"
    DECLINE_CLEARING_EMAIL = "DECLINE_CLEARING_EMAIL"
    DECLINE_NOT_ELIGIBLE_PRODUCER = "DECLINE_NOT_ELIGIBLE_PRODUCER"
    INCOMPLETE = "INCOMPLETE"
    INCOMPLETE_TBC = "INCOMPLETE_TBC"
    INVALID_BROKER = "INVALID_BROKER"
    BLOCKED_AT_MARKET = "BLOCKED_AT_MARKET"
    BLOCKED_FEIN = "BLOCKED_FEIN"
    BLOCKED_BROKER = "BLOCKED_BROKER"
    UNAPPOINTED_BROKER = "UNAPPOINTED_BROKER"
    EFFECTIVE_DATE_TOO_DISTANT = "EFFECTIVE_DATE_TOO_DISTANT"

    @staticmethod
    def decline_results() -> set[ReportTriageResult]:
        return {
            ReportTriageResult.DECLINE,
            ReportTriageResult.DECLINE_NO_EMAIL,
            ReportTriageResult.DECLINE_CLEARING_EMAIL,
            ReportTriageResult.DECLINE_NOT_ELIGIBLE_PRODUCER,
        }

    @staticmethod
    def clear_results() -> set[ReportTriageResult]:
        return {ReportTriageResult.ACCEPT, ReportTriageResult.DECLINE_CLEARING_EMAIL}

    @staticmethod
    def skip_email_results() -> set[ReportTriageResult]:
        return {ReportTriageResult.INCOMPLETE_TBC, ReportTriageResult.DECLINE_NO_EMAIL}


class SubmissionCoverageSource(StrEnum):
    FILE = "FILE"
    AUTO = "AUTO"
    MANUAL = "MANUAL"
    NAICS = "NAICS"
    COPY = "COPY"
    SYNC = "SYNC"
    EMAIL = "EMAIL"
    API = "API"


class IFTAAggregationType(StrEnum):
    ALL_JURISDICTIONS_TOTAL = "ALL_JURISDICTIONS_TOTAL"
    US_TOTAL = "US_TOTAL"
    CANADA_TOTAL = "CANADA_TOTAL"


class LogicalOperator(StrEnum):
    AND = "AND"
    OR = "OR"


class MissingDataStatus(StrEnum):
    NO_FILES = "NO_FILES"
    INVALID_FILES = "INVALID_FILES"
    NO_DATA = "NO_DATA"


class FileExtractedMetadataStatus(StrEnum):
    PENDING = "PENDING"
    FAILED = "FAILED"
    READY = "READY"
    PARTIAL = "PARTIAL"


class BrokerageEmployeeSource(StrEnum):
    MANUAL = "MANUAL"
    API = "API"
    EMAIL = "EMAIL"
    COPY = "COPY"
    AUTO = "AUTO"
    SYNC = "SYNC"


class SubmissionTaxonomySource(StrEnum):
    MANUAL = "MANUAL"
    TAXONOMY_SYNC = "TAXONOMY_SYNC"
    AUTO = "AUTO"


class SupportUserActionType(StrEnum):
    ADD = "add"
    EDIT = "edit"
    REMOVE = "remove"


class DataField(StrEnum):
    ENTITY_MAPPED_DATA = "entity_mapped_data"
    ONBOARDED_DATA = "onboarded_data"


class SubmissionConsolidationStatus(StrEnum):
    PENDING = "PENDING"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"
    NOT_READY = "NOT_READY"


class TaskScoringStatus(StrEnum):
    PREPARING = "PREPARING"
    PENDING = "PENDING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class UserShadowFileState(StrEnum):
    DELETED = "DELETED"
    REPLACED = "REPLACED"
    CREATED = "CREATED"


class VerificationCheckStages(StrEnum):
    ENTITY_MAPPING = "ENTITY_MAPPING"
    DATA_ONBOARDING = "DATA_ONBOARDING"
    COMPLETED = "COMPLETED"


class HubTemplateType(StrEnum):
    HUB = "HUB"
    DASHBOARD = "DASHBOARD"


class TaskDatasetExecutionStatus(StrEnum):
    PENDING = "PENDING"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"


class SubmissionPremisesType(StrEnum):
    SYNC = "SYNC"
    FNI = "FNI"
    COPY = "COPY"
