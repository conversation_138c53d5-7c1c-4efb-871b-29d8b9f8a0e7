from enum import StrEnum
import os
import uuid

from common.clients.utils import get_call_origin
from flask_login import current_user
from infrastructure_common.logging import get_logger
from ldclient import Context, ContextBuilder, LDClient
from ldclient.config import Config
from static_common.models.types import Str<PERSON><PERSON><PERSON>
import ldclient

from copilot.models import User

_user = {"key": "CAPI"}
_submission = Context(key=get_call_origin(), kind="submission")


class FeatureType(StrEnum):
    ENABLE_FILE_BASED_COVERAGE_ASSIGNMENT = "enableFileBasedCoverageAssignment"
    ENABLE_NEW_EMAIL_FLOW = "enableNewEmailWorkflow"
    ENABLE_BOUNCED_EMAILS = "enableBouncedEmails"
    ENABLE_LIMIT_AND_ATTACHMENT_FROM_EMAILS = "limit-and-attachment-point-from-emails"
    ENABLE_MULTIPLE_VALUES_PER_ENTITY = "PDSMultipleValues"
    PARAGON_WC_UW_AUTOASSIGNMENT = "paragon-wc-uw-autoassignment"
    PARAGON_BROKER_TRIAGE_LOGIC = "paragon-broker-triage-logic"
    PARAGON_IMS_SEND_DAILY_REPORT_OF_MISSING_CORRELATIONS = "paragon-ims-send-daily-report-of-missing-correlations"
    RUN_EMAILS_THROUGH_QUEUE = "run-emails-through-queue"
    ALLOW_NON_PROD_EMAILS = "allow-non-prod-emails"
    USE_BROKER_RESOLVER = "use-broker-resolver"
    DROP_UNNECESSARY_COLUMNS = "dropUnnecessaryColumns"
    EXTENDED_SHARED_EMAIL_FOR_KALEPA_ORG = "extendedSharedEmailForKalepaOrg"
    EXTENDED_SHARED_EMAIL_FOR_BOWHEAD_ORG = "extendedSharedEmailForBowheadOrg"
    ENABLE_NEW_FACTS_SUGGESTIONS = "enableNewFactsSuggestions"
    ADD_ML_FACTSUBTYPES_TO_EM = "addMLFactsubtypesToEM"
    INCLUDE_ID_IN_GET_UPLOAD_URL = "include-id-in-get-upload-url"
    INCLUDE_CLIENT_TYPE_AND_TAGS_IN_EXTERNAL_API = "include-client-type-and-tags-in-external-api"
    DROP_FINANCIAL_FACTS = "dropFinancialFacts"
    LIGHT_CLEARING_FOR_CACHED_SUBMISSIONS = "light-clearing-for-cached-submissions"
    PROCESS_BOSS_FILES_WITH_CHILD_FILES = "process-boss-not-rose-files-with-child-files"
    DONT_RELY_ON_GMAIL_THREADS = "dont-rely-on-gmail-threads"
    USE_LATEST_USER_EMAIL = "use-latest-user-email"
    AUTO_SET_NAME_FOR_BOSS_SUBMISSIONS = "auto-set-name-for-boss-submissions"
    NATIONWIDE_BOSS_R3_RELEASE = "nwR3December"
    NATIONWIDE_VERIFIED_SHELLS_PDS = "nwVerifiedShellsPDS"
    STUCK_SUBMISSION_ON_LOCKED_FILE = "stuckOnLockedFile"
    ENFORCE_ASSIGNED_TO_CONFIRM_DI = "enforce-assigned-to-confirm-di"
    ALLOW_CREATE_OR_REPLACE_FILES_FOR_VERIFIED_SUB = "allow-create-or-replace-files-for-verified-sub"
    ENABLE_VALIDATION_DO = "enable-validation-do"
    MOVE_FILES_THROUGH_DI = "move-files-through-di"
    COMPLETE_PROCESSING_DEPENDENCY_ON_MAIN_THREAD = "complete-processing-dependency-on-main-thread"
    USE_MDA_ENGINE = "use-mda-engine"
    USE_SMTP_FOR_EMAILS = "use-smtp-for-emails"
    AUTO_CC_NW_MOREINFO_EMAIL = "cc-nationwide-moreinfo"
    PDS_CUSTOMIZABLE_CLASSIFIERS = "pds-customizable-classifiers"
    """https://app.launchdarkly.com/projects/default/flags/pds-customizable-classifiers/"""
    EXTEND_EXTERNAL_REPORT_API_RESPONSE = "extend-external-report-api-response"
    SPLIT_BOWHEAD_GROUPS = "split-bowhead-groups"
    SECURA_MAPPINGS_ENABLED = "secura-mappings-enabled"
    SET_DEFAULT_OMNIVIEW_DOC_TYPE = "set-default-omniview-doc-type"
    USE_PRIORITY_FOR_TAXONOMIES = "use-priority-for-taxonomies"
    """https://app.launchdarkly.com/projects/default/flags/delete-email-on-raw-email-deletion/"""
    DELETE_EMAIL_ON_RAW_EMAIL_DELETION = "delete-email-on-raw-email-deletion"
    ALLOW_SUB_REVERTING = "allowSubReverting"
    CUSTOMIZABLE_CLASSIFIERS_V2 = "showNewClassifiers"
    """https://app.launchdarkly.com/projects/default/flags/showNewClassifiers"""
    ENABLE_CONFLICT_DETECTION_AGAINST_SHELLS = "enableConflictDetectionAgainstShells"
    """https://app.launchdarkly.com/projects/default/flags/enableConflictDetectionAgainstShells/"""
    BOWHEAD_CLEARING_BY_MATCHED_SUB_PREMISES = "bowheadClearingByMatchedSubmissionPremises"
    """https://app.launchdarkly.com/projects/default/flags/bowheadClearingByMatchedSubmissionPremises"""


class FeatureFlagsClient:
    _client: LDClient = None  # type: ignore

    @staticmethod
    def _init() -> None:
        if "IS_TEST_ENV" in os.environ:
            return
        if FeatureFlagsClient._client:
            return

        ldclient.set_config(Config(os.environ["LAUNCH_DARKLY_API_KEY"]))
        FeatureFlagsClient._client = ldclient.get()

    @staticmethod
    def is_feature_enabled(feature: FeatureType, user_email: str | None = None) -> bool:
        # for testing purposes
        if os.environ.get(feature.name, "false").lower() == "true":
            return True
        if "IS_TEST_ENV" in os.environ or os.environ.get("KALEPA_ENV", "dev") == "local":
            # default off for tests
            get_logger().warning("Feature flag not mocked in test environment, returning false", feature=feature)
            return False
        FeatureFlagsClient._init()

        user = _user if user_email is None else {"key": user_email}
        return FeatureFlagsClient._client.variation(feature, user, False)

    # TODO(ENG-27964): This is for now copy paste from common, but we should stop using this client and migrate to
    #  common one altogether
    @staticmethod
    def is_feature_enabled_in_submission_context(
        feature: StrEnum | str,
        submission_id: StrUUID,
        additional_context_info: dict = None,
        default_value: bool = False,
    ) -> bool:
        # for testing purposes
        if os.environ.get(feature.name, "false").lower() == "true":
            return True
        if "IS_TEST_ENV" in os.environ or os.environ.get("KALEPA_ENV", "dev") == "local":
            # default off for tests
            get_logger().warning("Feature flag not mocked in test environment, returning false", feature=feature)
            return False

        try:
            FeatureFlagsClient._init()
            builder = ContextBuilder(
                key=str(submission_id) if submission_id else str(uuid.uuid4()), copy_from=_submission
            )
            additional_context_info = additional_context_info or {}
            builder.try_set(attribute="submission_id", value=str(submission_id))
            for key, value in additional_context_info.items():
                builder.try_set(attribute=key, value=value if value is None or isinstance(value, str) else str(value))
            key = feature if isinstance(feature, str) else str(feature.value)
            return FeatureFlagsClient._client.variation(key=key, context=builder.build(), default=default_value)
        except Exception as e:
            get_logger().error(
                "Error while checking feature flag. Issue but not critical",
                exc_info=e,
                feature=feature,
                organization_id=submission_id,
            )
        return default_value

    @staticmethod
    def is_feature_enabled_for_request_user(feature: FeatureType) -> bool:
        FeatureFlagsClient._init()

        maybe_current_user: User | None = current_user
        if not current_user:
            return FeatureFlagsClient.is_feature_enabled(feature)

        org_params = {
            "organizationId": maybe_current_user.organization_id,
            "organizationName": maybe_current_user.organization.name,
        }
        params = {
            "kind": "user",
            "key": maybe_current_user.email,
            "email": maybe_current_user.email,
            "custom": org_params,
        }
        params.update(org_params)
        return FeatureFlagsClient._client.variation(feature, params, False)
