from datetime import datetime
from uuid import UUID
import hashlib

from marshmallow import (
    EXCLUDE,
    Schema,
    ValidationError,
    fields,
    post_load,
    pre_load,
    validates,
)
from marshmallow_enum import EnumField
from marshmallow_sqlalchemy import auto_field
from static_common.enums.contractor import ContractorSubmissionType
from static_common.enums.insurance import ProjectInsuranceType
from static_common.enums.submission import SubmissionStage

from copilot.models.submission_sync import (
    DryRunSubmissionMatch,
    MatcherDryRunResult,
    SubmissionSync,
    SubmissionSyncCoverage,
    SubmissionSyncIdentifiers,
    SubmissionSyncMatcherData,
    SubmissionSyncReport,
    SyncMatchingRunRequest,
)
from copilot.models.synchronization.submissions import (
    DeleteSyncRequests,
    MatcherType,
    RunSyncRequest,
)
from copilot.schemas.base import BaseSchema


class SubmissionSyncCoverageSchema(Schema):
    coverage_name = fields.String(required=True, allow_none=True)
    coverage_type = fields.String(required=False, allow_none=True, default=None)
    quoted_premium = fields.Float(required=False, allow_none=True, default=None)
    bound_premium = fields.Float(required=False, allow_none=True, default=None)
    total_premium = fields.Float(required=False, allow_none=True, default=None)
    limit = fields.Float(required=False, allow_none=True, default=None)
    attachment_point = fields.Float(required=False, allow_none=True, default=None)
    keep_other_coverage = fields.Boolean(required=False, allow_none=True, default=None)
    stage = EnumField(SubmissionStage, by_value=True, required=False)

    @post_load
    def make_object(self, data, **kwargs):
        return SubmissionSyncCoverage(**data)


class SubmissionSyncReportSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        load_instance = False
        model = SubmissionSyncReport
        unknown = EXCLUDE

    id = auto_field()
    created_at = auto_field()
    updated_at = auto_field()
    org_group = auto_field()

    @post_load
    def make_object(self, data, **kwargs):
        return SubmissionSyncReport(**data)


class SubmissionSyncIdentifiersSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        load_instance = False
        model = SubmissionSyncIdentifiers
        unknown = EXCLUDE

    id = auto_field()
    created_at = auto_field()
    updated_at = auto_field()
    quote_numbers = auto_field()
    policy_numbers = auto_field()
    submission_sync_id = auto_field(required=False)

    @post_load
    def make_object(self, data, **kwargs):
        return SubmissionSyncIdentifiers(**data)


class SubmissionSyncMatcherDataSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        load_instance = False
        model = SubmissionSyncMatcherData
        unknown = EXCLUDE

    id = auto_field()
    created_at = auto_field()
    updated_at = auto_field()
    named_insured = auto_field()
    named_insured_address = auto_field()
    submission_sync_id = auto_field(required=False)
    additional_data = fields.Dict(required=False, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return SubmissionSyncMatcherData(**data)


class SubmissionSyncSchema(BaseSchema):
    def __init__(self, *args, **kwargs):
        self._used_for_loading = kwargs.pop("used_for_loading", False)
        super().__init__(*args, **kwargs)

    class Meta(BaseSchema.Meta):
        load_instance = False
        model = SubmissionSync
        unknown = EXCLUDE

    id = auto_field()
    created_at = auto_field()
    policy_number = auto_field()
    submission_name = auto_field()
    underwriter_email = auto_field()
    stage = auto_field()
    received_date = auto_field()
    effective_date = auto_field()
    coverage_name = auto_field()
    coverage_type = auto_field()
    quoted_premium = auto_field()
    bound_premium = auto_field()
    organization_id = auto_field()
    applied_changes = fields.List(
        fields.Nested(lambda: SubmissionSyncSchema(exclude=("applied_changes", "pending_changes")), load_only=True)
    )
    pending_changes = fields.List(
        fields.Nested(lambda: SubmissionSyncSchema(exclude=("applied_changes", "pending_changes")), load_only=True)
    )
    applied = auto_field(missing=False)
    attempt = auto_field()
    declined_date = auto_field()
    source = auto_field()
    account_id = auto_field()
    is_renewal = auto_field()
    brokerage = auto_field()
    broker = auto_field()
    coverages = auto_field()
    direct_match_only = auto_field()
    created_date = auto_field(required=False, missing=None)
    quoted_date = auto_field(required=False, missing=None)
    bound_date = auto_field(required=False, missing=None)
    matcher_data = fields.Nested(SubmissionSyncMatcherDataSchema, allow_none=True, required=False, load_only=True)
    sync_identifiers = fields.Nested(SubmissionSyncIdentifiersSchema, allow_none=True, required=False, load_only=True)
    sync_report = fields.Nested(SubmissionSyncReportSchema, allow_none=True, required=False, load_only=True)
    contractor_submission_type = EnumField(ContractorSubmissionType, by_value=True, allow_none=True, required=False)
    project_insurance_type = EnumField(ProjectInsuranceType, by_value=True, allow_none=True, required=False)
    client_id_being_renewed = auto_field(required=False, missing=None)
    broker_email = auto_field(required=False, missing=None)
    premium = auto_field(required=False, missing=None)
    expired_premium = auto_field(required=False, missing=None)
    primary_state = auto_field(required=False, missing=None)
    client_stage_id = auto_field(required=False, missing=None)
    user_groups_to_match = auto_field(required=False, missing=None)
    policy_number_being_renewed = auto_field(required=False, missing=None)
    policy_status = auto_field(required=False, missing=None)
    policy_expiration_date = auto_field(required=False, missing=None)
    first_seen = auto_field(required=False, missing=None)
    replaces_client_id = auto_field(required=False, missing=None)
    replaced_by_client_id = auto_field(required=False, missing=None)
    is_new = fields.Boolean(required=False, missing=None, allow_none=True, dump_only=True)
    parent_client_id = auto_field(required=False, missing=None, allow_none=True)

    @validates("submission_name")
    def submission_name_not_empty_validator(self, data, **kwargs):
        if self._used_for_loading and (data is None or not data.strip()):
            raise ValidationError("submission_name must not be empty")

    # TODO(sudijovski) remove this once clients are updated and columns are removed
    @pre_load
    def set_missing_fields(self, data, **kwargs):
        # put coverages in the list
        coverages: list = data.get("coverages") or []
        if data.get("coverage_name") and not coverages:
            coverages = [
                {
                    "coverage_name": data.get("coverage_name"),
                    "coverage_type": data.get("coverage_type"),
                    "quoted_premium": data.get("quoted_premium"),
                    "bound_premium": data.get("bound_premium"),
                    "total_premium": data.get("total_premium"),
                }
            ]
            data["coverage_type"] = None
            data["coverage_name"] = None
        data["coverages"] = coverages
        data["direct_match_only"] = data.get("direct_match_only", False)
        return data

    @post_load
    def make_object(self, data, **kwargs):
        policy_number = data["policy_number"]
        organization_id = data["organization_id"]
        data["id"] = UUID(hashlib.md5(f"{policy_number}-{organization_id}".encode("utf8", "surrogatepass")).hexdigest())
        data["applied"] = data.get("applied", False)
        data["created_at"] = data.get("created_at", datetime.now().replace(tzinfo=None))
        data["attempt"] = data.get("attempt", 0)
        data["is_renewal"] = data.get("is_renewal")
        return SubmissionSync(**data)


class RunSyncRequestSchema(Schema):
    ids = fields.List(fields.UUID, allow_none=False, required=True)
    run_id = fields.String(allow_none=True, required=False)

    @post_load
    def make_object(self, data, **kwargs):
        return RunSyncRequest(**data)


class DeleteSyncRequestsSchema(Schema):
    client_ids = fields.List(fields.String, allow_none=False, required=True)
    organization_id = fields.Integer(allow_none=True, required=False)

    @post_load
    def make_object(self, data, **kwargs):
        return DeleteSyncRequests(**data)


class DryRunSubmissionMatchSchema(Schema):
    id = fields.UUID(allow_none=False, required=True)
    name = fields.String(allow_none=False, required=True)
    created_at = fields.DateTime(allow_none=False, required=True)
    matcher_type = EnumField(MatcherType, by_value=True, required=True, allow_none=False)
    matching_score = fields.Float(required=True, allow_none=False)

    @post_load
    def make_object(self, data, **kwargs):
        return DryRunSubmissionMatch(**data)


class MatcherDryRunResultSchema(Schema):
    sync_id = fields.UUID(allow_none=False, required=True)
    client_id = fields.String(allow_none=False, required=True)
    received_date = fields.DateTime(allow_none=False, required=True)
    name_in_report = fields.String(allow_none=False, required=True)
    submissions = fields.List(fields.Nested(DryRunSubmissionMatchSchema), allow_none=False, required=True)

    @post_load
    def make_object(self, data, **kwargs):
        return MatcherDryRunResult(**data)


class SyncMatchingRunRequestSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    run_id = fields.UUID(required=True, allow_none=False)
    organization_id = fields.Integer(required=True, allow_none=False)
    submission_sync_ids = fields.List(fields.UUID(allow_none=False), required=True, allow_none=False)
    producer_config = fields.Dict(required=False, allow_none=True)
    automatically_triggered = fields.Boolean(required=False, allow_none=True, default=False)
    assign_client_ids = fields.Boolean(required=False, allow_none=True, default=False)

    @post_load
    def make_object(self, data, **kwargs):
        return SyncMatchingRunRequest(**data)
