type: object
properties:
  id:
    type: string
    format: uuid
    pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
    readOnly: true
  policy_number:
    type: string
    nullable: false
  submission_name:
    type: string
    nullable: false
  underwriter_email:
    type: string
    nullable: true
    format: email
  stage:
    type: string
    nullable: true
    enum:
      - "INDICATED"
      - "ON_MY_PLATE"
      - "DECLINED"
      - "COMPLETED"
      - "QUOTED"
      - "WAITING_FOR_OTHERS"
      - "QUOTED_LOST"
      - "QUOTED_BOUND"
      - "CLEARING_ISSUE"
      - "BLOCKED"
      - "EXPIRED"
      - "CANCELED"
  received_date:
    type: string
    format: date-time
    nullable: true
  effective_date:
    type: string
    format: date-time
    nullable: true
  coverage_name:
    type: string
    nullable: true
  coverage_type:
    type: string
    nullable: true
    enum:
      - "PRIMARY"
      - "EXCESS"
  quoted_premium:
    type: number
    format: float
    nullable: true
  bound_premium:
    type: number
    format: float
    nullable: true
  organization_id:
    type: integer
    nullable: false
  attempt:
    type: integer
    nullable: false
    default: 0
  declined_date:
    type: string
    format: date-time
    nullable: true
  source:
    type: string
    nullable: false
  account_id:
    type: string
    nullable: true
  is_renewal:
    type: boolean
    nullable: true
  broker:
    type: string
    nullable: true
  brokerage:
    type: string
    nullable: true
  coverages:
    type: array
    default: [ ]
    nullable: true
    items:
      $ref: '../../v3.yml#/components/schemas/SubmissionSyncCoverage'
  direct_match_only:
    type: boolean
    nullable: true
    default: false
  matcher_data:
    nullable: true
    allOf:
      - $ref: '../../v3.yml#/components/schemas/SubmissionSyncMatcherData'
  sync_identifiers:
    nullable: true
    allOf:
      - $ref: '../../v3.yml#/components/schemas/SubmissionSyncIdentifiers'
  sync_report:
    nullable: true
    allOf:
      - $ref: '../../v3.yml#/components/schemas/SubmissionSyncReport'
  created_date:
    type: string
    format: date-time
    nullable: true
  quoted_date:
    type: string
    format: date-time
    nullable: true
  bound_date:
    type: string
    format: date-time
    nullable: true
  contractor_submission_type:
    type: string
    nullable: true
    enum:
      - "PROJECT"
      - "PRACTICE"
  project_insurance_type:
    type: string
    nullable: true
    enum:
      - "WRAP_UP"
      - "OWNERS_INTEREST"
      - "PROJECT_SPECIFIC"
  client_id_being_renewed:
    type: string
    nullable: true
  broker_email:
    type: string
    format: email
    nullable: true
  premium:
    type: number
    format: float
    nullable: true
  expired_premium:
    type: number
    format: float
    nullable: true
  primary_state:
    type: string
    nullable: true
  client_stage_id:
    type: integer
    nullable: true
  user_groups_to_match:
    type: array
    nullable: true
    items:
      type: string
  policy_number_being_renewed:
    type: string
    nullable: true
  policy_status:
    type: string
    nullable: true
  policy_expiration_date:
    type: string
    format: date-time
    nullable: true
  first_seen:
    type: string
    format: date-time
    nullable: true
  replaces_client_id:
    type: string
    nullable: true
  replaced_by_client_id:
    type: string
    nullable: true
  is_new:
    type: boolean
    nullable: true
  parent_client_id:
    type: string
    nullable: true
required:
  - policy_number
  - submission_name
  - organization_id
  - source
