"""Add support for sync groups

Revision ID: fa19c321c883
Revises: 3817a82814f5
Create Date: 2025-05-31 14:27:02.374385+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "fa19c321c883"
down_revision = "3817a82814f5"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "submission_sync",
        # this will refer to a parent policy number. In the olden days we started with using a policy number
        # which later was renamed to client_submission_id. However, this column is still used in the codebase
        sa.Column("parent_client_id", sa.String(), nullable=True, index=True),
    )
    op.create_index(
        "ix_submission_sync_parent_client_id_organization_id",
        "submission_sync",
        ["parent_client_id", "organization_id"],
        unique=False,
    )


def downgrade():
    op.drop_column("submission_sync", "parent_client_id")
