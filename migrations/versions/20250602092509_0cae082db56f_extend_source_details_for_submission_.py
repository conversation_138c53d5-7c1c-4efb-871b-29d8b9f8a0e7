"""extend source details for submission level extrated data

Revision ID: 0cae082db56f
Revises: fa19c321c883
Create Date: 2025-06-02 09:25:09.868960+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "0cae082db56f"
down_revision = "fa19c321c883"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("ALTER TYPE source_details ADD VALUE IF NOT EXISTS 'MANUAL';")


def downgrade():
    ...
