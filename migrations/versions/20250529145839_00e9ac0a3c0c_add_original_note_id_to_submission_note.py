"""Add original note id to submission note

Revision ID: 00e9ac0a3c0c
Revises: a395f3c3f104
Create Date: 2025-05-29 14:58:39.229601+00:00

"""

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "00e9ac0a3c0c"
down_revision = "a395f3c3f104"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "submission_note",
        sa.Column(
            "original_note_id",
            postgresql.UUID(as_uuid=True),
            sa.ForeignKey("submission_note.id", ondelete="SET NULL"),
        ),
    )


def downgrade():
    op.drop_column("submission_note", "original_note_id")
