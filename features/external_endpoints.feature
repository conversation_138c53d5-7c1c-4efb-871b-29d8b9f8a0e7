Feature: Client calls external endpoints to create reports and files

  Scenario: <PERSON>lient creates a report and attaches a file with client tags and later updates the file
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "yc2A8QCZm5HGQFRVJwjs4e3Xc99CWT10" that belongs to organization with id "6"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an external API client with create:reports scope
    When we POST report external
    """
    {
      "businesses": [
        {
          "requested_name": "The Oracle",
          "requested_address": "Somewhere in New York"
        }
      ],
      "name": "External report 1",
      "status": "cleared",
      "coverage_type": "primary",
      "external_id": "5112345A",
      "is_renewal": false,
      "broker": "<PERSON>",
      "brokerage": "The Matrix"
    }
    """
    And we POST external file with client tags attached to the report
    """
    {
      "file_content": "Random file content 1",
      "file_name": "file.csv",
      "body": {
        "comment": "This is a comment",
        "client_file_type": "Broker of Record",
        "tags": [
          {
            "tag1": "value1"
          },
          {
            "tag2": "value2"
          },
          {
            "boss_doc_id": "g37383u-hdhd-99"
          }
        ]
      }
    }
    """
    Then when we GET the file by id, it should have the following properties
    """
    {
      "client_file_type": "Broker of Record",
      "client_file_tags": {
        "file_description": "file.csv",
        "tag1": "value1",
        "tag2": "value2",
        "boss_doc_id": "g37383u-hdhd-99"
      }
    }
    """
    # trying to upload a file with the same boss_doc_id
    When we POST external file with client tags attached to the report
    """
    {
      "file_content": "Content is irrelevant in this case",
      "file_name": "duplicate_file.csv",
      "body": {
        "comment": "This is a comment",
        "client_file_type": "Broker of Record",
        "tags": [
          {
            "tag1": "value1"
          },
          {
            "tag2": "value2"
          },
          {
            "boss_doc_id": "g37383u-hdhd-99"
          }
        ]
      }
    }
    """
    Then the server should respond with the HTTP status code "409"
    When we POST external file with client tags attached to the report
    """
    {
      "file_content": "Random file content 2",
      "file_name": "file.csv",
      "body": {
        "comment": "This is a another comment",
        "client_file_type": "Submission file",
        "tags": {
          "tag3": "value2",
          "file_description": "Should be display name",
          "another_tag": "another value",
          "boss_doc_id": "boss_document_id"
        }
      }
    }
    """
    Then when we GET the file by id, it should have the following properties
    """
    {
      "display_name": "Should be display name",
      "client_file_type": "Submission file",
      "client_file_tags": {
        "tag3": "value2",
        "another_tag": "another value",
        "file_description": "Should be display name",
        "boss_doc_id": "boss_document_id"
      }
    }
    """
    When we PUT external request to update the file externally
    """
    {
      "client_file_type": "Another client type",
      "tags": [
        {
          "tag1": "value1"
        },
        {
          "file_description": "Some random text"
        },
        {
          "boss_doc_id": "Should not have any effect"
        }
      ]
    }
    """
    Then when we GET the file by id, it should have the following properties
    """
    {
      "display_name": "Some random text",
      "name": "file.csv",
      "client_file_type": "Another client type",
      "client_file_tags": {
        "tag1": "value1",
        "file_description": "Some random text",
        "boss_doc_id": "boss_document_id"
      }
    }
    """

  Scenario: Client creates a report and uploads file by using create url and then deletes it
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "yc2A8QCZm5HGQFRVJwjs4e3Xc99CWT10" that belongs to organization with id "6"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an external API client with create:reports scope
    When we POST report external
    """
    {
      "businesses": [
        {
          "requested_name": "The Oracle",
          "requested_address": "Somewhere in New York"
        }
      ],
      "name": "External report 2",
      "status": "cleared",
      "coverage_type": "primary",
      "external_id": "5112332A",
      "is_renewal": false,
      "broker": "John Smith",
      "brokerage": "The Matrix"
    }
    """
    And we UPDATE the Submission
    """
    {
      "processing_state": "COMPLETED",
      "is_verified": true
    }
    """
    # this is without any tags so we check backwards compatibility
    And we get a file upload url for the previously created report
    """
    {
      "file_name": "file.csv"
    }
    """
    Then the response should contain id and s3 key of the file
    When copilot-workers calls create file after we've uploaded the file
    Then we should receive a success response with matching file ids
    # this is with tags so we check the new functionality
    When we get a file upload url for the previously created report
    """
    {
      "file_name": "second_file.csv",
      "client_file_type": "Broker of Record",
      "tags": [
        {
          "tag1": "value1"
        },
        {
          "tag2": "value2"
        },
        {
          "boss_doc_id": "g37383u-hdhd-99"
        }
      ]
    }
    """
    Then the response should contain id and s3 key of the file
    When copilot-workers calls create file after we've uploaded the file
    Then we should receive a success response with matching file ids
    And when we GET the file by id, it should have the following properties
    """
    {
      "client_file_type": "Broker of Record",
      "client_file_tags": {
        "file_description": "second_file.csv",
        "tag1": "value1",
        "tag2": "value2",
        "boss_doc_id": "g37383u-hdhd-99"
      }
    }
    """
    And we UPDATE the file
    """
    {
      "processing_state": "COMPLETED"
    }
    """
    # checking if we get 409 when sending duplicate boss id
    When we get a file upload url for the previously created report
    """
    {
      "file_name": "another duplicate.csv",
      "client_file_type": "Broker of Record",
      "tags": [
        {
          "boss_doc_id": "g37383u-hdhd-99"
        }
      ]
    }
    """
    Then the server should respond with the HTTP status code "409"
    When we DELETE external the file by report and external_identifier
    Then when we GET the file by id, it should have the following properties
    """
    {
      "is_deleted": true,
      "external_identifier": null
    }
    """

  Scenario: Client creates a report and updates the status using external endpoint
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "auth0|12aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "auth0|32aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "yc2A8QCZm5HGQFRVJwjs4e3Xc99CWT10" that belongs to organization with id "6"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an external API client with create:reports scope
    When we POST report external
    """
    {
      "businesses": [
        {
          "requested_name": "The Oracle",
          "requested_address": "Somewhere in New York"
        }
      ],
      "name": "External report",
      "status": "cleared",
      "coverage_type": "primary",
      "external_id": "5112123A",
      "is_renewal": false,
      "broker": "John Smith",
      "brokerage": "The Matrix",
      "submission_status": "Awaiting Review"
    }
    """
    When we POST external client status update
    """
    {
      "custom_status": [
        {
          "name": "submission_stage",
          "value": "Quote"
        },
        {
          "name": "submission_status",
          "value": "Awaiting Review"
        }
      ]
    }
    """
    Then the server should respond with the HTTP status code "200"
    When we GET the previously created submission with expanded properties
    """
    ["client_stage_id", "client_stage_comment", "client_clearing_status"]
    """
    Then the submission should have the following properties
    """
    {
      "stage": "ON_MY_PLATE",
      "client_stage_id": 62,
      "client_stage_comment": null
    }
    """
    And we verify the submission
    And we POST external client status update
    """
    {
      "custom_status": [
        {
          "name": "non_existant_stage",
          "value": "Quote"
        }
      ]
    }
    """
    Then the server should respond with the HTTP status code "400"
    When we POST external client status update
    """
    {
      "custom_status": [
        {
          "name": "submission_status",
          "value": "Quoted and bound"
        }
      ]
    }
    """
    Then the server should respond with the HTTP status code "400"
    When we POST external client status update
    """
    {
      "custom_status": [
        {
          "name": "submission_status",
          "value": "Closed"
        },
        {
          "name": "close_disposition",
          "value": "Released"
        },
        {
          "name": "close_reason",
          "value": "Closed"
        },
        {
          "name": "close_subreason",
          "value": "Bound by Competitor"
        },
        {
          "name": "closing_notes",
          "value": "Closing notes"
        }
      ]
    }
    """
    Then the server should respond with the HTTP status code "200"
    When we GET the previously created submission with expanded properties
    """
    ["client_stage_id", "client_stage_comment", "client_clearing_status"]
    """
    Then the submission should have the following properties
    """
    {
      "stage": "QUOTED_LOST",
      "client_stage_id": 67,
      "client_stage_comment": "Closing notes"
    }
    """
    When we POST external client status update
    """
    {
      "custom_status": [
        {
          "name": "submission_status",
          "value": "Awaiting Review"
        }
      ]
    }
    """
    Then the server should respond with the HTTP status code "200"
    When we PATCH the report using the external endpoint
    """
    {
      "name": "Renamed external report",
      "status": "cleared",
      "coverage_type": "primary",
      "external_id": "5112123A",
      "policy_expiration_date": "2023-10-28",
      "broker": "The One",
      "broker_email": "<EMAIL>",
      "brokerage": "The Matrix",
      "brokerage_office": "Somewhere over the rainbow",
      "primary_naics_code": "492110",
      "assigned_user_email": "<EMAIL>",
      "additional_identifiers": [
         {
             "type": "quote_number",
             "identifier": "3222131"
         }
      ]
    }
    """
    When we GET the previously created submission with expanded properties
    """
    [
        "policy_expiration_date",
        "broker",
        "brokerage_contact",
        "brokerage",
        "primary_naics_code",
        "identifiers",
        "brokerage_office",
        "client_clearing_status",
        "assigned_underwriters"
    ]
    """
    Then the submission should have the following properties
    """
    {
      "name": "Renamed external report",
      "policy_expiration_date": "2023-10-28T00:00:00",
      "brokerage_office": "Somewhere over the rainbow",
      "broker": {
        "name": "The One",
        "email": "<EMAIL>"
      },
      "brokerage_contact": {
        "name": "The One",
        "email": "<EMAIL>"
      },
      "primary_naics_code": "NAICS_492110"
    }
    """
    And the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have the following identifiers
    """
    {
      "quote_number": "3222131"
    }
    """
    When we PATCH the report using the external endpoint
    """
    {
      "name": "Renamed external report",
      "status": "cleared",
      "coverage_type": "primary",
      "external_id": "5112123A",
      "policy_expiration_date": "2023-10-28",
      "broker": "Thomas Anderson",
      "broker_email": "<EMAIL>",
      "correspondence_contact_name": "The One",
      "correspondence_contact_email": "<EMAIL>",
      "sub_producer_name": "Some random agent",
      "sub_producer_email": "random_agent@email",
      "primary_naics_code": "492110",
      "additional_identifiers": [
         {
             "type": "quote_number",
             "identifier": "12345678"
         },
         {
             "type": "policy_number",
             "identifier": "87654321"
         }
      ]
    }
    """
    When we GET the previously created submission with expanded properties
    """
    [
        "policy_expiration_date",
        "broker",
        "brokerage_contact",
        "brokerage",
        "primary_naics_code",
        "identifiers",
        "brokerage_office",
        "sub_producer_name",
        "sub_producer_email",
        "assigned_underwriters"
    ]
    """
    Then the submission should have the following properties
    """
    {
      "name": "Renamed external report",
      "policy_expiration_date": "2023-10-28T00:00:00",
      "sub_producer_name": "Some random agent",
      "sub_producer_email": "random_agent@email",
      "broker": {
        "name": "Thomas Anderson",
        "email": "<EMAIL>"
      },
      "brokerage_contact": {
        "name": "The One",
        "email": "<EMAIL>"
      },
      "primary_naics_code": "NAICS_492110"
    }
    """
    And the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have the following identifiers
    """
    {
      "quote_number": "12345678",
      "policy_number": "87654321"
    }
    """
    # delete the UW
    When we PATCH the report using the external endpoint
    """
    {
      "name": "Renamed external report",
      "status": "cleared",
      "coverage_type": "primary",
      "external_id": "5112123A",
      "policy_expiration_date": "2023-10-28",
      "broker": "Thomas Anderson",
      "broker_email": "<EMAIL>",
      "correspondence_contact_name": "The One",
      "correspondence_contact_email": "<EMAIL>",
      "assigned_user_email": "",
      "sub_producer_name": "Some random agent",
      "sub_producer_email": "random_agent@email",
      "primary_naics_code": "492110",
      "additional_identifiers": [
         {
             "type": "quote_number",
             "identifier": "12345678"
         },
         {
             "type": "policy_number",
             "identifier": ""
         }
      ]
    }
    """
    When we GET the previously created submission with expanded properties
    """
    [
        "policy_expiration_date",
        "broker",
        "brokerage_contact",
        "brokerage",
        "primary_naics_code",
        "identifiers",
        "brokerage_office",
        "sub_producer_name",
        "sub_producer_email",
        "assigned_underwriters"
    ]
    """
    Then the submission should be assigned to "0" underwriters
    And the submission should have the following identifiers
    """
    {
      "quote_number": "12345678"
    }
    """
    When we POST external client status update
    """
    {
      "custom_status": [
        {
          "name": "submission_status",
          "value": "Quoted"
        }
      ]
    }
    """
    Then the server should respond with the HTTP status code "200"
    When we GET the previously created submission with expanded properties
    """
    ["client_stage_id", "client_stage_comment"]
    """
    Then the submission should have the following properties
    """
    {
      "stage": "QUOTED",
      "client_stage_id": 65,
      "client_stage_comment": null
    }
    """
    When we POST external client status update
    """
    {
      "custom_status": [
        {
          "name": "submission_status",
          "value": "Bound-Sent"
        },
        {
          "name": "closing_notes",
          "value": "Bound notes"
        }
      ]
    }
    """
    Then the server should respond with the HTTP status code "200"
    When we GET the previously created submission with expanded properties
    """
    ["client_stage_id", "client_stage_comment"]
    """
    Then the submission should have the following properties
    """
    {
      "stage": "QUOTED_BOUND",
      "client_stage_id": 60,
      "client_stage_comment": "Bound notes"
    }
    """
    When we add external notes
    """
    {
      "notes": "External notes"
    }
    """
    Then the server should respond with the HTTP status code "201"

  Scenario: Client creates a verified shells and updates the client status
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "yc2A8QCZm5HGQFRVJwjs4e3Xc99CWT10" that belongs to organization with id "6"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an external API client with create:reports scope
    When we POST report external
    """
    {
      "businesses": [
        {
          "requested_name": "The Oracle",
          "requested_address": "Somewhere in New York"
        }
      ],
      "name": "And yet another external report",
      "status": "cleared",
      "coverage_type": "primary",
      "external_id": "5352123A",
      "is_renewal": false,
      "broker": "John Smith",
      "brokerage": "The Matrix"
    }
    """
    And we UPDATE the Submission
    """
    {
      "is_verified_shell": true
    }
    """
    When we POST external client status update
    """
    {
      "custom_status": [
        {
          "name": "submission_status",
          "value": "Closed"
        },
        {
          "name": "close_disposition",
          "value": "Released"
        },
        {
          "name": "close_reason",
          "value": "Closed"
        },
        {
          "name": "close_subreason",
          "value": "Bound by Competitor"
        },
        {
          "name": "closing_notes",
          "value": "Closing notes"
        }
      ]
    }
    """
    Then the server should respond with the HTTP status code "200"
    When we GET the previously created submission with expanded properties
    """
    ["client_stage_id", "client_stage_comment", "client_clearing_status"]
    """
    Then the submission should have the following properties
    """
    {
      "stage": "ON_MY_PLATE",
      "client_stage_id": 67,
      "client_stage_comment": "Closing notes"
    }
    """
    When we UPDATE the Submission
    """
    {
      "created_at": "2024-10-28T00:00:00"
    }
    """
    When we POST external client status update
    """
    {
      "custom_status": [
        {
          "name": "submission_status",
          "value": "Bound-Sent"
        },
        {
          "name": "closing_notes",
          "value": "Bound notes"
        }
      ]
    }
    """
    Then the server should respond with the HTTP status code "200"
    When we GET the previously created submission with expanded properties
    """
    ["client_stage_id", "client_stage_comment"]
    """
    Then the submission should have the following properties
    """
    {
      "stage": "QUOTED_BOUND",
      "client_stage_id": 60,
      "client_stage_comment": "Bound notes"
    }
    """


  Scenario: Client creates a report with with broker and corr. contact. Later the report is verified
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "yc2A8QCZm5HGQFRVJwjs4e3Xc99CWT10" that belongs to organization with id "6"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an external API client with create:reports scope
    When we POST report external
    """
    {
      "businesses": [
        {
          "requested_name": "The Oracle",
          "requested_address": "Somewhere in New York"
        }
      ],
      "name": "External report",
      "status": "cleared",
      "coverage_type": "primary",
      "external_id": "5112124A",
      "is_renewal": false,
      "policy_expiration_date": "2023-10-28",
      "broker": "The One",
      "broker_email": "<EMAIL>",
      "correspondence_contact_name": "Thomas Anderson",
      "correspondence_contact_email": "<EMAIL>",
      "sub_producer_name": "Some random agent",
      "sub_producer_email": "random_agent@email",
      "brokerage": "The Matrix",
      "brokerage_office": "Somewhere over the rainbow",
      "submission_status": "Bound-Sent"
    }
    """
    When we GET the previously created submission with expanded properties
    """
    [
        "client_stage_id",
        "client_stage_comment",
        "policy_expiration_date",
        "broker",
        "brokerage_contact",
        "brokerage",
        "primary_naics_code",
        "identifiers"
    ]
    """
    Then the submission should have the following properties
    """
    {
      "stage": "ON_MY_PLATE",
      "client_stage_id": 60,
      "policy_expiration_date": "2023-10-28T00:00:00",
      "broker": {
        "name": "The One",
        "email": "<EMAIL>"
      },
      "brokerage_contact": {
        "name": "Thomas Anderson",
        "email": "<EMAIL>"
      }
    }
    """
    When we verify the submission
    And we GET the previously created submission with expanded properties
    """
    ["client_stage_id"]
    """
    Then the submission should have the following properties
    """
    {
      "stage": "QUOTED_BOUND",
      "client_stage_id": 60
    }
    """

  Scenario: Client creates two reports with different external ids. Updates one successfully and second update fails
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "yc2A8QCZm5HGQFRVJwjs4e3Xc99CWT10" that belongs to organization with id "6"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an external API client with create:reports scope
    When we POST report external
    """
    {
      "businesses": [
        {
          "requested_name": "The Oracle",
          "requested_address": "Somewhere in New York"
        }
      ],
      "name": "First external report with id 5002030A",
      "status": "cleared",
      "coverage_type": "primary",
      "external_id": "5002030A",
      "is_renewal": false,
      "policy_expiration_date": "2023-10-28",
      "broker": "The One",
      "broker_email": "<EMAIL>",
      "correspondence_contact_name": "Thomas Anderson",
      "correspondence_contact_email": "<EMAIL>",
      "sub_producer_name": "Some random agent",
      "sub_producer_email": "random_agent@email",
      "brokerage": "The Matrix",
      "brokerage_office": "Somewhere over the rainbow"
    }
    """
    When we POST report external
    """
    {
      "businesses": [
        {
          "requested_name": "The Oracle",
          "requested_address": "Somewhere in New York"
        }
      ],
      "name": "Second external report with changing id 5002031A",
      "status": "cleared",
      "coverage_type": "primary",
      "external_id": "5002031A",
      "is_renewal": false,
      "policy_expiration_date": "2023-10-28",
      "broker": "The One",
      "broker_email": "<EMAIL>",
      "correspondence_contact_name": "Thomas Anderson",
      "correspondence_contact_email": "<EMAIL>",
      "sub_producer_name": "Some random agent",
      "sub_producer_email": "random_agent@email",
      "brokerage": "The Matrix",
      "brokerage_office": "Somewhere over the rainbow"
    }
    """
    And we GET the previously created submission with expanded properties
    """
    [
      "client_submission_ids"
    ]
    """
    Then the submission should have the following client ids
    """
    ["5002031A"]
    """
    When we PATCH the report using the external endpoint
    """
    {
      "name": "Second external report with changing id 5002032A",
      "external_id": "5002032A"
    }
    """
    And we GET the previously created submission with expanded properties
    """
    [
      "client_submission_ids"
    ]
    """
    Then the submission should have the following client ids
    """
    ["5002032A"]
    """
    When we PATCH the report using the external endpoint
    """
    {
      "name": "Second external report with changing id 5002030A",
      "external_id": "5002030A"
    }
    """
    Then the server should respond with the HTTP status code "409"
    When we GET the previously created submission with expanded properties
    """
    [
      "client_submission_ids"
    ]
    """
    Then the submission should have the following client ids
    """
    ["5002032A"]
    """
    And the submission should have the following properties
    """
    {
      "name": "Second external report with changing id 5002032A"
    }
    """

  Scenario: Create coverage splits and check if an automatic bundle is created
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "yc2A8QCZm5HGQFRVJwjs4e3Xc99CWT10" that belongs to organization with id "6"
    Given we have clearing enabled for the organization with id "6" with configuration
    """
    {
      "enabled": true,
      "statuses": [
        "Clear",
        "Secondary",
        "Duplicate"
      ],
      "clearedStatuses": [
        "Clear"
      ],
      "notClearedStatuses": [
        "Secondary",
        "Duplicate"
      ]
    }
    """
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an external API client with create:reports scope
    When we POST report external
    """
    {
      "businesses": [],
      "user_email": "<EMAIL>",
      "name": "External report that will be automatically bundled",
      "is_renewal": "false",
      "external_id": "50000009C",
      "proposed_effective_date": "2025-04-01",
      "coverage_type": "primary",
      "broker": "Charles Xavier",
      "brokerage": "X-Men Insurance Brokerage",
      "brokerage_office": "Westchester, NY",
      "additional_identifiers": [
        {
          "type": "quote_number",
          "identifier": "***********"
        }
      ],
      "policy_expiration_date": "2026-04-01",
      "submission_status": "Awaiting Review",
      "broker_email": "<EMAIL>",
      "sub_producer_name": "Ororo Munroe",
      "sub_producer_email": null,
      "client_clearing_status": "Clear"
    }
    """
    And we verify the submission
    When we POST report external
    """
    {
      "businesses": [],
      "user_email": "<EMAIL>",
      "name": "External report that will be automatically bundled",
      "is_renewal": "false",
      "external_id": "50000001A",
      "proposed_effective_date": "2025-04-01",
      "coverage_type": "excess",
      "broker": "Charles Xavier",
      "brokerage": "X-Men Insurance Brokerage",
      "brokerage_office": "Westchester, NY",
      "additional_identifiers": [
        {
          "type": "quote_number",
          "identifier": "***********"
        }
      ],
      "policy_expiration_date": "2026-04-01",
      "submission_status": "Awaiting Review",
      "broker_email": "<EMAIL>",
      "sub_producer_name": "Ororo Munroe",
      "sub_producer_email": null,
      "client_clearing_status": "Clear"
    }
    """
    And we verify the submission
    Then the report should be bundled with another report with external id "50000009C"
