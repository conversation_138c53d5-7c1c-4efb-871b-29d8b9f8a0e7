from unittest.mock import ANY, MagicMock, call, patch
from uuid import UUID, uuid4
import datetime

from pytest import raises
from static_common.enums.permission import PermissionType
from werkzeug.exceptions import BadRequest, NotFound

from copilot.clients.knock import NOTIFICATION_TIMESTAMP_FORMAT, NotificationType
from copilot.logic.generated_notes import create_text_note
from copilot.logic.reports import handle_shadow_report_verification
from copilot.models import db
from copilot.models.reports import ReportPermission, SubmissionNote
from copilot.models.types import ReportShadowType
from copilot.v3.controllers.submission_notes import (
    add_recommendation_submission_notes,
    add_submission_note,
    add_submission_note_external,
    create_or_replace_note,
    get_submission_notes,
    remove_submission_note,
    update_submission_note,
)
from tests.integration.factories import (
    organization_fixture,
    report_and_submission_fixture,
    report_fixture,
    report_shadow_dependency_fixture,
    submission_fixture,
    submission_note_fixture,
    user_fixture,
)

NOTE_WITH_XSS = (
    '{"root":{"children":[{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"Totally'
    " Safe"
    ' Link","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"link","version":1,"target":null,"url":"javascript:alert(document.cookie)"}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}'
)


def test_add_submission_note(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    report, submission = report_and_submission_fixture()

    mocker.patch("flask_login.utils._get_user", return_value=user)
    add_submission_note(
        str(submission.id),
        {
            "options": {"users_to_notify": [], "note_html": ""},
            "submission_note": {"text": "lorem ipsum", "html_content": "abecadlo"},
        },
    )

    notes = SubmissionNote.query.all()

    assert len(notes) == 1

    note = notes[0]

    assert note.text == "lorem ipsum"

    assert note.created_at is not None
    assert note.author_id == 1

    assert note.updated_at is None
    assert note.last_edit_by_id is None

    assert note.html_content == "abecadlo"


def test_add_recommendation_submission_notes(app_context, mocker):
    organization_fixture()
    user_fixture()
    _, submission_1 = report_and_submission_fixture()
    _, submission_2 = report_and_submission_fixture()

    db.session.commit()

    submission_id_1 = submission_1.id
    submission_id_2 = submission_2.id
    rule_id_1 = uuid4()
    unexpected_text: str = "shouldn't be here"
    submission_note_fixture(submission_id=submission_id_2, rule_id=rule_id_1, text=unexpected_text)
    db.session.commit()

    mocker.patch("flask_login.utils._get_user", return_value=MagicMock(is_internal_machine_user=True))

    expected_text_1: str = "This note should actually be created"
    expected_text_2: str = (
        "This note should also be created because we allow multiple notes per (submission_id, rule_id) if they are"
        " added during the same request"
    )
    expected_text_3: str = "This note should be updated instead of unexpected text"
    unexpected_text2: str = "its also incorrect"

    note_request_1: dict = {
        "submission_id": str(submission_id_1),
        "rule_id": str(uuid4()),
        "text": expected_text_1,
    }
    note_request_2: dict = {
        "submission_id": str(submission_id_1),
        "rule_id": str(uuid4()),
        "text": expected_text_2,
    }
    note_request_3: dict = {
        "submission_id": str(submission_id_2),
        "rule_id": str(rule_id_1),
        "text": unexpected_text2,
    }
    add_recommendation_submission_notes([note_request_1, note_request_2, note_request_3])

    note_request_4: dict = {
        "submission_id": str(submission_id_2),
        "rule_id": str(rule_id_1),
        "text": expected_text_3,
    }

    add_recommendation_submission_notes([note_request_4])

    note_request_5: dict = {
        "submission_id": str(submission_id_2),
        "rule_id": str(rule_id_1),
        "text": expected_text_3,
    }

    add_recommendation_submission_notes([note_request_5])

    notes = SubmissionNote.query.all()

    assert 3 == len(notes)
    assert any(note for note in notes if expected_text_1 in note.text)
    assert any(note for note in notes if expected_text_2 in note.text)
    assert any(note for note in notes if expected_text_3 in note.text)
    assert not any(note for note in notes if unexpected_text in note.text)
    assert not any(note for note in notes if unexpected_text2 in note.text)


def test_add_recommendation_submission_notes_remove_stale_notes(app_context, mocker):
    organization_fixture()
    user_fixture()
    _, submission_1 = report_and_submission_fixture()
    _, submission_2 = report_and_submission_fixture()

    db.session.commit()

    submission_id_1 = submission_1.id
    submission_id_2 = submission_2.id
    rule_id_1 = uuid4()
    rule_id_2 = uuid4()
    unexpected_text: str = "shouldn't be here"
    expected_text: str = "should be here"
    expected_text_2: str = "user edited, should be here"
    submission_note_fixture(submission_id=submission_id_1, rule_id=rule_id_1, note=unexpected_text)
    submission_note_fixture(submission_id=submission_id_1, rule_id=rule_id_2, last_edit_by_id=1, note=expected_text_2)
    submission_note_fixture(submission_id=submission_id_2, rule_id=uuid4(), note=expected_text)
    db.session.commit()

    mocker.patch("flask_login.utils._get_user", return_value=MagicMock(is_internal_machine_user=True))

    note_request_1: dict = {
        "submission_id": str(submission_id_1),
        "rule_id": str(rule_id_1),
        "text": "",
        "is_delete_request": True,
    }
    note_request_2: dict = {
        "submission_id": str(submission_id_1),
        "rule_id": str(rule_id_2),
        "text": "",
        "is_delete_request": True,
    }
    add_recommendation_submission_notes([note_request_1, note_request_2])

    notes = SubmissionNote.query.all()

    assert 2 == len(notes)
    assert any(note for note in notes if expected_text in note.text)
    assert any(note for note in notes if expected_text_2 in note.text)
    assert not any(note for note in notes if unexpected_text in note.text)


def test_add_submission_note_notifies_tagged_users(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    user_2 = user_fixture(id=2)
    user_3 = user_fixture(id=3)
    report, submission = report_and_submission_fixture()

    mocker.patch("flask_login.utils._get_user", return_value=user)

    with patch("copilot.clients.knock.KnockClient._publish") as publish_mock:
        expected_note_html = "test html"
        expected_note_text = "hello @Test Tester (<EMAIL>) and @Jest Jester (<EMAIL>)"
        add_submission_note(
            str(submission.id),
            {
                "options": {
                    "users_to_notify": [2, 3],
                    "note_html": expected_note_html,
                },
                "submission_note": {"text": expected_note_text},
            },
        )
        note = SubmissionNote.query.all()[0]
        expected_notification_type = NotificationType.recipient_only("user-tagged-in-note")
        expected_data = {
            "noteId": str(note.id),
            "html": expected_note_html,
            "lastModifiedTimestamp": note.created_at.strftime(NOTIFICATION_TIMESTAMP_FORMAT),
            "reportId": str(submission.report.id),
            "reportName": submission.report.name,
            "linkToNotes": f"{submission.report.get_url()}?openNotes=true",
            "effectiveDate": None,
            "agent": None,
            "agency": None,
            "recommendation": None,
            "isRenewal": False,
        }
        publish_mock.assert_has_calls(
            [
                call(user.id, user_2, expected_notification_type, expected_data),
                call(user.id, user_3, expected_notification_type, expected_data),
            ]
        )
        note.text = expected_note_text


def __assert_correct_report_permission_created(
    grantee_user_id: int, report_id: UUID, message: str, permissions: list[ReportPermission]
) -> None:
    assert any(
        p
        for p in permissions
        if p.grantee_user_id == grantee_user_id
        and p.permission_type == PermissionType.EDITOR
        and p.is_referral == True
        and p.report_id == report_id
        and p.message == message
    )


def test_add_submission_note_refers_users(app_context, mocker):
    organization_fixture()
    user = user_fixture(
        email="<EMAIL>", name="testaroo", registered_in_knock=True, external_id="fakeexternal1"
    )
    user_2 = user_fixture(id=2, registered_in_knock=True, external_id="fakeexternal2")
    user_3 = user_fixture(id=3, registered_in_knock=True, external_id="fakeexternal3")
    report, submission = report_and_submission_fixture()

    mocker.patch("flask_login.utils._get_user", return_value=user)

    with patch("copilot.clients.knock.KnockClient._publish") as publish_mock:
        expected_users_to_refer: list[int] = [user_2.id, user_3.id]
        expected_note_html: str = "test html"
        expected_note_text: str = "test text"
        add_submission_note(
            str(submission.id),
            {
                "options": {
                    "users_to_notify": [],
                    "users_to_refer": expected_users_to_refer,
                    "note_html": expected_note_html,
                },
                "submission_note": {"text": expected_note_text},
            },
        )
        note = SubmissionNote.query.all()[0]

        expected_notification_type = NotificationType.recipient_only("submission-referred-to-you")
        publish_mock.assert_has_calls(
            [
                call(user.id, user_2, expected_notification_type, ANY),
                call(user.id, user_3, expected_notification_type, ANY),
            ],
            any_order=True,
        )

        permissions = ReportPermission.query.all()
        assert 3 == len(permissions)
        __assert_correct_report_permission_created(
            grantee_user_id=user_2.id, report_id=report.id, message=expected_note_html, permissions=permissions
        )
        __assert_correct_report_permission_created(
            grantee_user_id=user_3.id, report_id=report.id, message=expected_note_html, permissions=permissions
        )

        assert expected_users_to_refer == note.referred_to_user_ids


def test_add_submission_note_validates_against_xss_in_note_text(app_context, mocker):
    organization_fixture()
    organization_fixture(id=2)
    user = user_fixture(
        email="<EMAIL>", name="testaroo", registered_in_knock=True, external_id="fakeexternal1"
    )
    report, submission = report_and_submission_fixture()

    mocker.patch("flask_login.utils._get_user", return_value=user)

    with patch("copilot.clients.knock.KnockClient._publish") as publish_mock:
        with raises(BadRequest):
            add_submission_note(
                str(submission.id),
                {
                    "options": {
                        "users_to_notify": [],
                        "users_to_refer": [],
                        "note_html": "html field doesnt matter here",
                    },
                    "submission_note": {"text": NOTE_WITH_XSS},
                },
            )


def test_add_submission_note_validates_against_xss_in_note_html(app_context, mocker):
    organization_fixture()
    organization_fixture(id=2)
    user = user_fixture(
        email="<EMAIL>", name="testaroo", registered_in_knock=True, external_id="fakeexternal1"
    )
    report, submission = report_and_submission_fixture()

    mocker.patch("flask_login.utils._get_user", return_value=user)

    with patch("copilot.clients.knock.KnockClient._publish") as publish_mock:
        with raises(BadRequest):
            add_submission_note(
                str(submission.id),
                {
                    "options": {
                        "users_to_notify": [],
                        "users_to_refer": [],
                        "note_html": '<a href="javascript:alert(document.cookie)">Fake link</a>',
                    },
                    "submission_note": {"text": "note text doesnt matter here"},
                },
            )


def test_add_submission_note_referring_an_invalid_user_raises_exception(app_context, mocker):
    organization_fixture()
    organization_fixture(id=2)
    user = user_fixture(
        email="<EMAIL>", name="testaroo", registered_in_knock=True, external_id="fakeexternal1"
    )
    user_2 = user_fixture(id=2, registered_in_knock=True, external_id="fakeexternal2", organization_id=2)
    report, submission = report_and_submission_fixture()

    mocker.patch("flask_login.utils._get_user", return_value=user)

    with patch("copilot.clients.knock.KnockClient._publish") as publish_mock:
        with raises(NotFound):
            add_submission_note(
                str(submission.id),
                {
                    "options": {
                        "users_to_notify": [],
                        "users_to_refer": [user_2.id],
                        "note_html": "test html",
                    },
                    "submission_note": {"text": "test text"},
                },
            )
            publish_mock.assert_not_called()
            permissions = ReportPermission.query.all()
            assert 1 == len(permissions)
            assert user.id == permissions[0].grantee_user_id


def test_update_submission_note(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    user_2 = user_fixture(id=2)
    user_4 = user_fixture(id=4)
    report, submission = report_and_submission_fixture()
    note = submission_note_fixture(
        submission_id=submission.id,
        text="hello @Test Tester (<EMAIL>) and @Jest Jester (<EMAIL>)",
    )

    db.session.commit()

    mocker.patch("flask_login.utils._get_user", return_value=user)

    with patch("copilot.clients.knock.KnockClient._publish") as publish_mock:
        new_text = "my new cool text"
        note_html = "test html"
        update_submission_note(
            str(note.id),
            {
                "options": {
                    "users_to_notify": [2, 4],
                    "note_html": note_html,
                },
                "submission_note": {"text": new_text, "submission_id": str(submission.id)},
            },
        )
        expected_notification_type = NotificationType.recipient_only("user-tagged-in-note")
        expected_data = {
            "noteId": str(note.id),
            "html": note_html,
            "lastModifiedTimestamp": note.updated_at.strftime(NOTIFICATION_TIMESTAMP_FORMAT),
            "reportId": str(submission.report.id),
            "reportName": submission.report.name,
            "linkToNotes": f"{submission.report.get_url()}?openNotes=true",
            "effectiveDate": None,
            "agent": None,
            "agency": None,
            "recommendation": None,
            "isRenewal": False,
        }
        publish_mock.assert_has_calls(
            [
                call(user.id, user_2, expected_notification_type, expected_data),
                call(user.id, user_4, expected_notification_type, expected_data),
            ]
        )
        assert note.text == new_text
        assert note.updated_at is not None
        assert note.last_edit_by_id == 1


def test_update_submission_note_during_shadow_verification(app_context, mocker):
    org = organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    report = report_fixture(
        owner_id=user.id,
        organization_id=org.id,
        tier=1,
        name=f"Shadowed report",
        shadow_type=ReportShadowType.HAS_ACTIVE_SHADOW,
    )
    shadow_report = report_fixture(
        owner_id=user.id,
        organization_id=org.id,
        name=f"Shadow report",
        shadow_type=ReportShadowType.IS_ACTIVE_SHADOW,
    )
    report_shadow_dependency_fixture(report_id=report.id, shadow_report_id=shadow_report.id)
    submission = submission_fixture(
        owner_id=user.id,
        processing_state="COMPLETED",
        report=report,
        is_manual_verified=False,
        is_auto_processed=True,
        updated_at=datetime.datetime.now(),
    )
    shadow_submission = submission_fixture(
        owner_id=user.id,
        report=shadow_report,
        is_manual_verified=False,
        is_auto_processed=True,
        updated_at=datetime.datetime.now(),
    )
    original_text_1 = "Test 123"
    original_text_2 = "Test 456"
    rule_id = uuid4()
    note_1 = submission_note_fixture(submission_id=submission.id, note=original_text_1, rule_id=rule_id)
    note_2 = submission_note_fixture(submission_id=submission.id, note=original_text_2, rule_id=None)
    # simulate rule-generated note appearing on shadow as well
    submission_note_fixture(submission_id=shadow_submission.id, note=original_text_1, rule_id=rule_id)
    db.session.commit()

    handle_shadow_report_verification(shadow_submission)

    with patch("copilot.clients.knock.KnockClient._publish") as publish_mock:
        note_html = "test html"
        text_1 = "All we ever hear from you is"
        text_2 = "Blah, blah, blah, blah, blah"

        update_submission_note(
            str(note_1.id),
            {
                "options": {
                    "note_html": note_html,
                    "users_to_notify": [],
                },
                "submission_note": {"text": text_1, "submission_id": str(submission.id)},
            },
        )
        update_submission_note(
            str(note_2.id),
            {
                "options": {
                    "note_html": note_html,
                    "users_to_notify": [],
                },
                "submission_note": {"text": text_2, "submission_id": str(submission.id)},
            },
        )
        assert {note.text for note in report.submission.submission_notes} == {text_1, text_2}


def test_remove_submission_note(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    report, submission = report_and_submission_fixture()

    note = submission_note_fixture(submission_id=submission.id)
    db.session.commit()

    mocker.patch("flask_login.utils._get_user", return_value=user)
    remove_submission_note(str(note.id))

    notes = SubmissionNote.query.all()

    assert len(notes) == 0


def test_get_submission_notes(app_context, mocker):
    organization_fixture()
    author = user_fixture(id=42, name="test tester")

    user_who_updated = user_fixture(id=43, name="dwayne johnson")
    report, submission = report_and_submission_fixture(owner_id=author.id)

    submission_note_fixture(submission_id=submission.id, author_id=author.id)
    submission_note_fixture(submission_id=submission.id, author_id=author.id, last_edit_by_id=user_who_updated.id)
    db.session.commit()

    mocker.patch("flask_login.utils._get_user", return_value=author)
    notes = get_submission_notes(str(submission.id))

    assert len(notes) == 2
    assert notes[0].get("author_name") == "test tester"
    assert notes[0].get("last_editor_name") is None
    assert notes[1].get("author_name") == "test tester"
    assert notes[1].get("last_editor_name") == "dwayne johnson"


def test_create_or_replace_note_creates_new_note_if_not_existent(app_context, mocker):
    org = organization_fixture()
    author = user_fixture(id=42, name="Jack Sparrow")

    report, submission = report_and_submission_fixture(owner_id=author.id)
    mocker.patch("flask_login.utils._get_user", return_value=author)

    submission_note_fixture(submission_id=submission.id, author_id=author.id, canonical_id=UUID(int=0))
    submission_note_fixture(submission_id=submission.id, author_id=author.id)
    db.session.commit()

    response_body, response_code = create_or_replace_note(
        str(report.id), str(UUID(int=1)), {"notes": "Fresh new note!"}
    )
    db.session.commit()

    assert response_code == 201
    assert response_body == {
        "id": str(UUID(int=1)),
    }

    notes = SubmissionNote.query.all()

    assert len(notes) == 3
    assert sum(1 if note.canonical_id is None else 0 for note in notes) == 1
    assert {note.canonical_id for note in notes} == {None, UUID(int=1), UUID(int=0)}
    assert any(note.text_content == "Fresh new note!" and note.canonical_id == UUID(int=1) for note in notes)


def test_create_or_replace_note_replaces_old_note(app_context, mocker):
    org = organization_fixture()
    author = user_fixture(id=42, name="Jack Sparrow")

    report, submission = report_and_submission_fixture(owner_id=author.id)
    mocker.patch("flask_login.utils._get_user", return_value=author)

    response_body, response_code = create_or_replace_note(
        str(report.id), str(UUID(int=1)), {"notes": "Fresh new note!"}
    )
    db.session.commit()

    assert response_code == 201
    assert response_body == {
        "id": str(UUID(int=1)),
    }

    notes = SubmissionNote.query.all()
    assert len(notes) == 1
    assert notes[0].canonical_id == UUID(int=1)
    assert notes[0].text_content == "Fresh new note!"

    response_body, response_code = create_or_replace_note(
        str(report.id), str(UUID(int=1)), {"notes": "Even newer note!"}
    )
    db.session.commit()

    assert response_code == 200
    assert response_body == {
        "id": str(UUID(int=1)),
    }

    notes = SubmissionNote.query.all()
    assert len(notes) == 1
    assert notes[0].canonical_id == UUID(int=1)
    assert notes[0].text_content == "Even newer note!"


def test_add_submission_note_external(app_context, mocker):
    org = organization_fixture()
    author = user_fixture(id=42, name="Jack Sparrow")

    report, submission = report_and_submission_fixture(owner_id=author.id)
    mocker.patch("flask_login.utils._get_user", return_value=author)
    first_note_id = uuid4()
    second_note_id = uuid4()
    third_note_id = uuid4()
    fourth_note_id = uuid4()

    submission_note_fixture(
        id=first_note_id,
        submission_id=submission.id,
        author_id=author.id,
        note=create_text_note("First note of many"),
    )
    submission_note_fixture(
        id=second_note_id,
        submission_id=submission.id,
        author_id=author.id,
        note=create_text_note("Second note is going to be deleted"),
    )
    submission_note_fixture(
        id=third_note_id,
        submission_id=submission.id,
        author_id=author.id,
        note=create_text_note("Third contains new line and \nwill be updated"),
    )
    submission_note_fixture(
        id=fourth_note_id,
        submission_id=submission.id,
        author_id=author.id,
        note=create_text_note("Fourth note slight update"),
    )
    db.session.commit()

    notes_response, status = add_submission_note_external(
        str(report.id),
        {
            "notes": (
                "First note of many\nThird note contains new line and\n"
                "will be updated\nFourth note slightly updated\nFifth note added"
            ),
            "smart_matching": True,
        },
    )

    response = notes_response["notes"]

    assert len(response) == 4

    all_notes = SubmissionNote.query.order_by(SubmissionNote.created_at.asc()).all()
    last_note_id = all_notes[-1].id

    assert [note["id"] for note in response] == [
        str(first_note_id),
        str(third_note_id),
        str(fourth_note_id),
        str(last_note_id),
    ]

    assert [note["text"] for note in response] == [
        "First note of many",
        "Third note contains new line and\nwill be updated",
        "Fourth note slightly updated",
        "Fifth note added",
    ]
