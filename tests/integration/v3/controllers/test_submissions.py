from collections import namedtuple
from datetime import datetime, timedelta
from http import HTTPStatus
from typing import Any
from unittest.mock import ANY, MagicMock, call, patch
from uuid import UUID, uuid4
import json
import os
import uuid

from common.clients.paragon_ims import IMSSearchClient
from dateutil.relativedelta import relativedelta
from entity_resolution_service_client_v3 import Entity, EntityPremises, Premises
from flask.ctx import RequestContext
from paragon_ims_api_client.models.clearance_info import ClearanceInfo
from paragon_ims_api_client.models.submission_information import SubmissionInformation
from sqlalchemy.exc import InternalError
from static_common.enums.caller_context import CallerContext
from static_common.enums.entity import EntityInformation, EntityPremisesType
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.origin import Origin
from static_common.enums.sensible import SensibleStatus
from static_common.enums.submission import SubmissionMode, SubmissionStage
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
from static_common.enums.submission_processing_state import SubmissionProcessingState
from static_common.enums.underwriters import SubmissionUserSource
from static_common.models.business_resolution_data import BusinessResolutionData
from static_common.models.paragon import ImsCoverage
from static_common.models.submission_level_data import SourceDetails
from static_common.schemas.business_resolution_data import BusinessResolutionDataSchema
from werkzeug.exceptions import BadRequest, Conflict, NotFound
import flask
import pytest
import werkzeug.exceptions

from copilot.clients.ers_v3 import ERSClientV3
from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.constants import PARAGON_WC_EMAIL
from copilot.models import (
    Loss,
    LossPolicy,
    ReportPermission,
    Settings,
    Submission,
    SubmissionBusiness,
    SubmissionHistory,
    db,
)
from copilot.models.emails import ReportEmailCorrespondence
from copilot.models.files import ProcessedFile
from copilot.models.reports import (
    ClientSubmissionStageConfig,
    Coverage,
    SubmissionClientId,
    SubmissionCoverage,
)
from copilot.models.sent_email import SentEmail
from copilot.models.submission_level_extracted_data import SubmissionLevelExtractedData
from copilot.models.submission_sync import SubmissionSync
from copilot.models.taxonomy import MappingTypeEnum
from copilot.models.types import (
    CoverageType,
    LineOfBusinessType,
    PermissionType,
    SubmissionActionType,
    SubmissionEvent,
)
from copilot.schemas.report import (
    SubmissionBulkDeclinePatch,
    SubmissionBulkDeclinePatchSchema,
)
from copilot.v3.controllers.sic_codes import CNA_ORGANIZATION_ID
from copilot.v3.controllers.submission_notes import get_submission_notes
from copilot.v3.controllers.submission_verification import verify
from copilot.v3.controllers.submissions import (
    add_client_submission_id,
    add_submission_user,
    align_submissions_client_stages,
    assign_submission_to_clearing_user,
    bulk_add_client_submission_ids,
    bulk_decline_submissions,
    create_or_replace_submission_level_extracted_data,
    delete_client_submission_id,
    delete_loss,
    delete_submission_user,
    execute_submission_action,
    expire_submissions,
    get_active_organizational_submissions,
    get_business_at_location,
    get_client_submission_id,
    get_identifier_suggestions,
    get_loss_lob_inference_requirements,
    get_loss_policies,
    get_losses,
    get_losses_summary,
    get_submission_by_id,
    get_submission_level_extracted_data_by_report_id,
    get_submission_lite,
    get_submission_or_404,
    get_submission_shadow_info,
    get_submission_shareholders,
    get_submissions_by_client_ids,
    invoke_loss_run_processing,
    put_loss_policy,
    send_submission_email,
    set_submission_identifiers,
    store_submission_level_extracted_data,
    update_loss,
    update_loss_lob_bulk,
    update_submission,
)
from tests.integration.factories import (
    broker_client_code_fixture,
    broker_fixture,
    brokerage_client_code_fixture,
    brokerage_fixture,
    client_submission_stage_config_fixture,
    coverage_fixture,
    email_fixture,
    file_fixture,
    loss_fixture,
    loss_policy_fixture,
    organization_fixture,
    processed_file_fixture,
    report_and_submission_fixture,
    report_email_correspondence_fixture,
    report_fixture,
    report_with_submissions_fixture,
    settings_fixture,
    shadow_submission_fixture,
    shareholder_fixture,
    submission_business_fixture,
    submission_client_id_fixture,
    submission_coverage_fixture,
    submission_fixture,
    submission_identifiers_suggestion_fixture,
    submission_level_extracted_data_fixture,
    submission_sync_fixture,
    submission_sync_identifiers_fixture,
    submission_user_fixture,
    taxonomy_description_fixture,
    taxonomy_mapping_fixture,
    user_fixture,
)
from tests.integration.utils import (
    AnonObj,
    always_has_permission,
    setup_user_and_request,
    with_feature_flag,
)


@pytest.fixture
def current_user_check_bypass(mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            organization_id=1,
            email="<EMAIL>",
            is_being_impersonated=False,
            id=1,
            has_submission_permission=lambda type, id: True,
            name="Test User",
        ),
    )


@pytest.fixture
def loss_policy_submission_requirements():
    org = organization_fixture(id=1)
    user = user_fixture(organization_id=org.id)
    report = report_fixture(organization_id=org.id, owner_id=user.id)
    submission = submission_fixture(
        report=report,
        user=user,
    )

    db.session.commit()

    return submission


def test_delete_submission_user(app_context, mocker):
    organization_fixture()
    user = user_fixture(email="<EMAIL>")
    report = report_fixture(owner_id=user.id, tier=None)
    submission = submission_fixture(owner_id=user.id, report=report, stage="ON_MY_PLATE")
    db.session.flush()
    submission_user_fixture(user_id=user.id, submission_id=submission.id, source=SubmissionUserSource.AUTO)
    db.session.commit()

    cs_user = AnonObj(
        id=1,
        email="<EMAIL>",
        is_internal_machine_user=False,
        has_submission_permission=always_has_permission,
        is_cs_manager_or_internal_machine_user=False,
        is_support=True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=cs_user)
    assert len(submission.assigned_underwriters) == 1
    with pytest.raises(BadRequest):
        delete_submission_user(str(submission.id), user.id)
    assert len(submission.assigned_underwriters) == 1

    internal_user = AnonObj(
        id=2,
        email="<EMAIL>",
        is_internal_machine_user=True,
        has_submission_permission=always_has_permission,
        is_cs_manager_or_internal_machine_user=True,
        is_support=True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=internal_user)
    delete_submission_user(str(submission.id), user.id)
    assert len(submission.assigned_underwriters) == 0


def test_get_submission_lite_frozen_as_of(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            organization_id=1,
            email="<EMAIL>",
            is_being_impersonated=False,
            has_submission_permission=always_has_permission,
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture()
    user = user_fixture()
    report = report_fixture(owner_id=user.id, tier=None)
    submission = submission_fixture(owner_id=user.id, report=report, stage="QUOTED_BOUND")
    db.session.commit()

    submission_lite = get_submission_lite(id=submission.id, expand=["frozen_as_of"])
    assert submission_lite is not None
    assert submission_lite.get("frozen_as_of")[:19] == submission_lite.get("created_at")[:19]


def test_get_submission_lite_frozen_as_of_is_none(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            organization_id=1,
            email="<EMAIL>",
            is_being_impersonated=False,
            has_submission_permission=always_has_permission,
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture()
    user = user_fixture()
    report = report_fixture(owner_id=user.id, tier=None)
    submission = submission_fixture(owner_id=user.id, report=report, stage="EXPIRED")
    db.session.commit()

    submission_lite = get_submission_lite(id=submission.id, expand=["frozen_as_of"])
    assert submission_lite is not None
    assert submission_lite["frozen_as_of"] is None


def test_get_submission_lite_get_file_s3_kye(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            organization_id=1,
            email="<EMAIL>",
            is_being_impersonated=False,
            has_submission_permission=always_has_permission,
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture()
    user = user_fixture()
    report = report_fixture(owner_id=user.id, tier=None)
    submission = submission_fixture(owner_id=user.id, report=report, stage="QUOTED_BOUND")
    file = file_fixture(submission_id=submission.id, s3_key="test_key")
    db.session.commit()

    submission_lite = get_submission_lite(id=submission.id, expand=["files"])
    assert "s3_key" not in submission_lite["files"][0]
    submission_lite = get_submission_lite(id=submission.id, expand=["files", "files.s3_key"])
    assert submission_lite["files"][0]["s3_key"] == "test_key"


def test_update_coverage(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True, organization_id=1, email="<EMAIL>", is_being_impersonated=False
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture()
    user = user_fixture()
    coverage_1 = coverage_fixture(name="c1")
    coverage_2 = coverage_fixture(name="c2")
    report = report_fixture(owner_id=user.id, tier=None)
    submission = submission_fixture(owner_id=user.id, report=report)
    submission.coverages.append(SubmissionCoverage(coverage=coverage_1, quoted_premium=100))
    db.session.commit()

    update_submission(
        str(submission.id),
        {
            "coverages": [
                {"id": str(submission.coverages[0].id), "coverage_id": str(coverage_1.id), "bound_premium": 200},
                {"coverage_id": str(coverage_2.id), "bound_premium": 300},
            ]
        },
    )

    assert len(submission.coverages) == 2

    with_c1 = [x for x in submission.coverages if x.coverage == coverage_1][0]
    with_c2 = [x for x in submission.coverages if x.coverage == coverage_2][0]

    assert with_c1.quoted_premium == 100
    assert with_c1.bound_premium == 200

    assert with_c2.bound_premium == 300


def test_expire_submissions_old_submissions(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, email="<EMAIL>", is_being_impersonated=False, id=1),
    )
    organization_fixture()
    user = user_fixture()
    date_far_in_the_past = datetime.utcnow() - relativedelta(months=6)
    date_in_the_past = datetime.utcnow() - relativedelta(days=10)
    sub_past_effective_but_fresh = report_with_submissions_fixture(
        proposed_effective_date=date_far_in_the_past, owner_id=user.id, organization_id=1
    ).submission
    sub_past_effective_but_fresh_but_shell = report_with_submissions_fixture(
        proposed_effective_date=date_far_in_the_past, owner_id=user.id, organization_id=1, is_verified_shell=True
    ).submission
    sub_past_effective_and_old = report_with_submissions_fixture(
        proposed_effective_date=date_far_in_the_past, owner_id=user.id, organization_id=1, created_at=date_in_the_past
    ).submission
    sub_old = report_with_submissions_fixture(
        created_at=date_far_in_the_past, owner_id=user.id, organization_id=1
    ).submission
    sub_new = report_with_submissions_fixture(
        created_at=datetime.utcnow(), owner_id=user.id, organization_id=1
    ).submission

    base_path = "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler"
    with patch(base_path + ".send_submission_stage_changed_event_bulk") as send_event_mock:
        expire_submissions()
        expired_submissions = Submission.query.filter(Submission.stage == SubmissionStage.EXPIRED).all()
        expired_submissions_ids = [s.id for s in expired_submissions]

        assert sub_past_effective_but_fresh.id not in expired_submissions_ids
        assert sub_past_effective_and_old.id in expired_submissions_ids
        assert sub_past_effective_but_fresh_but_shell.id in expired_submissions_ids
        assert sub_old.id in expired_submissions_ids
        assert sub_new.id not in expired_submissions_ids
        assert len(expired_submissions_ids) == 3
        assert send_event_mock.called


def test_expire_submissions_do_not_expire_org_group(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, email="<EMAIL>", is_being_impersonated=False, id=1),
    )
    organization_fixture(id=58)
    user_fixture(organization_id=58)
    past_date = datetime.utcnow() - relativedelta(months=6)
    report_1, submission_1 = report_and_submission_fixture(organization_id=58, created_at=past_date)
    report_2, submission_2 = report_and_submission_fixture(organization_id=58, created_at=past_date)
    settings_fixture(organization_id=58, org_groups=["THC", "Hospitality", "Main St", "Cannabis Select"])
    db.session.commit()

    report_1.org_group = "Cannabis Select"
    report_2.org_group = "THC"
    db.session.commit()

    expire_submissions()

    expired_submissions = Submission.query.filter(Submission.stage == SubmissionStage.EXPIRED).all()
    assert len(expired_submissions) == 1
    assert str(expired_submissions[0].id) == str(submission_2.id)


def test_expire_submissions_recent_submissions(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, email="<EMAIL>", is_being_impersonated=False),
    )
    organization_fixture()
    user = user_fixture()
    current_date = datetime.utcnow()
    date_in_the_past = datetime.utcnow() - relativedelta(months=6)
    _, sub_with_effective = report_and_submission_fixture(proposed_effective_date=current_date, owner_id=user.id)
    _, old_sub_with_with_effective = report_and_submission_fixture(
        created_at=date_in_the_past, proposed_effective_date=current_date, owner_id=user.id
    )
    _, sub_in_terminal_stage = report_and_submission_fixture(
        created_at=datetime.utcnow(), owner_id=user.id, stage=SubmissionStage.BLOCKED
    )

    expire_submissions()
    expired_submissions = Submission.query.filter(Submission.stage == SubmissionStage.EXPIRED).all()

    assert not expired_submissions


def test_expire_quoted_submissions(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, email="<EMAIL>", is_being_impersonated=False, id=1),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})
    org_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=org_id)
    user = user_fixture(organization_id=org_id)

    past_date = datetime.utcnow() - relativedelta(months=7)
    _, old_sub = report_and_submission_fixture(
        created_at=past_date, owner_id=user.id, stage=SubmissionStage.QUOTED, organization_id=org_id
    )
    _, fresh_sub = report_and_submission_fixture(owner_id=user.id, stage=SubmissionStage.QUOTED, organization_id=org_id)
    _, not_quoted = report_and_submission_fixture(
        created_at=past_date, owner_id=user.id, stage=SubmissionStage.EXPIRED, organization_id=org_id
    )

    expire_submissions()
    expired_submissions = Submission.query.filter(Submission.stage == SubmissionStage.QUOTED_LOST).all()

    assert len(expired_submissions) == 1
    assert str(expired_submissions[0].id) == str(old_sub.id)


def test_get_client_submission_id(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            has_submission_permission=lambda type, id: True, email="<EMAIL>", is_being_impersonated=False
        ),
    )
    organization_id = 5
    client_submission_id = "CDC12345"
    organization_fixture(id=organization_id)
    user = user_fixture(organization_id=organization_id)

    _, submission = report_and_submission_fixture(owner_id=user.id, organization_id=organization_id)
    submission_client_id_fixture(submission_id=submission.id, client_submission_id=client_submission_id)
    result, status_code = get_client_submission_id(client_submission_id, organization_id)

    assert status_code == 200
    assert result["submission_id"] == str(submission.id)


def test_can_update_quoted_submission_dates(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True, organization_id=1, email="<EMAIL>", is_being_impersonated=False
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture()
    user_fixture()
    report = report_fixture(tier=None)
    submission = submission_fixture(
        stage=SubmissionStage.QUOTED,
        proposed_effective_date=datetime(2020, 1, 1),
        policy_expiration_date=datetime(2020, 1, 1),
        report=report,
    )
    db.session.commit()

    update_submission(
        str(submission.id),
        {
            "proposed_effective_date": None,
            "policy_expiration_date": None,
        },
    )

    submission = Submission.query.get(submission.id)

    assert submission.proposed_effective_date is None
    assert submission.policy_expiration_date is None


def test_can_update_notes(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True, organization_id=1, email="<EMAIL>", is_being_impersonated=False
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture()
    user_fixture()
    report = report_fixture(tier=None)
    submission = submission_fixture(report=report)

    db.session.commit()
    update_submission(str(submission.id), {"notes": "test-123"})

    submission = Submission.query.get(submission.id)

    assert submission.notes == "test-123"


def test_can_take_business_at_location(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            has_submission_permission=lambda type, id: True,
            email="<EMAIL>",
            is_being_impersonated=False,
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture()
    user_fixture()
    _, submission = report_and_submission_fixture()
    capi_business_id = uuid4()
    business_id = uuid4()
    tenant_entity_id = uuid4()
    submission_business = SubmissionBusiness(id=capi_business_id, business_id=business_id, submission_id=submission.id)
    db.session.add(submission_business)

    mocker.patch(
        "flask.current_app.ers_client_v3.get_entity",
        return_value=Entity(
            premises=[
                EntityPremises(
                    type=EntityPremisesType.PHYSICAL_ADDRESS,
                    premises=Premises(entity_tenants_ids=[str(tenant_entity_id)]),
                )
            ]
        ),
    )
    mocker.patch(
        "flask.current_app.ers_client_v3.get_entities",
        return_value={str(tenant_entity_id): Entity(id=str(tenant_entity_id))},
    )

    db.session.commit()
    result = get_business_at_location(str(submission.id), str(capi_business_id))

    assert len(result["entities"]) == 1
    assert result["entities"][0]["id"] == str(tenant_entity_id)


def test_bulk_decline_submissions(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            organization_id=1,
            has_submission_permission=lambda type, id: True,
            email="<EMAIL>",
            id=1,
            is_being_impersonated=False,
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture()
    user_fixture()
    report = report_fixture(tier=None)
    submission = submission_fixture(report=report)

    submission = get_submission_or_404(submission.id)
    assert submission.stage != SubmissionStage.DECLINED
    assert submission.reason_for_declining is None

    request = SubmissionBulkDeclinePatch(ids=[submission.id], reason="Test reason")
    bulk_decline_submissions(SubmissionBulkDeclinePatchSchema().dump(request))

    assert submission.stage == SubmissionStage.DECLINED
    assert submission.reason_for_declining == "Test reason"


@pytest.mark.parametrize(
    "is_verification_required,is_verified,should_share,expected_to_be_shared",
    [
        (None, None, None, True),
        (None, None, True, True),
        (None, None, False, False),
        (None, True, None, True),
        (None, True, True, True),
        (None, True, False, False),
        (None, False, None, True),
        (None, False, True, True),
        (None, False, False, False),
        (True, None, None, False),
        (True, None, True, True),
        (True, None, False, False),
        (True, True, None, True),
        (True, True, True, True),
        (True, True, False, False),
        (True, False, None, False),
        (True, False, True, True),
        (True, False, False, False),
        (False, None, None, True),
        (False, None, True, True),
        (False, None, False, False),
        (False, True, None, True),
        (False, True, True, True),
        (False, True, False, False),
        (False, False, None, True),
        (False, False, True, True),
        (False, False, False, False),
    ],
)
def test_sharing_when_adding_user(
    app_context, mocker, is_verification_required, is_verified, should_share, expected_to_be_shared
):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            has_submission_permission=lambda type, id: True,
            has_report_permission=lambda type, id: True,
            id=1,
            email="<EMAIL>",
            is_being_impersonated=False,
        ),
    )
    mocker.patch("copilot.v3.controllers.submissions.handle_submission_data_update", return_value=None)
    organization_fixture()
    user = user_fixture()
    user1 = user_fixture(id=2, email="<EMAIL>")
    report = report_fixture()
    submission = submission_fixture(
        is_verified=is_verified, is_verification_required=is_verification_required, report=report
    )
    db.session.commit()
    request = {"submission_id": str(submission.id), "user_id": user1.id}
    if should_share is not None:
        request["should_share"] = should_share
    add_submission_user(submission_id=str(submission.id), body=request)
    shared = bool(
        ReportPermission.query.filter(ReportPermission.grantee_user_id == user1.id)
        .filter(ReportPermission.report_id == report.id)
        .filter(ReportPermission.permission_type == PermissionType.OWNER)
        .first()
    )
    assert shared == expected_to_be_shared


def test_submission_with_files_can_be_deleted(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False),
    )
    organization_fixture()
    user_fixture()
    _, submission = report_and_submission_fixture(mode=SubmissionMode.GC_PROJECT)
    file_fixture(submission_id=submission.id)
    db.session.commit()
    db.session.delete(submission)
    db.session.commit()


LossesTestRequirements = namedtuple("LossRunTestRequirements", "submission_id organization_id file_id")


@pytest.fixture
def losses_test_requirements(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            has_submission_permission=lambda type, id: True,
            organization_id=1,
            applicable_settings=Settings(loss_runs_enabled=True),
            email="<EMAIL>",
            is_being_impersonated=False,
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})
    organization = organization_fixture()
    user_fixture()
    _, submission = report_and_submission_fixture()
    file = file_fixture(submission_id=submission.id, sensible_status=SensibleStatus.COMPLETE)
    db.session.commit()

    return LossesTestRequirements(submission.id, organization.id, file.id)


@pytest.fixture
def default_loss_policy(losses_test_requirements):
    submission_id, organization_id, _ = losses_test_requirements
    loss_policy = loss_policy_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        number="F1234",
        effective_date=datetime(year=2023, month=4, day=14),
        expiration_date=datetime(year=2023, month=5, day=14),
        raw_line_of_business="testlob1",
        line_of_business=LineOfBusinessType.OTHER,
        original_line_of_business="testlob1",
        evidence=[{"x": -1, "y": 1}, {"x": 2, "y": 5}],
    )
    db.session.commit()

    return loss_policy


@pytest.fixture
def default_loss(losses_test_requirements, default_loss_policy):
    submission_id, organization_id, file_id = losses_test_requirements
    loss = loss_fixture(
        submission_id=submission_id,
        file_id=file_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=5.0,
        carrier="Progressive",
        line_of_business=LineOfBusinessType.OTHER,
        loss_address="PA",
        claim_number="12345",
        loss_policy=default_loss_policy,
        evidence=[{"x": 0, "y": 1}, {"x": 2, "y": 3}],
    )
    db.session.commit()

    return loss


@pytest.fixture
def duplicate_loss(losses_test_requirements, default_loss_policy):
    submission_id, organization_id, file_id = losses_test_requirements
    loss = loss_fixture(
        submission_id=submission_id,
        file_id=file_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=5.0,
        carrier="Progressive",
        line_of_business=LineOfBusinessType.OTHER,
        loss_address="PA",
        claim_number="12345",
        loss_policy=default_loss_policy,
        is_duplicate=True,
    )
    db.session.commit()

    return loss


@pytest.fixture
def loss_without_is_duplicate(losses_test_requirements, default_loss_policy):
    submission_id, organization_id, file_id = losses_test_requirements
    loss = loss_fixture(
        submission_id=submission_id,
        file_id=file_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=5.0,
        carrier="Progressive",
        line_of_business=LineOfBusinessType.OTHER,
        loss_address="PA",
        claim_number="12345",
        loss_policy=default_loss_policy,
        is_duplicate=None,
    )
    db.session.commit()

    return loss


@pytest.fixture
def loss_from_partially_complete_file(losses_test_requirements: LossesTestRequirements) -> None:
    submission_id, organization_id, _ = losses_test_requirements
    partially_complete_file = file_fixture(
        submission_id=submission_id, sensible_status=SensibleStatus.PARTIALLY_COMPLETE
    )
    loss_fixture(
        file_id=partially_complete_file.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=9.0,
        carrier="Progressive 2",
        original_line_of_business=None,
        line_of_business=LineOfBusinessType.BUSINESS_AUTO,
        loss_address="PA",
        claim_number="12347",
    ),
    db.session.commit()


@pytest.fixture
def loss_from_invalid_file(losses_test_requirements: LossesTestRequirements) -> None:
    submission_id, organization_id, _ = losses_test_requirements
    partially_complete_file = file_fixture(submission_id=submission_id, sensible_status=SensibleStatus.INVALID)
    loss_fixture(
        file_id=partially_complete_file.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=9.0,
        carrier="Progressive 2",
        original_line_of_business=None,
        line_of_business=LineOfBusinessType.BUSINESS_AUTO,
        loss_address="PA",
        claim_number="12347",
    ),
    db.session.commit()


@pytest.fixture
def manual_loss_from_partially_complete_file(losses_test_requirements: LossesTestRequirements) -> None:
    submission_id, organization_id, _ = losses_test_requirements
    partially_complete_file = file_fixture(submission_id=submission_id, sensible_status=SensibleStatus.INVALID)
    loss_fixture(
        file_id=partially_complete_file.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=9.0,
        carrier="Progressive 2",
        original_line_of_business=None,
        line_of_business=LineOfBusinessType.BUSINESS_AUTO,
        loss_address="PA",
        claim_number="12347",
        is_manual=True,
    ),
    db.session.commit()


@pytest.fixture
def manual_loss_from_invalid_file(losses_test_requirements: LossesTestRequirements) -> None:
    submission_id, organization_id, _ = losses_test_requirements
    partially_complete_file = file_fixture(submission_id=submission_id, sensible_status=SensibleStatus.INVALID)
    loss_fixture(
        file_id=partially_complete_file.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=9.0,
        carrier="Progressive 2",
        original_line_of_business=None,
        line_of_business=LineOfBusinessType.BUSINESS_AUTO,
        loss_address="PA",
        claim_number="12347",
        is_manual=True,
    ),
    db.session.commit()


@pytest.fixture
def manual_loss_from_partially_complete_file(losses_test_requirements: LossesTestRequirements) -> None:
    submission_id, organization_id, _ = losses_test_requirements
    partially_complete_file = file_fixture(submission_id=submission_id, sensible_status=SensibleStatus.INVALID)
    loss_fixture(
        file_id=partially_complete_file.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=9.0,
        carrier="Progressive 2",
        original_line_of_business=None,
        line_of_business=LineOfBusinessType.BUSINESS_AUTO,
        loss_address="PA",
        claim_number="12347",
        is_manual=True,
    ),
    db.session.commit()


@pytest.fixture
def manual_loss_from_invalid_file(losses_test_requirements: LossesTestRequirements) -> None:
    submission_id, organization_id, _ = losses_test_requirements
    partially_complete_file = file_fixture(submission_id=submission_id, sensible_status=SensibleStatus.INVALID)
    loss_fixture(
        file_id=partially_complete_file.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=9.0,
        carrier="Progressive 2",
        original_line_of_business=None,
        line_of_business=LineOfBusinessType.BUSINESS_AUTO,
        loss_address="PA",
        claim_number="12347",
        is_manual=True,
    ),
    db.session.commit()


@pytest.fixture
def losses_for_original_line_of_balance_trimming_tests(losses_test_requirements):
    submission_id, organization_id, file_id = losses_test_requirements
    coverage = coverage_fixture(organization_id=organization_id)

    loss_fixture(
        file_id=file_id,
        coverage_id=coverage.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        original_line_of_business="    business Auto   ",
        claim_number="12345",
        carrier="Carrier1",
        sum_of_total_net_incurred=1.0,
    )
    loss_fixture(
        file_id=file_id,
        coverage_id=coverage.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        original_line_of_business="Business Auto   ",
        claim_number="12345",
        carrier="Carrier1",
        sum_of_total_net_incurred=2.0,
    )
    loss_fixture(
        file_id=file_id,
        coverage_id=coverage.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        original_line_of_business="    fiduciary liability   ",
        line_of_business=None,
        claim_number="12346",
        carrier="Carrier2",
        sum_of_total_net_incurred=3.0,
    )
    loss_fixture(
        file_id=file_id,
        coverage_id=coverage.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        original_line_of_business="    Business auto",
        claim_number="12346",
        carrier="Carrier2",
        sum_of_total_net_incurred=5.0,
    )


def test_get_losses(losses_test_requirements, default_loss):
    submission_id, _, _ = losses_test_requirements
    losses_envelope_dict = get_losses(submission_id)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 1
    __verify_loss_run(default_loss, losses[0])
    assert losses_envelope_dict["page"] == 1
    assert losses_envelope_dict["total_pages"] == 1
    assert losses_envelope_dict["total_items"] == 1
    assert losses_envelope_dict["has_next"] is False


def test_get_losses_filters_out_duplicates(losses_test_requirements, duplicate_loss):
    submission_id, _, _ = losses_test_requirements
    losses_envelope_dict = get_losses(submission_id)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 0
    assert losses_envelope_dict["page"] == 1
    assert losses_envelope_dict["total_pages"] == 1
    assert losses_envelope_dict["total_items"] == 0
    assert losses_envelope_dict["has_next"] is False


def test_get_losses_does_not_filter_out_losses_without_is_duplicate(
    losses_test_requirements, loss_without_is_duplicate
):
    submission_id, _, _ = losses_test_requirements
    losses_envelope_dict = get_losses(submission_id)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 1
    assert losses_envelope_dict["page"] == 1
    assert losses_envelope_dict["total_pages"] == 1
    assert losses_envelope_dict["total_items"] == 1
    assert losses_envelope_dict["has_next"] is False


def test_get_losses_filters_losses_from_partially_complete_files(
    losses_test_requirements, loss_from_partially_complete_file
):
    submission_id, _, _ = losses_test_requirements
    losses_envelope_dict = get_losses(submission_id, filter_from_partially_extracted_files=True)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 0
    assert losses_envelope_dict["page"] == 1
    assert losses_envelope_dict["total_pages"] == 1
    assert losses_envelope_dict["total_items"] == 0
    assert losses_envelope_dict["has_next"] is False


def test_get_losses_filters_losses_from_invalid_files(losses_test_requirements, loss_from_invalid_file):
    submission_id, _, _ = losses_test_requirements
    losses_envelope_dict = get_losses(submission_id)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 0
    assert losses_envelope_dict["page"] == 1
    assert losses_envelope_dict["total_pages"] == 1
    assert losses_envelope_dict["total_items"] == 0
    assert losses_envelope_dict["has_next"] is False

    losses_envelope_dict = get_losses(submission_id, filter_from_failed_files=False)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 1


def test_get_losses_filters_manual_losses_from_partially_commplete_files(
    losses_test_requirements, manual_loss_from_partially_complete_file
):
    submission_id, _, _ = losses_test_requirements
    losses_envelope_dict = get_losses(submission_id)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 1
    assert losses_envelope_dict["page"] == 1
    assert losses_envelope_dict["total_pages"] == 1
    assert losses_envelope_dict["total_items"] == 1
    assert losses_envelope_dict["has_next"] is False

    losses_envelope_dict = get_losses(submission_id, filter_from_partially_extracted_files=False)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 1


def test_get_losses_filters_manual_losses_from_invalid_files(losses_test_requirements, manual_loss_from_invalid_file):
    submission_id, _, _ = losses_test_requirements
    losses_envelope_dict = get_losses(submission_id)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 1
    assert losses_envelope_dict["page"] == 1
    assert losses_envelope_dict["total_pages"] == 1
    assert losses_envelope_dict["total_items"] == 1
    assert losses_envelope_dict["has_next"] is False

    losses_envelope_dict = get_losses(submission_id, filter_from_failed_files=False)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 1


def test_get_losses_filters_manual_losses_from_partially_commplete_files(
    losses_test_requirements, manual_loss_from_partially_complete_file
):
    submission_id, _, _ = losses_test_requirements
    losses_envelope_dict = get_losses(submission_id)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 1
    assert losses_envelope_dict["page"] == 1
    assert losses_envelope_dict["total_pages"] == 1
    assert losses_envelope_dict["total_items"] == 1
    assert losses_envelope_dict["has_next"] is False

    losses_envelope_dict = get_losses(submission_id, filter_from_partially_extracted_files=False)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 1


def test_get_losses_filters_manual_losses_from_invalid_files(losses_test_requirements, manual_loss_from_invalid_file):
    submission_id, _, _ = losses_test_requirements
    losses_envelope_dict = get_losses(submission_id)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 1
    assert losses_envelope_dict["page"] == 1
    assert losses_envelope_dict["total_pages"] == 1
    assert losses_envelope_dict["total_items"] == 1
    assert losses_envelope_dict["has_next"] is False

    losses_envelope_dict = get_losses(submission_id, filter_from_failed_files=False)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 1


def test_get_losses_aggregates_losses_by_claim_number_and_carrier(losses_test_requirements):
    submission_id, organization_id, file_id = losses_test_requirements
    expected_carrier = "Progressive"
    expected_claim_number = "12345"
    coverage = coverage_fixture(organization_id=organization_id)
    loss1 = loss_fixture(
        file_id=file_id,
        coverage_id=coverage.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=5.0,
        carrier=expected_carrier,
        line_of_business=LineOfBusinessType.PROPERTY,
        loss_address="NY",
        claim_number=expected_claim_number,
        claim_status="SOME_BAD_VALUE",
        sum_of_net_paid_alae=7.0,
        sum_of_net_outstanding_alae=9.0,
        loss_type="Test",
    )
    loss2 = loss_fixture(
        file_id=file_id,
        coverage_id=coverage.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=10, day=12),
        sum_of_total_net_incurred=5.0,
        carrier=expected_carrier + " 5",
        line_of_business=LineOfBusinessType.OTHER,
        loss_address="PA",
        claim_number=expected_claim_number,
        claim_status="OPEN",
        sum_of_net_paid_alae=4.0,
        sum_of_net_outstanding_alae=8.0,
        loss_type="Test",
    )
    db.session.commit()

    losses_envelope_dict = get_losses(submission_id)
    losses = losses_envelope_dict["losses"]

    assert losses_envelope_dict["total_items"] == 1
    assert len(losses) == 1

    result_loss = losses[0]
    assert result_loss["submission_id"] == str(submission_id)
    assert result_loss["organization_id"] == organization_id
    assert result_loss["loss_date"] == str(loss2.loss_date)
    assert result_loss["sum_of_total_net_incurred"] == 10.0
    assert result_loss["sum_of_net_paid_alae"] == 11.0
    assert result_loss["sum_of_net_outstanding_alae"] == 17.0
    assert result_loss["carrier"] == expected_carrier
    assert loss1.line_of_business.value in result_loss["line_of_business"].split(" | ")
    assert loss2.line_of_business.value in result_loss["line_of_business"].split(" | ")
    assert result_loss["loss_address"] == f"{loss1.loss_address} | {loss2.loss_address}"
    assert result_loss["claim_number"] == expected_claim_number
    assert result_loss["coverage_rollup"] == coverage.name
    assert result_loss["claim_status"] == loss2.claim_status
    assert result_loss["loss_type"] == "Test"


def test_get_losses_aggregated_has_source_files(losses_test_requirements):
    submission_id, organization_id, file_id = losses_test_requirements
    expected_carrier = "Progressive"
    expected_claim_number = "12345"
    other_file = file_fixture(
        name="Other Name",
        organization_id=organization_id,
        submission_id=submission_id,
        sensible_status=SensibleStatus.COMPLETE,
    )
    internal_file = file_fixture(
        name="Internal File",
        organization_id=organization_id,
        submission_id=submission_id,
        sensible_status=SensibleStatus.COMPLETE,
        is_internal=True,
    )
    coverage = coverage_fixture(organization_id=organization_id)
    loss1 = loss_fixture(
        file_id=file_id,
        coverage_id=coverage.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=5.0,
        carrier=expected_carrier,
        line_of_business=LineOfBusinessType.PROPERTY,
        loss_address="NY",
        claim_number=expected_claim_number,
        claim_status="SOME_BAD_VALUE",
        sum_of_net_paid_alae=7.0,
        sum_of_net_outstanding_alae=9.0,
    )
    loss2 = loss_fixture(
        file_id=other_file.id,
        coverage_id=coverage.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=10, day=12),
        sum_of_total_net_incurred=5.0,
        carrier=expected_carrier + " 5",
        line_of_business=LineOfBusinessType.OTHER,
        loss_address="PA",
        claim_number=expected_claim_number,
        claim_status="OPEN",
        sum_of_net_paid_alae=4.0,
        sum_of_net_outstanding_alae=8.0,
    )
    loss3 = loss_fixture(
        file_id=internal_file.id,
        coverage_id=coverage.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=10, day=12),
        sum_of_total_net_incurred=5.0,
        carrier=expected_carrier + " 5",
        line_of_business=LineOfBusinessType.PROPERTY,
        loss_address="LA",
        claim_number=expected_claim_number,
        claim_status="OPEN",
        sum_of_net_paid_alae=4.0,
        sum_of_net_outstanding_alae=8.0,
    )
    db.session.commit()

    losses_envelope_dict = get_losses(submission_id)
    losses = losses_envelope_dict["losses"]

    assert losses_envelope_dict["total_items"] == 1
    assert len(losses) == 1

    result_loss = losses[0]
    assert result_loss["submission_id"] == str(submission_id)
    assert result_loss["organization_id"] == organization_id
    assert result_loss["loss_date"] == str(loss2.loss_date)
    assert result_loss["sum_of_total_net_incurred"] == 15.0
    assert result_loss["sum_of_net_paid_alae"] == 15.0
    assert result_loss["sum_of_net_outstanding_alae"] == 25.0
    assert result_loss["carrier"] == expected_carrier
    assert loss1.line_of_business.value in result_loss["line_of_business"].split(" | ")
    assert loss2.line_of_business.value in result_loss["line_of_business"].split(" | ")
    assert loss3.line_of_business.value in result_loss["line_of_business"].split(" | ")
    assert {*result_loss["loss_address"].split(" | ")} == {loss1.loss_address, loss2.loss_address, loss3.loss_address}
    assert result_loss["claim_number"] == expected_claim_number
    assert result_loss["coverage_rollup"] == coverage.name
    assert result_loss["claim_status"] == loss2.claim_status
    assert len(result_loss["source_files"]) == 2
    assert sorted(result_loss["source_files"], key=lambda x: x["id"]) == sorted(
        [{"id": str(file_id), "name": "Missing Name"}, {"id": str(other_file.id), "name": other_file.name}],
        key=lambda x: x["id"],
    )


def test_update_loss(losses_test_requirements):
    submission_id, organization_id, file_id = losses_test_requirements
    expected_carrier = "Progressive"
    expected_claim_number = "12345"
    coverage = coverage_fixture(organization_id=organization_id)
    loss1 = loss_fixture(
        file_id=file_id,
        coverage_id=coverage.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=5.0,
        carrier=expected_carrier,
        line_of_business=LineOfBusinessType.PROPERTY,
        loss_address="NY",
        claim_number=expected_claim_number,
        claim_status="SOME_BAD_VALUE",
        sum_of_net_paid_alae=7.0,
        sum_of_net_outstanding_alae=9.0,
        loss_type="Old Type",
    )
    db.session.commit()
    assert loss1.partial_hash is None
    assert loss1.full_hash is None

    assert Loss.query.count() == 1
    losses_envelope_dict = get_losses(submission_id)
    losses = losses_envelope_dict["losses"]

    result_loss = losses[0]
    updated_loss = update_loss(
        result_loss["id"],
        {
            "sum_of_total_net_incurred": 20.0,
            "sum_of_net_paid_alae": 20.0,
            "sum_of_net_outstanding_alae": 20.0,
            "line_of_business": "",
            "loss_type": "New Type",
        },
    )
    assert 20.0 == updated_loss["sum_of_total_net_incurred"]
    assert 20.0 == updated_loss["sum_of_net_paid_alae"]
    assert 20.0 == updated_loss["sum_of_net_outstanding_alae"]
    assert None == updated_loss["line_of_business"]
    assert "New Type" == updated_loss["loss_type"]
    losses = Loss.query.all()
    assert 1 == len(losses)
    loss = losses[0]
    assert loss.partial_hash is not None
    assert loss.full_hash is not None


def test_update_loss_with_merged_losses(losses_test_requirements):
    submission_id, organization_id, file_id = losses_test_requirements
    expected_carrier = "Progressive"
    expected_claim_number = "12345"
    coverage = coverage_fixture(organization_id=organization_id)
    loss1 = loss_fixture(
        file_id=file_id,
        coverage_id=coverage.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=5.0,
        carrier=expected_carrier,
        line_of_business=LineOfBusinessType.PROPERTY,
        loss_address="NY",
        claim_number=expected_claim_number,
        claim_status="SOME_BAD_VALUE",
        sum_of_net_paid_alae=7.0,
        sum_of_net_outstanding_alae=9.0,
    )
    loss2 = loss_fixture(
        file_id=file_id,
        coverage_id=coverage.id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=10, day=12),
        sum_of_total_net_incurred=5.0,
        carrier=expected_carrier + " 5",
        line_of_business=LineOfBusinessType.OTHER,
        loss_address="PA",
        claim_number=expected_claim_number,
        claim_status="OPEN",
        sum_of_net_paid_alae=4.0,
        sum_of_net_outstanding_alae=8.0,
    )
    db.session.commit()

    loss_id_1 = loss1.id
    loss_id_2 = loss2.id

    assert 2 == Loss.query.count()
    losses_envelope_dict = get_losses(submission_id)
    losses = losses_envelope_dict["losses"]

    result_loss = losses[0]
    updated_loss = update_loss(
        result_loss["id"],
        {
            "sum_of_total_net_incurred": 20.0,
            "sum_of_net_paid_alae": 20.0,
            "sum_of_net_outstanding_alae": 20.0,
        },
    )
    updated_loss_id = updated_loss.get("id")

    assert 20.0 == updated_loss["sum_of_total_net_incurred"] == 20.0
    assert 20.0 == updated_loss["sum_of_net_paid_alae"] == 20.0
    assert 20.0 == updated_loss["sum_of_net_outstanding_alae"] == 20.0
    assert updated_loss["merged_parent_id"] is None

    assert 3 == Loss.query.count()

    original_losses = Loss.query.filter_by(merged_parent_id=updated_loss_id).all()
    assert 2 == len(original_losses)
    assert any(loss for loss in original_losses if str(loss.id) == str(loss_id_1))
    assert any(loss for loss in original_losses if str(loss.id) == str(loss_id_2))

    updated_loss = Loss.query.filter_by(id=updated_loss_id).first()
    assert updated_loss.id is not None
    assert updated_loss.merged_parent_id is None
    assert updated_loss.partial_hash is not None
    assert updated_loss.full_hash is not None


def test_get_losses_filters_by_year(losses_test_requirements, default_loss):
    submission_id, _, _ = losses_test_requirements

    result = get_losses(submission_id=submission_id, year="2023")["losses"]
    assert len(result) == 1
    __verify_loss_run(default_loss, result[0])

    result = get_losses(submission_id=submission_id, year="2022")["losses"]
    assert len(result) == 0


def test_get_losses_filters_by_line_of_business(losses_test_requirements, default_loss):
    submission_id, _, _ = losses_test_requirements

    result = get_losses(submission_id=submission_id, line_of_business=default_loss.line_of_business)["losses"]
    assert len(result) == 1
    __verify_loss_run(default_loss, result[0])

    result = get_losses(submission_id=submission_id, line_of_business=LineOfBusinessType.BUSINESS_AUTO)["losses"]
    assert len(result) == 0


def test_get_losses_filters_by_file_id(losses_test_requirements, default_loss):
    submission_id, _, _ = losses_test_requirements

    result = get_losses(submission_id=submission_id, file_id=str(default_loss.file_id))["losses"]
    assert len(result) == 1
    __verify_loss_run(default_loss, result[0])

    result = get_losses(submission_id=submission_id, file_id=str(uuid.uuid4()))["losses"]
    assert len(result) == 0

    result = get_losses(submission_id=submission_id, file_id=None)["losses"]
    assert len(result) == 1
    __verify_loss_run(default_loss, result[0])


def test_get_losses_capitalizes_and_strips_line_of_business(
    losses_test_requirements, losses_for_original_line_of_balance_trimming_tests
):
    submission_id, _, _ = losses_test_requirements

    result = get_losses(submission_id=submission_id)["losses"]

    assert len(result) == 2
    assert result[0]["original_line_of_business"] == "BUSINESS AUTO"
    assert result[1]["original_line_of_business"] == "BUSINESS AUTO | FIDUCIARY LIABILITY"


def test_get_losses_supports_pagination(losses_test_requirements, default_loss):
    submission_id, organization_id, file_id = losses_test_requirements
    additional_loss = loss_fixture(
        file_id=file_id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2022, month=7, day=22),
        sum_of_total_net_incurred=3.0,
        carrier="Nationwide",
        line_of_business=LineOfBusinessType.BUSINESS_AUTO,
        loss_address="NC",
        claim_number="54321",
    )
    db.session.commit()

    losses_envelope_dict = get_losses(submission_id=submission_id, page=1, per_page=1)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 1
    __verify_loss_run(default_loss, losses[0])

    assert losses_envelope_dict["page"] == 1
    assert losses_envelope_dict["total_pages"] == 2
    assert losses_envelope_dict["total_items"] == 2
    assert losses_envelope_dict["has_next"] is True

    losses_envelope_dict = get_losses(submission_id=submission_id, page=2, per_page=1)
    losses = losses_envelope_dict["losses"]
    assert len(losses) == 1
    __verify_loss_run(additional_loss, losses[0])

    assert losses_envelope_dict["page"] == 2
    assert losses_envelope_dict["total_pages"] == 2
    assert losses_envelope_dict["total_items"] == 2
    assert losses_envelope_dict["has_next"] is False


def test_get_losses_no_results(losses_test_requirements):
    submission_id, _, _ = losses_test_requirements
    losses_envelope_dict = get_losses(submission_id=submission_id)
    assert len(losses_envelope_dict["losses"]) == 0
    assert losses_envelope_dict["page"] == 1
    assert losses_envelope_dict["total_pages"] == 1
    assert losses_envelope_dict["total_items"] == 0
    assert losses_envelope_dict["has_next"] is False


def __verify_loss_run(expected: Loss, actual: dict[str, Any]) -> None:
    assert str(expected.submission_id) == actual["submission_id"]
    assert expected.organization_id == actual["organization_id"]
    assert str(expected.loss_date) == actual["loss_date"]
    assert expected.sum_of_total_net_incurred == actual["sum_of_total_net_incurred"]
    assert expected.carrier == actual["carrier"]
    assert expected.line_of_business == actual["line_of_business"]
    assert expected.loss_address == actual["loss_address"]
    assert expected.evidence == actual["evidence"]

    expected_loss_policy = str(expected.loss_policy_id) if expected.loss_policy_id else None
    assert expected_loss_policy == actual.get("loss_policy_id", None)


def test_get_loss_policies(losses_test_requirements, default_loss_policy):
    loss_policy_2 = loss_policy_fixture(
        submission_id=losses_test_requirements.submission_id,
        organization_id=losses_test_requirements.organization_id,
        number="F4321",
        effective_date=datetime(year=2022, month=2, day=14),
        expiration_date=datetime(year=2023, month=2, day=14),
        raw_line_of_business="testlob2",
        line_of_business=LineOfBusinessType.OTHER,
        original_line_of_business="testlob2",
    )
    db.session.commit()

    results = get_loss_policies(losses_test_requirements.submission_id)

    assert len(results) == 2
    __verify_loss_policy(default_loss_policy, results[0])
    __verify_loss_policy(loss_policy_2, results[1])


def test_get_loss_policies_none_exist(losses_test_requirements):
    results = get_loss_policies(losses_test_requirements.submission_id)
    assert len(results) == 0


def __verify_loss_policy(expected: LossPolicy, actual: dict[str, Any]) -> None:
    assert str(expected.id) == actual["id"]
    assert expected.number == actual["number"]
    assert str(expected.submission_id) == actual["submission_id"]
    assert expected.organization_id == actual["organization_id"]
    assert str(expected.effective_date) == actual["effective_date"]
    assert str(expected.expiration_date) == actual["expiration_date"]
    assert expected.raw_line_of_business == actual["raw_line_of_business"]
    assert expected.line_of_business == actual["line_of_business"]
    assert expected.original_line_of_business == actual["original_line_of_business"]
    assert expected.evidence == actual["evidence"]


def test_get_losses_summary(losses_test_requirements):
    submission_id, organization_id, file_id = losses_test_requirements
    loss_fixture(
        file_id=file_id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=5.0,
        carrier="Progressive",
        line_of_business=LineOfBusinessType.OTHER,
        loss_address="PA",
        claim_number="12345",
    ),
    loss_fixture(
        file_id=file_id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2022, month=5, day=2),
        sum_of_total_net_incurred=7.0,
        carrier="Progressive",
        line_of_business=LineOfBusinessType.BUSINESS_AUTO,
        loss_address="MN",
        claim_number="12345",
    ),
    loss_fixture(
        file_id=file_id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2019, month=11, day=25),
        sum_of_total_net_incurred=9.0,
        carrier="UFG",
        line_of_business=LineOfBusinessType.OTHER,
        loss_address="PA",
        claim_number="12346",
    ),
    db.session.commit()

    summary_of_losses_dict = get_losses_summary(submission_id)
    assert summary_of_losses_dict["total_loss_value"] == 21.0
    assert summary_of_losses_dict["number_of_claims"] == 2
    assert len(summary_of_losses_dict["lob_groupings"]) == 2
    __assert_contains_grouping(summary_of_losses_dict["lob_groupings"], LineOfBusinessType.OTHER.value, 14.0)
    __assert_contains_grouping(summary_of_losses_dict["lob_groupings"], LineOfBusinessType.BUSINESS_AUTO.value, 7.0)

    assert len(summary_of_losses_dict["carrier_groupings"]) == 2
    __assert_contains_grouping(summary_of_losses_dict["carrier_groupings"], "Progressive", 12.0)
    __assert_contains_grouping(summary_of_losses_dict["carrier_groupings"], "UFG", 9.0)

    assert len(summary_of_losses_dict["year_groupings"]) == 3
    __assert_contains_grouping(summary_of_losses_dict["year_groupings"], "2019", 9.0)
    __assert_contains_grouping(summary_of_losses_dict["year_groupings"], "2022", 7.0)
    __assert_contains_grouping(summary_of_losses_dict["year_groupings"], "2023", 5.0)

    assert len(summary_of_losses_dict["location_groupings"]) == 2
    __assert_contains_grouping(summary_of_losses_dict["location_groupings"], "MN", 7.0)
    __assert_contains_grouping(summary_of_losses_dict["location_groupings"], "PA", 14.0)


def test_get_losses_summary_filters_from_invalid_files(
    losses_test_requirements, loss_from_partially_complete_file, loss_from_invalid_file
):
    submission_id, _, _ = losses_test_requirements

    summary_of_losses_dict = get_losses_summary(submission_id)
    assert summary_of_losses_dict["total_loss_value"] == 0.0
    assert summary_of_losses_dict["number_of_claims"] == 0
    assert len(summary_of_losses_dict["year_groupings"]) == 0
    assert len(summary_of_losses_dict["location_groupings"]) == 0
    assert len(summary_of_losses_dict["lob_groupings"]) == 0
    assert len(summary_of_losses_dict["carrier_groupings"]) == 0


def test_get_losses_summary_filters_duplicates(losses_test_requirements, duplicate_loss):
    submission_id, _, _ = losses_test_requirements

    summary_of_losses_dict = get_losses_summary(submission_id)
    assert summary_of_losses_dict["total_loss_value"] == 0.0
    assert summary_of_losses_dict["number_of_claims"] == 0
    assert len(summary_of_losses_dict["year_groupings"]) == 0
    assert len(summary_of_losses_dict["location_groupings"]) == 0
    assert len(summary_of_losses_dict["lob_groupings"]) == 0
    assert len(summary_of_losses_dict["carrier_groupings"]) == 0


def test_get_losses_summary_considers_original_lob(losses_test_requirements):
    submission_id, organization_id, file_id = losses_test_requirements
    loss_fixture(
        file_id=file_id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=5.0,
        carrier="Progressive",
        original_line_of_business="OG",
        line_of_business=LineOfBusinessType.BUSINESS_AUTO,
        loss_address="PA",
        claim_number="12345",
    ),
    loss_fixture(
        file_id=file_id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=7.0,
        carrier="Progressive",
        original_line_of_business="OG",
        line_of_business=None,
        loss_address="PA",
        claim_number="12346",
    ),
    loss_fixture(
        file_id=file_id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=9.0,
        carrier="Progressive",
        original_line_of_business=None,
        line_of_business=None,
        loss_address="PA",
        claim_number="12347",
    ),
    db.session.commit()

    summary_of_losses_dict = get_losses_summary(submission_id)
    assert summary_of_losses_dict["total_loss_value"] == 21.0
    assert summary_of_losses_dict["number_of_claims"] == 3

    assert len(summary_of_losses_dict["lob_groupings"]) == 3
    __assert_contains_grouping(summary_of_losses_dict["lob_groupings"], "Og", 7.0)
    __assert_contains_grouping(summary_of_losses_dict["lob_groupings"], LineOfBusinessType.BUSINESS_AUTO.value, 5.0)
    __assert_contains_grouping(summary_of_losses_dict["lob_groupings"], LineOfBusinessType.UNKNOWN.value, 9.0)


def test_get_losses_summary_groups_carriers_with_numeric_differences(losses_test_requirements):
    submission_id, organization_id, file_id = losses_test_requirements
    loss_fixture(
        file_id=file_id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=5.0,
        carrier="Progressive",
        original_line_of_business="OG",
        line_of_business=LineOfBusinessType.BUSINESS_AUTO,
        loss_address="PA",
        claim_number="12345",
    ),
    loss_fixture(
        file_id=file_id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=7.0,
        carrier="Progressive 1",
        original_line_of_business=None,
        line_of_business=LineOfBusinessType.BUSINESS_AUTO,
        loss_address="PA",
        claim_number="12346",
    ),
    loss_fixture(
        file_id=file_id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=9.0,
        carrier="Progressive 2",
        original_line_of_business=None,
        line_of_business=LineOfBusinessType.BUSINESS_AUTO,
        loss_address="PA",
        claim_number="12347",
    ),
    db.session.commit()

    summary_of_losses_dict = get_losses_summary(submission_id)
    assert summary_of_losses_dict["total_loss_value"] == 21.0
    assert summary_of_losses_dict["number_of_claims"] == 3

    assert len(summary_of_losses_dict["carrier_groupings"]) == 1
    __assert_contains_grouping(summary_of_losses_dict["carrier_groupings"], "Progressive", 21.0)


def test_get_losses_deduplicates_loss_runs_with_carriers_with_numeric_differences(losses_test_requirements):
    submission_id, organization_id, file_id = losses_test_requirements
    loss_fixture(
        file_id=file_id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=5.0,
        carrier="Progre4ssive",
        original_line_of_business="OG",
        line_of_business=LineOfBusinessType.BUSINESS_AUTO,
        loss_address="PA",
        claim_number="12345",
    ),
    loss_fixture(
        file_id=file_id,
        submission_id=submission_id,
        organization_id=organization_id,
        loss_date=datetime(year=2023, month=9, day=12),
        sum_of_total_net_incurred=7.0,
        carrier="Progressive 12",
        original_line_of_business=None,
        line_of_business=LineOfBusinessType.BUSINESS_AUTO,
        loss_address="PA",
        claim_number="12345",
    ),
    db.session.commit()

    summary_of_losses_dict = get_losses_summary(submission_id)
    assert summary_of_losses_dict["total_loss_value"] == 12.0
    assert summary_of_losses_dict["number_of_claims"] == 1


def test_get_losses_summary_capitalizes_and_strips_original_line_of_business(
    losses_test_requirements, losses_for_original_line_of_balance_trimming_tests
):
    submission_id, _, _ = losses_test_requirements

    summary_of_losses_dict = get_losses_summary(submission_id)

    assert summary_of_losses_dict["number_of_claims"] == 2
    assert len(summary_of_losses_dict["lob_groupings"]) == 2
    __assert_contains_grouping(summary_of_losses_dict["lob_groupings"], "Business Auto", 8.0)
    __assert_contains_grouping(summary_of_losses_dict["lob_groupings"], "Fiduciary Liability", 3.0)


def test_update_proposed_effective_date_on_expired_submission(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=False,
            is_machine_user=False,
            organization_id=1,
            email="<EMAIL>",
            id=1,
            is_being_impersonated=False,
            applicable_settings=Settings(),
            has_submission_permission=lambda type, id: True,
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture()
    user_fixture()
    report = report_fixture(tier=None)
    submission = submission_fixture(
        stage=SubmissionStage.EXPIRED, proposed_effective_date=datetime(2020, 1, 1), report=report
    )
    db.session.commit()

    update_submission(
        str(submission.id),
        {
            "proposed_effective_date": "2020-10-30T05:27:34Z",
        },
    )
    submission = Submission.query.get(submission.id)

    assert submission.stage == SubmissionStage.EXPIRED

    update_submission(
        str(submission.id),
        {
            "proposed_effective_date": "2050-10-30T05:27:34",
        },
    )
    submission = Submission.query.get(submission.id)

    assert submission.stage == SubmissionStage.ON_MY_PLATE


def test_update_submission_to_stuck(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=False,
            is_machine_user=False,
            organization_id=1,
            email="<EMAIL>",
            is_being_impersonated=False,
            applicable_settings=Settings(),
            has_submission_permission=lambda type, id: True,
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture()
    user_fixture()
    report = report_fixture(tier=None)
    submission = submission_fixture(report=report)
    db.session.commit()

    stuck_reason = "Not sure what to confirm the unconfirmed entity to"

    update_submission(
        str(submission.id),
        {
            "stuck_reason": stuck_reason,
        },
    )
    submission = Submission.query.get(submission.id)

    assert submission.stuck_reason == stuck_reason


def test_update_submission_clearing_assignee(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=False,
            is_machine_user=False,
            organization_id=1,
            email="<EMAIL>",
            is_being_impersonated=False,
            applicable_settings=Settings(),
            has_submission_permission=lambda type, id: True,
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture()
    user_fixture()
    report = report_fixture(tier=None)
    submission = submission_fixture(report=report, stage=SubmissionStage.QUOTED_BOUND)
    db.session.commit()

    update_submission(
        str(submission.id),
        {
            "clearing_assignee_id": report.owner_id,
        },
    )
    submission = Submission.query.get(submission.id)

    assert submission.clearing_assignee_id == report.owner_id


def test_assign_submission_to_clearing_user(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=False,
            is_machine_user=False,
            organization_id=1,
            email="<EMAIL>",
            is_being_impersonated=False,
            applicable_settings=Settings(),
            has_submission_permission=lambda type, id: True,
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture()
    user_fixture()
    report = report_fixture(tier=None)
    submission = submission_fixture(report=report, stage=SubmissionStage.CLEARING_ISSUE)
    db.session.commit()

    assign_submission_to_clearing_user(submission.id, {"clearing_assignee_id": report.owner_id})

    assert Submission.query.get(submission.id).clearing_assignee_id == report.owner_id


def test_assign_submission_to_clearing_user_returns_409_if_clearing_assignee_already_assigned(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=False,
            is_machine_user=False,
            organization_id=1,
            email="<EMAIL>",
            is_being_impersonated=False,
            applicable_settings=Settings(),
            has_submission_permission=lambda type, id: True,
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture()
    user_1 = user_fixture()
    user_2 = user_fixture()
    report = report_fixture(tier=None)
    submission = submission_fixture(report=report, stage=SubmissionStage.CLEARING_ISSUE)
    submission.clearing_assignee_id = user_1.id
    db.session.commit()

    with pytest.raises(werkzeug.exceptions.Conflict, match="Submission already assigned to clearing user"):
        assign_submission_to_clearing_user(submission.id, {"clearing_assignee_id": user_2.id})


def test_update_submission_gl_iso_code(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=False,
            is_machine_user=False,
            organization_id=1,
            email="<EMAIL>",
            name="foo",
            is_being_impersonated=False,
            applicable_settings=Settings(),
            has_submission_permission=lambda type, id: True,
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture()
    user_fixture()
    report = report_fixture(tier=None)
    submission = submission_fixture(report=report, stage=SubmissionStage.QUOTED_BOUND)
    db.session.commit()

    update_submission(str(submission.id), {"iso_gl_code": "ISO_GL_12345"})
    db.session.rollback()

    submission = Submission.query.get(submission.id)
    assert submission.iso_gl_code == "ISO_GL_12345"


# TODO(ENG-28064): Re-enable
# def test_update_submission_naics_sic_sync(app_context, mocker):
#     mocker.patch(
#         "flask_login.utils._get_user",
#         return_value=AnonObj(
#             is_internal_machine_user=False,
#             is_machine_user=False,
#             organization_id=1,
#             email="<EMAIL>",
#             is_being_impersonated=False,
#             applicable_settings=Settings(),
#             has_submission_permission=lambda type, id: True,
#         ),
#     )
#     mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})
#
#     organization_fixture()
#     user_fixture()
#     report = report_fixture(tier=None)
#     submission = submission_fixture(report=report, primary_naics_code="NAICS_123456")
#     new_naics_code = "NAICS_111150"
#     taxonomy_mapping_fixture(
#         mapping_type=MappingTypeEnum.NAICS_TO_SIC,
#         code=new_naics_code,
#         mapped_codes=["SIC_1234", "SIC_2345"],
#     )
#     sic_1 = "SIC_0000"
#     taxonomy_mapping_fixture(mapping_type=MappingTypeEnum.SIC_TO_NAICS, code=sic_1, mapped_codes=["NAICS_000000"])
#     db.session.commit()
#
#     update_submission(
#         str(submission.id),
#         {
#             "primary_naics_code": new_naics_code,
#         },
#     )
#     submission = Submission.query.get(submission.id)
#
#     assert submission.primary_naics_code == new_naics_code
#     assert submission.sic_code == "SIC_1234"
#
#     update_submission(
#         str(submission.id),
#         {
#             "sic_code": sic_1,
#         },
#     )
#     submission = Submission.query.get(submission.id)
#     assert submission.sic_code == sic_1
#     assert submission.primary_naics_code == "NAICS_000000"
#
#     sic_2 = "SIC_0001"
#     taxonomy_mapping_fixture(mapping_type=MappingTypeEnum.SIC_TO_NAICS, code=sic_2, mapped_codes=["NAICS_000001"])
#     update_submission(
#         str(submission.id),
#         {
#             "primary_naics_code": new_naics_code,
#             "sic_code": sic_2,
#         },
#     )
#     assert submission.sic_code == sic_2
#     assert submission.primary_naics_code == "NAICS_000001"
#
#
# def test_update_submission_naics_sic_5_sync(app_context, mocker):
#     mocker.patch(
#         "flask_login.utils._get_user",
#         return_value=AnonObj(
#             is_internal_machine_user=False,
#             is_machine_user=False,
#             organization_id=1,
#             email="<EMAIL>",
#             is_being_impersonated=False,
#             applicable_settings=Settings(),
#             has_submission_permission=lambda type, id: True,
#         ),
#     )
#     mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})
#
#     organization_fixture(id=CNA_ORGANIZATION_ID)
#     user_fixture(organization_id=CNA_ORGANIZATION_ID)
#     report = report_fixture(tier=None, organization_id=CNA_ORGANIZATION_ID)
#     submission = submission_fixture(report=report, primary_naics_code="NAICS_123456")
#     new_naics_code = "NAICS_111150"
#     description = taxonomy_description_fixture(
#         code=new_naics_code,
#         description="New NAICS",
#     )
#     taxonomy_mapping_fixture(
#         mapping_type=MappingTypeEnum.NAICS_TO_SIC,
#         code=new_naics_code,
#         mapped_codes=["SIC_1234", "SIC_2345"],
#         taxonomy_description=description,
#     )
#     taxonomy_mapping_fixture(
#         mapping_type=MappingTypeEnum.NAICS_TO_SIC5,
#         code=new_naics_code,
#         mapped_codes=["SIC_12345", "SIC_23456"],
#         taxonomy_description=description,
#     )
#     sic_1 = "SIC_00000"
#     taxonomy_mapping_fixture(mapping_type=MappingTypeEnum.SIC5_TO_NAICS, code=sic_1, mapped_codes=["NAICS_000000"])
#     db.session.commit()
#
#     update_submission(
#         str(submission.id),
#         {
#             "primary_naics_code": new_naics_code,
#         },
#     )
#     submission = Submission.query.get(submission.id)
#
#     assert submission.primary_naics_code == new_naics_code
#     assert submission.sic_code == "SIC_12345"
#
#     update_submission(
#         str(submission.id),
#         {
#             "sic_code": sic_1,
#         },
#     )
#     submission = Submission.query.get(submission.id)
#     assert submission.sic_code == sic_1
#     assert submission.primary_naics_code == "NAICS_000000"
#
#     sic_2 = "SIC_00001"
#     taxonomy_mapping_fixture(mapping_type=MappingTypeEnum.SIC5_TO_NAICS, code=sic_2, mapped_codes=["NAICS_000001"])
#     update_submission(
#         str(submission.id),
#         {
#             "primary_naics_code": new_naics_code,
#             "sic_code": sic_2,
#         },
#     )
#     assert submission.sic_code == sic_2
#     assert submission.primary_naics_code == "NAICS_000001"


def test_update_submission_assigns_garage_coverage_for_admiral(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=False,
            is_machine_user=False,
            organization_id=49,
            email="<EMAIL>",
            is_being_impersonated=False,
            applicable_settings=Settings(),
            has_submission_permission=lambda type, id: True,
            name="XYZ",
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture(id=49)
    user_fixture(organization_id=49)
    coverage_fixture(name="garageDealers", organization_id=49, is_disabled=False)
    existing_coverage = coverage_fixture(
        name="discontinuedOperations", organization_id=49, is_disabled=False, logical_group="LIABILITY"
    )
    report = report_fixture(tier=None, organization_id=49)
    submission = submission_fixture(report=report, primary_naics_code="NAICS_123456")
    submission_coverage_fixture(submission_id=submission.id, coverage_id=existing_coverage.id)
    db.session.commit()

    # NAICS is still not verfied, no coverage should be assigned
    update_submission(
        str(submission.id),
        {
            "primary_naics_code": "NAICS_812930",
        },
    )
    submission = Submission.query.get(submission.id)

    assert 1 == len(submission.coverages)

    # NAICS is verified and is parking + garage naics, new coverage should be assigned
    update_submission(
        str(submission.id),
        {
            "is_naics_verified": True,
        },
    )
    submission = Submission.query.get(submission.id)

    submission_coverages = submission.coverages
    assert 2 == len(submission_coverages)
    assert any(i for i in submission_coverages if i.coverage.name == "garageDealers" and i.coverage_type == "PRIMARY")

    # Subsequent updates succeed
    update_submission(
        str(submission.id),
        {
            "processing_state": "COMPLETED",
        },
    )
    assert 2 == len(submission_coverages)
    assert any(i for i in submission_coverages if i.coverage.name == "garageDealers")


def test_update_submission_does_not_assign_garage_coverage(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=False,
            is_machine_user=False,
            organization_id=49,
            email="<EMAIL>",
            is_being_impersonated=False,
            applicable_settings=Settings(),
            has_submission_permission=lambda type, id: True,
            name="XYZ",
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture(id=49)
    user_fixture(organization_id=49)
    coverage_fixture(name="garageDealers", organization_id=49, is_disabled=False)
    existing_coverage = coverage_fixture(name="liability", organization_id=49, is_disabled=False)
    report = report_fixture(tier=None, organization_id=49)
    submission = submission_fixture(report=report, primary_naics_code="NAICS_123456")
    submission_coverage_fixture(submission_id=submission.id, coverage_id=existing_coverage.id)
    db.session.commit()

    # Coverage without LIABILITY logical_group is assigned so no additional coverage is added
    update_submission(
        str(submission.id),
        {
            "primary_naics_code": "NAICS_812930",
            "is_naics_verified": True,
        },
    )
    submission = Submission.query.get(submission.id)

    assert 1 == len(submission.coverages)


def test_update_submission_not_stuck_if_some_unconfirmed_entities(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=False,
            is_machine_user=False,
            organization_id=1,
            email="<EMAIL>",
            is_being_impersonated=False,
            applicable_settings=Settings(),
            has_submission_permission=lambda type, id: True,
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture()
    user_fixture()
    report = report_fixture(tier=None)
    submission = submission_fixture(report=report)
    file = file_fixture(submission_id=submission.id)
    business_resolution_data = [BusinessResolutionData(entity_idx=idx, entity_id=None) for idx in range(20)]
    business_resolution_data = {
        "resolution_data": BusinessResolutionDataSchema().dump(business_resolution_data, many=True)
    }
    processed_file_fixture(file_id=file.id, business_resolution_data=business_resolution_data)
    db.session.commit()

    update_submission(
        str(submission.id),
        {"processing_state": "BUSINESS_CONFIRMATION"},
    )
    submission = Submission.query.get(submission.id)

    assert submission.stuck_reason is None


def __assert_contains_grouping(groupings: list[dict[str, Any]], group_name: str, group_value: float) -> None:
    assert any(
        [
            grouping
            for grouping in groupings
            if grouping["group_name"] == group_name and grouping["group_value"] == group_value
        ]
    )


def __verify_one_submission_history_record_added(submission_id: UUID, submission_action_type: SubmissionActionType):
    submission_history = (
        SubmissionHistory.query.filter(SubmissionHistory.submission_id == submission_id)
        .filter(SubmissionHistory.submission_action_type == submission_action_type)
        .all()
    )
    assert len(submission_history) == 1
    assert submission_history[0].submission_id == submission_id


def __format_loss_policy_date(loss_policy_date):
    return loss_policy_date.strftime("%Y-%m-%d")


def test_put_loss_policy_defaults_number_to_unknown(
    app_context, loss_policy_submission_requirements, current_user_check_bypass
):
    request_body = {
        "number": None,
        "organization_id": loss_policy_submission_requirements.report.organization_id,
        "raw_line_of_business": "test lob",
        "effective_date": "2023-01-01",
        "expiration_date": "2024-01-01",
    }
    loss_policy, status_code = put_loss_policy(
        submission_id=str(loss_policy_submission_requirements.id), body=request_body
    )

    assert HTTPStatus.CREATED == status_code
    assert loss_policy is not None
    assert request_body.get("number") == "UNKNOWN"

    request_body = {
        "organization_id": loss_policy_submission_requirements.report.organization_id,
        "raw_line_of_business": "test lob 2",
        "effective_date": "2024-01-01",
        "expiration_date": "2025-01-01",
    }
    loss_policy, status_code = put_loss_policy(
        submission_id=str(loss_policy_submission_requirements.id), body=request_body
    )

    assert HTTPStatus.CREATED == status_code
    assert loss_policy is not None
    assert request_body.get("number") == "UNKNOWN"


def test_put_loss_policy_creates_new_policy_when_none_exists(
    app_context, loss_policy_submission_requirements, current_user_check_bypass
):
    submission_id = str(loss_policy_submission_requirements.id)
    organization_id = loss_policy_submission_requirements.report.organization_id

    expected_effective_date = "2023-01-01"
    expected_expiration_date = "2024-01-01"
    request_body = {
        "number": "testnumber",
        "organization_id": organization_id,
        "raw_line_of_business": "test lob",
        "effective_date": expected_effective_date,
        "expiration_date": expected_expiration_date,
    }
    loss_policy, status_code = put_loss_policy(submission_id=submission_id, body=request_body)

    assert HTTPStatus.CREATED == status_code
    assert loss_policy is not None
    assert submission_id == str(loss_policy.get("submission_id"))
    assert request_body.get("number") == loss_policy.get("number")
    assert request_body.get("organization_id") == loss_policy.get("organization_id")
    assert request_body.get("raw_line_of_business") == loss_policy.get("raw_line_of_business")
    assert expected_effective_date == loss_policy.get("effective_date")
    assert expected_expiration_date == loss_policy.get("expiration_date")


def test_put_loss_policy_updates_policy_when_one_exists(
    app_context, loss_policy_submission_requirements, current_user_check_bypass
):
    submission_id = str(loss_policy_submission_requirements.id)
    organization_id = loss_policy_submission_requirements.report.organization_id

    original_loss_policy = loss_policy_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        number="12345",
        effective_date="2023-04-14",
        expiration_date="2023-05-14",
        raw_line_of_business="testlob",
        line_of_business=LineOfBusinessType.OTHER,
        original_line_of_business="testlob",
    )

    expected_effective_date = "2023-01-01"
    expected_expiration_date = "2024-01-01"
    request_body = {
        "id": original_loss_policy.id,
        "organization_id": original_loss_policy.organization_id,
        "number": "54321",
        "effective_date": expected_effective_date,
        "expiration_date": expected_expiration_date,
    }
    updated_loss_policy, status_code = put_loss_policy(submission_id=submission_id, body=request_body)

    assert HTTPStatus.OK == status_code
    assert updated_loss_policy is not None
    assert submission_id == str(updated_loss_policy.get("submission_id"))
    assert request_body.get("number") == updated_loss_policy.get("number")
    assert request_body.get("organization_id") == updated_loss_policy.get("organization_id")
    assert original_loss_policy.raw_line_of_business == updated_loss_policy.get("raw_line_of_business")
    assert expected_effective_date == __format_loss_policy_date(original_loss_policy.effective_date)
    assert expected_expiration_date == __format_loss_policy_date(original_loss_policy.expiration_date)


def test_put_loss_policy_prevents_duplicates(
    app_context, loss_policy_submission_requirements, current_user_check_bypass
):
    submission_id = str(loss_policy_submission_requirements.id)
    organization_id = loss_policy_submission_requirements.report.organization_id

    effective_date = "2023-04-14"
    expiration_date = "2023-05-14"
    original_loss_policy = loss_policy_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        number="12345",
        effective_date=effective_date,
        expiration_date=expiration_date,
        raw_line_of_business="   testlob   ",
        original_line_of_business="testlob",
        line_of_business=LineOfBusinessType.OTHER,
    )
    db.session.commit()

    assert LossPolicy.query.count() == 1

    request_body = {
        "organization_id": original_loss_policy.organization_id,
        "number": original_loss_policy.number,
        "effective_date": effective_date,
        "expiration_date": expiration_date,
        "raw_line_of_business": original_loss_policy.raw_line_of_business.upper(),
    }

    with pytest.raises(Conflict):
        put_loss_policy(submission_id=submission_id, body=request_body)

    loss_policies = db.session.query(LossPolicy).all()
    assert 1 == len(loss_policies)
    assert original_loss_policy.raw_line_of_business == loss_policies[0].raw_line_of_business
    assert original_loss_policy.line_of_business == loss_policies[0].line_of_business


def test_update_loss_lob_bulk_unbalanced_request(
    app_context, current_user_check_bypass, losses_test_requirements, mocker
):
    submission_id, _, _ = losses_test_requirements

    request = {"loss_ids": [uuid4()], "lobs": ["test1", "test2"]}

    with pytest.raises(BadRequest):
        update_loss_lob_bulk(submission_id, request)


def test_update_loss_lob_bulk_loss_not_found(app_context, current_user_check_bypass, losses_test_requirements, mocker):
    submission_id, organization_id, file_id = losses_test_requirements

    loss = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        claim_number="12345",
    )
    db.session.commit()

    request = {"loss_ids": [loss.id, uuid4()], "lobs": ["test1", "test2"]}

    with pytest.raises(NotFound):
        update_loss_lob_bulk(submission_id, request)


def test_update_loss_lob_bulk(app_context, current_user_check_bypass, losses_test_requirements, mocker):
    submission_id, organization_id, file_id = losses_test_requirements

    loss_1 = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        lob_raw=None,
        claim_number="12345",
        carrier=None,
    )
    loss_2 = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        lob_raw="test124",
        claim_number="54321",
        carrier=None,
    )
    loss_3 = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        lob_raw="abc123",
        claim_number="8675309",
        carrier=None,
    )

    db.session.commit()

    expected_lob = "BUSINESS_AUTO"
    expected_lob_2 = "PROPERTY"
    request = {
        "loss_ids": [loss_1.id, loss_2.id, loss_3.id],
        "lobs": [expected_lob, expected_lob_2, None],
        "types": [expected_lob, expected_lob_2, None],
    }

    update_loss_lob_bulk(submission_id, request)

    assert Loss.query.filter_by(id=loss_1.id, line_of_business=expected_lob, loss_type=expected_lob).count() == 1
    assert Loss.query.filter_by(id=loss_2.id, line_of_business=expected_lob_2, loss_type=expected_lob_2).count() == 1
    assert Loss.query.filter_by(id=loss_3.id).first().line_of_business is None


def test_update_loss_lob_bulk_updates_hashes(app_context, current_user_check_bypass, losses_test_requirements, mocker):
    submission_id, organization_id, file_id = losses_test_requirements

    loss_1 = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        lob_raw=None,
        claim_number="12345",
        carrier=None,
        line_of_business="Unknown",
    )
    assert loss_1.full_hash is None

    db.session.commit()

    request = {"loss_ids": [loss_1.id], "lobs": ["BUSINESS_AUTO"]}

    update_loss_lob_bulk(submission_id, request)

    losses = Loss.query.filter_by(id=loss_1.id).all()
    assert 1 == len(losses)
    loss = losses[0]
    assert LineOfBusinessType.BUSINESS_AUTO == loss.line_of_business
    assert loss.full_hash is not None


def test_get_loss_lob_inference_requirements(app_context, current_user_check_bypass, losses_test_requirements):
    submission_id, organization_id, file_id = losses_test_requirements
    file_2 = file_fixture(submission_id=submission_id)
    _, submission_2 = report_and_submission_fixture()
    file_3 = file_fixture(submission_id=submission_2.id)
    db.session.commit()

    expected_loss = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        claim_number="12345",
        lob_raw="test",
        original_line_of_business="test",
        line_of_business=None,
        loss_type_raw="test",
        carrier="test carrier",
        claim_description="test description",
    )
    different_file_loss = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_2.id,
        claim_number="12345",
        lob_raw="test",
        original_line_of_business="test",
        line_of_business=None,
        loss_type="test",
        carrier="test carrier",
        claim_description="test description",
    )
    different_submission_loss = loss_fixture(
        submission_id=submission_2.id,
        organization_id=organization_id,
        file_id=file_3.id,
        claim_number="12345",
        lob_raw="test",
        original_line_of_business="test",
        line_of_business=None,
        carrier="test carrier",
        claim_description="test description",
    )
    original_lob_none_loss = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        claim_number="12345",
        lob_raw="test",
        original_line_of_business=None,
        line_of_business=None,
        carrier="test carrier",
        claim_description="test description",
    )
    lob_exists_loss = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        claim_number="12345",
        lob_raw="test",
        original_line_of_business="test",
        line_of_business="BUSINESS_AUTO",
        carrier="test carrier",
        claim_description="test description",
    )
    carrier_none_loss = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        claim_number="12345",
        lob_raw="test",
        original_line_of_business="test",
        line_of_business=None,
        carrier=None,
        claim_description="test description",
    )
    description_none_loss = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        claim_number="12345",
        lob_raw="test",
        original_line_of_business="test",
        line_of_business=None,
        carrier="test carrier",
        claim_description=None,
    )

    db.session.commit()

    results = get_loss_lob_inference_requirements(submission_id)
    assert 2 == len(results)
    assert any(req for req in results if str(req.get("id")) == str(expected_loss.id))
    assert any(
        req for req in results if str(req.get("id")) == str(expected_loss.id) and req.get("loss_type_raw") == "test"
    )

    assert any(req for req in results if str(req.get("id")) == str(different_file_loss.id))
    assert any(
        req
        for req in results
        if str(req.get("id")) == str(different_file_loss.id) and req.get("loss_type_raw") == "test"
    )

    results = get_loss_lob_inference_requirements(submission_id, file_id=file_id)
    assert 1 == len(results)
    assert any(req for req in results if str(req.get("id")) == str(expected_loss.id))


def test_delete_loss(app_context, current_user_check_bypass, losses_test_requirements):
    submission_id, organization_id, file_id = losses_test_requirements

    loss = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        lob_raw=None,
        claim_number="12345",
        carrier=None,
    )
    db.session.commit()

    assert 1 == Loss.query.filter_by(id=loss.id).count()

    _, status_code = delete_loss(id=str(loss.id))
    assert 204 == status_code

    assert 0 == Loss.query.filter_by(id=loss.id).count()


def test_delete_loss_merged_losses(app_context, current_user_check_bypass, losses_test_requirements):
    submission_id, organization_id, file_id = losses_test_requirements

    loss_1 = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        lob_raw=None,
        claim_number="12345",
        carrier="carrier1",
        is_manual=False,
    )
    loss_2 = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        lob_raw=None,
        claim_number="12345",
        carrier="carrier1",
        is_manual=False,
    )

    loss_3 = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        lob_raw=None,
        claim_number="12345",
        carrier="carrier1",
        is_manual=False,
    )

    different_is_manual_loss = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        lob_raw=None,
        claim_number="12345",
        carrier="carrier1",
        is_manual=True,
    )

    different_claim_number_loss = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        lob_raw=None,
        claim_number="54321",
        carrier="carrier1",
        is_manual=False,
    )

    different_carrier_loss = loss_fixture(
        submission_id=submission_id,
        organization_id=organization_id,
        file_id=file_id,
        lob_raw=None,
        claim_number="54321",
        carrier="carrier2",
        is_manual=False,
    )

    db.session.commit()

    assert 6 == Loss.query.filter_by(submission_id=submission_id).count()

    _, status_code = delete_loss(id=str(loss_1.id))
    assert 204 == status_code

    assert 3 == Loss.query.filter_by(submission_id=submission_id).count()
    assert 1 == Loss.query.filter_by(id=different_is_manual_loss.id).count()
    assert 1 == Loss.query.filter_by(id=different_claim_number_loss.id).count()
    assert 1 == Loss.query.filter_by(id=different_carrier_loss.id).count()


@pytest.fixture
def get_invoke_loss_run_processing_mocks(app_context, current_user_check_bypass, mocker):
    def inner(manual_processing_cache_exists: bool = False):
        process_file_mock = mocker.patch(
            "copilot.logic.loss_runs.manual_processing.current_app.workflows_client.invoke_copilot_workers_process_file"
        )
        manual_processing_cache_exists_mock = mocker.patch(
            "copilot.v3.controllers.submissions.manual_processing_cache_exists",
            return_value=manual_processing_cache_exists,
        )
        try_add_notifyee_to_manual_processing_cache = mocker.patch(
            "copilot.v3.controllers.submissions.try_add_notifyee_to_manual_processing_cache"
        )
        add_manual_processing_cache_entry_mock = mocker.patch(
            "copilot.v3.controllers.submissions.add_manual_processing_cache_entry"
        )

        return (
            process_file_mock,
            manual_processing_cache_exists_mock,
            try_add_notifyee_to_manual_processing_cache,
            add_manual_processing_cache_entry_mock,
        )

    return inner


@pytest.fixture
def get_invoke_loss_run_processing_requirements(app_context, current_user_check_bypass, mocker):
    def inner(
        loss_runs_enabled: bool,
        loss_runs_manual: bool,
        spreadsheet_file_processing_state: FileProcessingState = FileProcessingState.CLASSIFIED,
        pdf_file_processing_state: FileProcessingState = FileProcessingState.CLASSIFIED,
        is_internal_machine_user: bool = False,
    ):
        mocker.patch(
            "flask_login.utils._get_user",
            return_value=AnonObj(
                id=1,
                is_internal_machine_user=is_internal_machine_user,
                has_submission_permission=lambda type, id: True,
                organization_id=1,
                applicable_settings=Settings(loss_runs_enabled=loss_runs_enabled, loss_runs_manual=loss_runs_manual),
                email="<EMAIL>",
                is_being_impersonated=False,
            ),
        )
        mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})
        organization = organization_fixture()
        user_fixture()
        _, submission = report_and_submission_fixture()
        loss_run_spreadsheet_file = file_fixture(
            submission_id=submission.id,
            processing_state=spreadsheet_file_processing_state,
            file_type=FileType.LOSS_RUN,
            name="test.xlsx",
        )
        loss_run_pdf_file = file_fixture(
            submission_id=submission.id,
            processing_state=pdf_file_processing_state,
            file_type=FileType.LOSS_RUN,
            name="loss.pdf",
        )
        db.session.commit()

        return submission, loss_run_spreadsheet_file, loss_run_pdf_file

    return inner


def test_invoke_loss_run_processing(
    app_context, get_invoke_loss_run_processing_requirements, get_invoke_loss_run_processing_mocks
):
    submission, loss_run_spreadsheet_file, loss_run_pdf_file = get_invoke_loss_run_processing_requirements(
        loss_runs_enabled=True, loss_runs_manual=True
    )

    (
        process_file_mock,
        manual_processing_cache_exists_mock,
        try_add_notifyee_to_manual_processing_cache,
        add_manual_processing_cache_entry_mock,
    ) = get_invoke_loss_run_processing_mocks()

    invoke_loss_run_processing(submission_id=submission.id)

    process_file_mock.assert_has_calls(
        [call(submission.id, loss_run_spreadsheet_file.id), call(submission.id, loss_run_pdf_file.id)],
        any_order=True,
    )
    manual_processing_cache_exists_mock.assert_called_once()
    add_manual_processing_cache_entry_mock.assert_called_once()
    try_add_notifyee_to_manual_processing_cache.assert_not_called()


def test_invoke_loss_run_processing_cache_already_exists(
    app_context,
    get_invoke_loss_run_processing_requirements,
    get_invoke_loss_run_processing_mocks,
):
    submission, _, _ = get_invoke_loss_run_processing_requirements(loss_runs_enabled=True, loss_runs_manual=True)

    (
        process_file_mock,
        manual_processing_cache_exists_mock,
        try_add_notifyee_to_manual_processing_cache,
        add_manual_processing_cache_entry_mock,
    ) = get_invoke_loss_run_processing_mocks(manual_processing_cache_exists=True)

    invoke_loss_run_processing(submission_id=submission.id)

    manual_processing_cache_exists_mock.assert_called_once()
    try_add_notifyee_to_manual_processing_cache.assert_called_once()
    add_manual_processing_cache_entry_mock.assert_not_called()
    process_file_mock.assert_not_called()


def test_invoke_loss_run_processing_always_processes_for_internal_machine_user(
    app_context, get_invoke_loss_run_processing_requirements, get_invoke_loss_run_processing_mocks
):
    submission, loss_run_spreadsheet_file, loss_run_pdf_file = get_invoke_loss_run_processing_requirements(
        loss_runs_enabled=False,
        loss_runs_manual=False,
        is_internal_machine_user=True,
    )
    (
        process_file_mock,
        manual_processing_cache_exists_mock,
        try_add_notifyee_to_manual_processing_cache,
        add_manual_processing_cache_entry_mock,
    ) = get_invoke_loss_run_processing_mocks()

    invoke_loss_run_processing(submission_id=submission.id)

    process_file_mock.assert_has_calls(
        [call(submission.id, loss_run_spreadsheet_file.id), call(submission.id, loss_run_pdf_file.id)],
        any_order=True,
    )
    manual_processing_cache_exists_mock.assert_called_once()
    add_manual_processing_cache_entry_mock.assert_called_once()
    try_add_notifyee_to_manual_processing_cache.assert_not_called()


def test_invoke_loss_run_processing_file_not_classified(
    app_context, get_invoke_loss_run_processing_requirements, get_invoke_loss_run_processing_mocks
):
    submission, loss_run_spreadsheet_file, loss_run_pdf_file = get_invoke_loss_run_processing_requirements(
        loss_runs_enabled=True, loss_runs_manual=True, pdf_file_processing_state=FileProcessingState.NOT_CLASSIFIED
    )
    (
        process_file_mock,
        manual_processing_cache_exists_mock,
        try_add_notifyee_to_manual_processing_cache,
        add_manual_processing_cache_entry_mock,
    ) = get_invoke_loss_run_processing_mocks()

    invoke_loss_run_processing(submission_id=submission.id)

    process_file_mock.assert_called_once_with(submission.id, loss_run_spreadsheet_file.id)
    manual_processing_cache_exists_mock.assert_called_once()
    add_manual_processing_cache_entry_mock.assert_called_once()
    try_add_notifyee_to_manual_processing_cache.assert_not_called()


def test_invoke_loss_run_processing_loss_runs_not_enabled(
    app_context, get_invoke_loss_run_processing_requirements, get_invoke_loss_run_processing_mocks
):
    submission, loss_run_spreadsheet_file, loss_run_pdf_file = get_invoke_loss_run_processing_requirements(
        loss_runs_enabled=False, loss_runs_manual=True
    )
    (
        process_file_mock,
        manual_processing_cache_exists_mock,
        try_add_notifyee_to_manual_processing_cache,
        add_manual_processing_cache_entry_mock,
    ) = get_invoke_loss_run_processing_mocks()

    invoke_loss_run_processing(submission_id=submission.id)

    process_file_mock.assert_not_called()
    manual_processing_cache_exists_mock.assert_not_called()
    add_manual_processing_cache_entry_mock.assert_not_called()
    try_add_notifyee_to_manual_processing_cache.assert_not_called()


def test_invoke_loss_run_processing_loss_runs_manual_not_enabled(
    app_context, get_invoke_loss_run_processing_requirements, get_invoke_loss_run_processing_mocks
):
    submission, loss_run_spreadsheet_file, loss_run_pdf_file = get_invoke_loss_run_processing_requirements(
        loss_runs_enabled=True, loss_runs_manual=False
    )
    (
        process_file_mock,
        manual_processing_cache_exists_mock,
        try_add_notifyee_to_manual_processing_cache,
        add_manual_processing_cache_entry_mock,
    ) = get_invoke_loss_run_processing_mocks()

    invoke_loss_run_processing(submission_id=submission.id)

    process_file_mock.assert_not_called()
    manual_processing_cache_exists_mock.assert_not_called()
    add_manual_processing_cache_entry_mock.assert_not_called()
    try_add_notifyee_to_manual_processing_cache.assert_not_called()


def test_add_submission_client_id_with_active_shadow(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            organization_id=1,
            email="<EMAIL>",
            is_being_impersonated=False,
            has_submission_permission=always_has_permission,
        ),
    )

    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture()
    shadow_submission = shadow_submission_fixture(submission, with_files=False)

    client_id = "12345"
    add_client_submission_id(
        str(submission.id), {"client_submission_id": client_id, "submission_id": str(submission.id)}
    )

    assert SubmissionClientId.query.filter_by(submission_id=submission.id).first().client_submission_id == client_id
    assert (
        SubmissionClientId.query.filter_by(submission_id=shadow_submission.id).first().client_submission_id == client_id
    )


def test_bulk_add_submission_client_ids(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            organization_id=1,
            email="<EMAIL>",
            is_being_impersonated=False,
            has_submission_permission=always_has_permission,
        ),
    )

    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture()

    client_id_1 = "12345"
    client_id_2 = "54321"
    body = [
        {"client_submission_id": client_id_1, "submission_id": str(submission.id)},
        {"client_submission_id": client_id_2, "submission_id": str(submission.id)},
    ]
    bulk_add_client_submission_ids(str(submission.id), body)

    added_client_ids = SubmissionClientId.query.filter_by(submission_id=submission.id).all()
    assert len(added_client_ids) == 2
    assert client_id_1 in [client_id.client_submission_id for client_id in added_client_ids]
    assert client_id_2 in [client_id.client_submission_id for client_id in added_client_ids]


def test_bulk_add_submission_client_ids_for_paragon_wc(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            organization_id=1,
            email="<EMAIL>",
            is_being_impersonated=False,
            has_submission_permission=always_has_permission,
        ),
    )
    paragon_ims_client_mock = MagicMock()
    mocker.patch(
        "copilot.v3.controllers.submissions.get_logged_in_paragon_ims_client", return_value=paragon_ims_client_mock
    )

    organization_fixture(id=ExistingOrganizations.Paragon.value)
    user_fixture(organization_id=ExistingOrganizations.Paragon.value)
    email = email_fixture(email_account=PARAGON_WC_EMAIL)
    db.session.commit()
    report, submission = report_and_submission_fixture(
        organization_id=ExistingOrganizations.Paragon.value, correspondence_id=email.correspondence_id
    )
    wc_cov = coverage_fixture(
        name=Coverage.ExistingNames.WorkersComp, organization_id=ExistingOrganizations.Paragon.value
    )
    submission_coverage = submission_coverage_fixture(
        submission_id=submission.id, coverage_id=wc_cov.id, coverage_type=CoverageType.PRIMARY
    )
    submission.coverages.append(submission_coverage)
    db.session.commit()

    client_id_1 = "12345"
    body = [
        {"client_submission_id": client_id_1, "submission_id": str(submission.id)},
    ]

    paragon_ims_client_mock.get_submission_information.side_effect = [
        SubmissionInformation(lob=ImsCoverage.WORKERS_COMPENSATION)
    ]
    bulk_add_client_submission_ids(str(submission.id), body)

    added_client_ids = SubmissionClientId.query.filter_by(submission_id=submission.id).all()
    assert len(added_client_ids) == 1
    assert client_id_1 in [client_id.client_submission_id for client_id in added_client_ids]


def test_bulk_add_submission_client_ids_for_paragon_es_with_primary_and_excess(app_context, mocker):
    setup_user_and_request(mocker)
    mocker.patch("flask.request", MagicMock(path="/v3/reports/bulk_add_submission_client_ids"))

    paragon_ims_client_mock = MagicMock()
    mocker.patch(
        "copilot.v3.controllers.submissions.get_logged_in_paragon_ims_client", return_value=paragon_ims_client_mock
    )

    organization_fixture(id=ExistingOrganizations.Paragon.value)
    user_fixture(organization_id=ExistingOrganizations.Paragon.value)
    report, submission = report_and_submission_fixture(
        name="Sub Name", organization_id=ExistingOrganizations.Paragon.value
    )
    report.correspondence = ReportEmailCorrespondence(email_account="<EMAIL>")
    paragon_cov = coverage_fixture(
        name=Coverage.ExistingNames.Liability, organization_id=ExistingOrganizations.Paragon.value
    )
    submission_gl_coverage = submission_coverage_fixture(
        submission_id=submission.id, coverage_id=paragon_cov.id, coverage_type=CoverageType.PRIMARY
    )
    submission_xs_coverage = submission_coverage_fixture(
        submission_id=submission.id, coverage_id=paragon_cov.id, coverage_type=CoverageType.EXCESS
    )
    submission.coverages.append(submission_gl_coverage)
    submission.coverages.append(submission_xs_coverage)
    db.session.commit()

    client_id_1 = "12345"
    client_id_2 = "54321"
    body = [
        {"client_submission_id": client_id_1, "submission_id": str(submission.id)},
        {"client_submission_id": client_id_2, "submission_id": str(submission.id)},
    ]
    paragon_ims_client_mock.get_submission_information.side_effect = [
        SubmissionInformation(lob=ImsCoverage.CASUALTY_PRIMARY),
        SubmissionInformation(lob=ImsCoverage.CASUALTY_EXCESS_SUPPORTED),
    ]
    bulk_add_client_submission_ids(str(submission.id), body)

    gl_client_id = SubmissionClientId.query.filter_by(submission_id=submission.id).all()
    assert len(gl_client_id) == 1
    assert client_id_1 in [client_id.client_submission_id for client_id in gl_client_id]

    xs_client_id = SubmissionClientId.query.filter_by(client_submission_id="54321").all()
    assert len(xs_client_id) == 1


def test_transition_submission_to_declined_generates_a_note(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=user.id,
            has_submission_permission=always_has_permission,
            organization_id=1,
            email="<EMAIL>",
            is_internal_machine_user=False,
            is_machine_user=False,
            is_being_impersonated=False,
            applicable_settings=Settings(),
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    report, submission = report_and_submission_fixture(stage="ON_MY_PLATE")
    db.session.commit()

    update_submission(
        str(submission.id),
        {"stage": "DECLINED", "reason_for_declining": "No more mana"},
    )

    notes = get_submission_notes(submission.id)
    assert len(notes) == 1
    assert "No more mana" in notes[0]["text"]

    update_submission(
        str(submission.id),
        {"stage": "DECLINED", "reason_for_declining": "No more mana"},
    )

    # no new note because stage is the same
    notes_2 = get_submission_notes(submission.id)
    assert len(notes_2) == 1

    update_submission(
        str(submission.id),
        {
            "stage": "ON_MY_PLATE",
        },
    )
    update_submission(
        str(submission.id),
        {"stage": "DECLINED", "reason_for_declining": "No more health"},
    )

    # no new note because stage is the same
    notes_3 = get_submission_notes(submission.id)
    assert len(notes_3) == 2
    assert "No more mana" in notes_3[0]["text"]
    assert "No more health" in notes_3[1]["text"]


def test_transition_submission_to_declined_generates_a_note_with_reason(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=user.id,
            has_submission_permission=always_has_permission,
            organization_id=1,
            email="<EMAIL>",
            is_internal_machine_user=False,
            is_machine_user=False,
            is_being_impersonated=False,
            applicable_settings=Settings(),
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    report, submission = report_and_submission_fixture(stage="ON_MY_PLATE")
    db.session.commit()

    update_submission(
        str(submission.id),
        {
            "stage": "DECLINED",
        },
    )

    notes = get_submission_notes(submission.id)
    assert len(notes) == 1
    assert "not specified" in notes[0]["text"]


def test_transition_submission_to_lost_generates_a_note_with_reason(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=user.id,
            has_submission_permission=always_has_permission,
            organization_id=1,
            email="<EMAIL>",
            is_internal_machine_user=False,
            is_machine_user=False,
            is_being_impersonated=False,
            applicable_settings=Settings(),
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    report, submission = report_and_submission_fixture(stage="ON_MY_PLATE")
    db.session.commit()

    update_submission(
        str(submission.id),
        {"stage": "QUOTED_LOST", "lost_reasons": ["Hulk", "IronMan"]},
    )

    notes = get_submission_notes(submission.id)
    assert len(notes) == 1
    assert "Hulk" in notes[0]["text"]
    assert "IronMan" in notes[0]["text"]


def test_transition_submission_to_lost_generates_a_note_without_a_reason(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=user.id,
            has_submission_permission=always_has_permission,
            organization_id=1,
            email="<EMAIL>",
            is_internal_machine_user=False,
            is_machine_user=False,
            is_being_impersonated=False,
            applicable_settings=Settings(),
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    report, submission = report_and_submission_fixture(stage="ON_MY_PLATE")
    db.session.commit()

    update_submission(
        str(submission.id),
        {
            "stage": "QUOTED_LOST",
        },
    )

    notes = get_submission_notes(submission.id)
    assert len(notes) == 1
    assert "not specified" in notes[0]["text"]
    assert notes[0]["is_generated_note"] is True


def test_store_submission_level_extracted_data(app_context, mocker):
    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture(stage="ON_MY_PLATE")
    file = file_fixture(submission_id=submission.id)
    with open("tests/data/entity_mapping/email_with_entity.json") as email_with_entity:
        processed_data = json.load(email_with_entity)
    file.processed_file = ProcessedFile(processed_data=processed_data)
    db.session.commit()

    store_submission_level_extracted_data(submission.id, file.id)

    extracted_data = SubmissionLevelExtractedData.query.all()
    assert len(extracted_data) == 7
    assert all(d.submission_id == submission.id and d.file_id == file.id for d in extracted_data)
    assert (
        next(json.loads(d.value) for d in extracted_data if d.field == EntityInformation.BROKER_NAME) == "LINDSAY HALL"
    )
    assert (
        next(json.loads(d.value) for d in extracted_data if d.field == EntityInformation.DESCRIPTION)
        == "MAE offers geotechnical engineering, hydrogeology, environmental consulting, construction materials"
        " testing, and engineering inspection services, aiding projects from start to finish."
    )
    assert next(json.loads(d.value) for d in extracted_data if d.field == EntityInformation.IS_PROJECT) is False
    assert next(json.loads(d.value) for d in extracted_data if d.field == EntityInformation.COVERAGES) == [
        ["liability", "excess"]
    ]


def test_create_or_replace_submission_level_extracted_data(app_context, mocker):
    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture(stage="ON_MY_PLATE")
    file = file_fixture(submission_id=submission.id)
    db.session.commit()

    response_1, _ = create_or_replace_submission_level_extracted_data(
        {
            "submission_id": submission.id,
            "file_id": file.id,
            "field": EntityInformation.BROKER_NAME,
            "value": '"FILE_CONTENT_BROKER_NAME"',
            "source_details": SourceDetails.FILE_CONTENT,
        }
    )

    assert response_1["id"] is not None

    response_2, _ = create_or_replace_submission_level_extracted_data(
        {
            "submission_id": submission.id,
            "file_id": file.id,
            "field": EntityInformation.BROKER_NAME,
            "value": '"GENERATED_BROKER_NAME"',
            "source_details": SourceDetails.GENERATED,
            "parent_id": response_1["id"],
        }
    )

    assert response_2["id"] is not None

    extracted_data = SubmissionLevelExtractedData.query.all()
    assert len(extracted_data) == 2
    assert (
        next(json.loads(d.value) for d in extracted_data if d.source_details == SourceDetails.GENERATED)
        == "GENERATED_BROKER_NAME"
    )
    assert (
        next(json.loads(d.value) for d in extracted_data if d.source_details == SourceDetails.FILE_CONTENT)
        == "FILE_CONTENT_BROKER_NAME"
    )
    assert (
        next(str(d.parent_id) for d in extracted_data if d.source_details == SourceDetails.GENERATED)
        == response_1["id"]
    )


def test_create_or_replace_submission_level_extracted_data_existing(app_context, mocker):
    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture(stage="ON_MY_PLATE")
    file = file_fixture(submission_id=submission.id)
    with open("tests/data/entity_mapping/email_with_entity.json") as email_with_entity:
        processed_data = json.load(email_with_entity)
    file.processed_file = ProcessedFile(processed_data=processed_data)
    db.session.commit()

    store_submission_level_extracted_data(submission.id, file.id)
    create_or_replace_submission_level_extracted_data(
        {
            "submission_id": submission.id,
            "file_id": file.id,
            "field": EntityInformation.BROKER_NAME,
            "value": '"NEW BROKER NAME"',
            "source_details": SourceDetails.FILE_CONTENT_RAW,
        }
    )

    extracted_data = SubmissionLevelExtractedData.query.all()
    assert len(extracted_data) == 7
    assert all(d.submission_id == submission.id and d.file_id == file.id for d in extracted_data)
    assert (
        next(json.loads(d.value) for d in extracted_data if d.field == EntityInformation.BROKER_NAME)
        == "NEW BROKER NAME"
    )
    assert (
        next(json.loads(d.value) for d in extracted_data if d.field == EntityInformation.DESCRIPTION)
        == "MAE offers geotechnical engineering, hydrogeology, environmental consulting, construction materials"
        " testing, and engineering inspection services, aiding projects from start to finish."
    )
    assert next(json.loads(d.value) for d in extracted_data if d.field == EntityInformation.IS_PROJECT) is False
    assert next(json.loads(d.value) for d in extracted_data if d.field == EntityInformation.COVERAGES) == [
        ["liability", "excess"]
    ]


def test_send_submission_email_should_ignore_non_first_rule(app_context, mocker):
    mock: MagicMock = mocker.patch(
        "copilot.notifications.submission_notification_handler_v2.NotificationHandlerV2.send_submission_notification"
    )
    organization_fixture()
    user = user_fixture()
    report, submission = report_and_submission_fixture(stage="ON_MY_PLATE")

    report.owner_id = user.id
    submission.owner_id = user.id
    submission.sent_rule_email = "Some rule"

    db.session.commit()

    body, code = send_submission_email(
        {
            "submission_id": submission.id,
            "user_id": user.id,
            "rule_name": "non_first_rule",
            "template_id": str(uuid4()),
        }
    )

    assert body is None
    assert code == 204
    mock.assert_not_called()


def test_send_submission_email_cc_emails(app_context, mocker):
    notifications_handler_mock = MagicMock()
    notifications_handler_mock.send_submission_notification.return_value = SentEmail()
    app_context.notifications_handler_v2 = notifications_handler_mock
    flask.current_app.notifications_handler_v2 = notifications_handler_mock

    organization_fixture()
    user = user_fixture()

    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=user.id,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=lambda x, y: True,
            is_being_impersonated=False,
        ),
    )

    template_id = str(uuid4())

    report, submission = report_and_submission_fixture(stage="ON_MY_PLATE")

    report.correspondence = report_email_correspondence_fixture(email_cc=["<EMAIL>"])
    report.owner_id = user.id
    submission.owner_id = user.id
    submission.sent_rule_email = "Some rule"

    db.session.commit()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    # Assert report correspondence email_cc is taken by default
    send_submission_email(
        {
            "submission_id": submission.id,
            "user_id": user.id,
            "template_id": template_id,
        }
    )

    notifications_handler_mock.send_submission_notification.assert_called_once()
    last_call = notifications_handler_mock.send_submission_notification.call_args

    assert "cc_emails" in last_call.kwargs
    assert last_call.kwargs["cc_emails"] == ["<EMAIL>"]

    notifications_handler_mock.send_submission_notification.reset_mock()

    # Assert body override cc_email is taken if present
    send_submission_email(
        {
            "submission_id": submission.id,
            "user_id": user.id,
            "template_id": template_id,
            "cc_emails": ["<EMAIL>"],
        }
    )

    notifications_handler_mock.send_submission_notification.assert_called_once()
    last_call = notifications_handler_mock.send_submission_notification.call_args

    assert "cc_emails" in last_call.kwargs
    assert last_call.kwargs["cc_emails"] == ["<EMAIL>"]


@pytest.mark.parametrize(
    ["from_stage", "to_stage", "is_allowed"],
    [
        [SubmissionStage.ON_MY_PLATE, SubmissionStage.QUOTED, True],
        [SubmissionStage.ON_MY_PLATE, SubmissionStage.QUOTED_LOST, False],
        [SubmissionStage.ON_MY_PLATE, SubmissionStage.QUOTED_BOUND, False],
        [SubmissionStage.QUOTED, SubmissionStage.QUOTED_BOUND, True],
        [SubmissionStage.QUOTED, SubmissionStage.QUOTED, True],
        [SubmissionStage.QUOTED, SubmissionStage.QUOTED_LOST, True],
        [SubmissionStage.QUOTED, SubmissionStage.ON_MY_PLATE, False],
        [SubmissionStage.QUOTED_LOST, SubmissionStage.ON_MY_PLATE, False],
        [SubmissionStage.QUOTED_LOST, SubmissionStage.QUOTED_BOUND, False],
        [SubmissionStage.CLEARING_ISSUE, SubmissionStage.QUOTED_BOUND, True],
        [SubmissionStage.CLEARING_ISSUE, SubmissionStage.QUOTED_LOST, True],
        [SubmissionStage.CLEARING_ISSUE, SubmissionStage.ON_MY_PLATE, True],
        [SubmissionStage.CLEARING_ISSUE, SubmissionStage.INDICATED, True],
    ],
)
def test_submission_transition_restrictions(app_context, mocker, from_stage, to_stage, is_allowed):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=123,
            is_internal_machine_user=True,
            organization_id=10,
            email="<EMAIL>",
            is_being_impersonated=False,
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": False})

    organization_fixture(id=10)
    user = user_fixture(organization_id=10)
    settings_fixture(
        organization_id=10,
        allowed_submission_stages=[
            "INDICATED",
            "ON_MY_PLATE",
            "WAITING_FOR_OTHERS",
            "QUOTED",
            "DECLINED",
            "QUOTED_LOST",
            "QUOTED_BOUND",
            "EXPIRED",
        ],
        transitions={
            "ON_MY_PLATE": {"blacklist": ["QUOTED_LOST", "QUOTED_BOUND"]},
            "WAITING_FOR_OTHERS": {"blacklist": ["QUOTED_LOST", "QUOTED_BOUND"]},
            "EXPIRED": {"blacklist": ["QUOTED_LOST", "QUOTED_BOUND"]},
            "INDICATED": {"whitelist": ["QUOTED_LOST", "QUOTED"]},
            "QUOTED": {"whitelist": ["QUOTED_LOST", "QUOTED_BOUND"]},
            "QUOTED_BOUND": {"whitelist": []},
            "QUOTED_LOST": {"whitelist": []},
            "DECLINED": {"whitelist": []},
            "CLEARING_ISSUE": {"blacklist": []},
        },
    )
    report = report_fixture(owner_id=user.id, tier=None, organization_id=10)
    submission = submission_fixture(owner_id=user.id, report=report, stage=from_stage)
    db.session.commit()

    if is_allowed:
        assert (
            update_submission(
                str(submission.id),
                {"stage": to_stage},
            )
            is not None
        )
    else:
        with pytest.raises(InternalError, match=f"You cannot move from {from_stage} to {to_stage}"):
            update_submission(str(submission.id), {"stage": to_stage})


def test_get_submission_shareholders(app_context):
    organization_fixture()
    user_fixture()

    _, submission = report_and_submission_fixture()
    sub_bus = submission_business_fixture(
        business_id=UUID("691451a6-9b13-4190-932f-f28216a79e3c"),
        submission_id=submission.id,
    )
    db.session.commit()

    file = file_fixture(
        submission_id=submission.id,
        file_id=uuid4(),
        file_type=FileType.SUPPLEMENTAL_FORM,
    )
    file2 = file_fixture(
        submission_id=submission.id,
        file_id=uuid4(),
        file_type=FileType.LOSS_RUN,
    )
    db.session.commit()

    shareholder = shareholder_fixture(
        submission_id=submission.id,
        file_id=file.id,
        shareholder_id=uuid4(),
        shareholder_name="test shareholder name",
        ownership_percentage="10",
        is_director_or_board_member=True,
        submission_business_id=sub_bus.id,
    )
    shareholder2 = shareholder_fixture(
        submission_id=submission.id,
        file_id=file2.id,
        shareholder_id=uuid4(),
        shareholder_name="test shareholder name2",
        ownership_percentage="30",
        is_director_or_board_member=False,
    )
    db.session.commit()

    shareholders_response = get_submission_shareholders(submission.id)
    assert len(shareholders_response) == 1
    assert shareholders_response[0]["shareholder_name"] == "test shareholder name"
    assert shareholders_response[0]["ownership_percentage"] == 10.0
    assert shareholders_response[0]["is_director_or_board_member"] == True
    assert shareholders_response[0]["entity_id"] == str(sub_bus.business_id)


def test_get_submissions_by_clients_ids_deduplicates_submissions(app_context, current_user_check_bypass):
    org = organization_fixture()
    user_fixture()

    report, submission = report_and_submission_fixture()
    report.organization_id = org.id
    db.session.commit()

    client_id = "12345"
    add_client_submission_id(
        str(submission.id), {"client_submission_id": client_id, "submission_id": str(submission.id)}
    )

    client_id = "54321"
    add_client_submission_id(
        str(submission.id), {"client_submission_id": client_id, "submission_id": str(submission.id)}
    )

    db.session.commit()

    submissions = get_submissions_by_client_ids(["12345", "54321"], org.id)

    assert len(submissions) == 1
    assert submissions[0]["id"] == str(submission.id)
    assert submissions[0]["created_at"] is not None

    api_client_submission_ids = submissions[0]["client_submission_ids"]
    assert len(api_client_submission_ids) == 2

    client_submission_ids = {si["client_submission_id"] for si in api_client_submission_ids}
    assert client_submission_ids == {"12345", "54321"}


def test_get_submissions_by_client_ids_ignores_deleted_submissions(app_context, current_user_check_bypass):
    org = organization_fixture()
    user_fixture()

    report, submission = report_and_submission_fixture()
    report.organization_id = org.id
    db.session.commit()

    fni_business = submission_business_fixture(
        named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED, submission_id=submission.id
    )
    submission_business_fixture(submission_id=submission.id)

    client_id = "12345"
    add_client_submission_id(
        str(submission.id), {"client_submission_id": client_id, "submission_id": str(submission.id)}
    )

    db.session.commit()

    report2, submission2 = report_and_submission_fixture()
    report2.organization_id = org.id

    second_client_id = "ABC123"
    add_client_submission_id(
        str(submission2.id), {"client_submission_id": second_client_id, "submission_id": str(submission2.id)}
    )
    submission2.is_deleted = True
    db.session.commit()

    submissions = get_submissions_by_client_ids([client_id, second_client_id], org.id)

    assert len(submissions) == 1
    assert submissions[0]["id"] == str(submission.id)
    assert submissions[0]["created_at"] is not None
    assert len(submission.businesses) == 1
    assert submission.businesses[0].named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED
    assert submission.businesses[0].id == fni_business.id


def test_get_submissions_by_client_ids_ignores_submissions_from_other_orgs(app_context, current_user_check_bypass):
    org = organization_fixture()
    org2 = organization_fixture(id=2)
    user_fixture()

    report, submission = report_and_submission_fixture()
    report.organization_id = org.id
    db.session.commit()

    client_id = "12345"
    add_client_submission_id(
        str(submission.id), {"client_submission_id": client_id, "submission_id": str(submission.id)}
    )

    db.session.commit()

    report2, submission2 = report_and_submission_fixture()
    report2.organization_id = org2.id

    second_client_id = "ABC123"
    add_client_submission_id(
        str(submission2.id), {"client_submission_id": second_client_id, "submission_id": str(submission2.id)}
    )
    db.session.commit()

    submissions = get_submissions_by_client_ids([client_id, second_client_id], org.id)

    assert len(submissions) == 1
    assert submissions[0]["id"] == str(submission.id)
    assert submissions[0]["created_at"] is not None


def test_get_submissions_by_client_ids_handles_multiple_submissions(app_context, current_user_check_bypass):
    org = organization_fixture()
    user_fixture()

    report, submission = report_and_submission_fixture()
    report.organization_id = org.id
    db.session.commit()

    client_id = "12345"
    add_client_submission_id(
        str(submission.id), {"client_submission_id": client_id, "submission_id": str(submission.id)}
    )

    report2, submission2 = report_and_submission_fixture()
    report2.organization_id = org.id

    second_client_id = "ABC123"
    add_client_submission_id(
        str(submission2.id), {"client_submission_id": second_client_id, "submission_id": str(submission2.id)}
    )
    db.session.commit()

    submissions = get_submissions_by_client_ids([client_id, second_client_id], org.id)

    assert len(submissions) == 2
    assert {s["id"] for s in submissions} == {str(submission.id), str(submission2.id)}

    verified_submissions = get_submissions_by_client_ids([client_id, second_client_id], org.id, verified_only=True)
    assert len(verified_submissions) == 0

    submission.is_verified = True
    db.session.commit()

    verified_submissions = get_submissions_by_client_ids([client_id, second_client_id], org.id, verified_only=True)
    assert len(verified_submissions) == 1
    assert verified_submissions[0]["id"] == str(submission.id)


def test_add_client_submission_id_is_normalized(app_context, current_user_check_bypass):
    org = organization_fixture(id=ExistingOrganizations.AdmiralInsuranceGroup.value)
    user_fixture(organization_id=org.id)
    report, submission = report_and_submission_fixture(organization_id=org.id)
    db.session.commit()

    client_id = "12 345 "
    add_client_submission_id(
        str(submission.id), {"client_submission_id": client_id, "submission_id": str(submission.id)}
    )

    assert submission.client_submission_ids[0].client_submission_id == "12345"


def test_get_submission_shadow_info(app_context, request_context, mocker, current_user_check_bypass):
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], True, False, []))

    # Setup base data
    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture()
    db.session.commit()

    # Test 1: No shadow dependencies
    shadow_info = get_submission_shadow_info(str(submission.id))
    assert shadow_info["shadowed_by_submission"] is None

    # Test 2: With shadow submission
    shadow_submission = shadow_submission_fixture(submission, with_files=False)
    shadow_submission.processing_state = SubmissionProcessingState.COMPLETED

    verify({}, str(shadow_submission.id), force_verify=True, manual=False, force=False)
    verify({}, str(shadow_submission.id), force_verify=True, manual=False, force=False)
    db.session.commit()

    shadow_info = get_submission_shadow_info(str(submission.id))
    shadowed_by_submission_info = shadow_info["shadowed_by_submission"]
    assert shadowed_by_submission_info["submission_id"] == str(shadow_submission.id)

    # Test 3: With deleted shadow submission
    shadow_submission.is_deleted = True
    db.session.commit()

    shadow_info = get_submission_shadow_info(str(submission.id))
    assert shadow_info["shadowed_by_submission"] is None

    # Test 4: With multiple shadow submissions (should return only one)
    shadow_submission.is_deleted = False
    another_shadow_submission = shadow_submission_fixture(shadow_submission, with_files=False)
    another_shadow_submission.processing_state = SubmissionProcessingState.COMPLETED
    verify({}, str(another_shadow_submission.id), force_verify=True, manual=False, force=False)
    verify({}, str(another_shadow_submission.id), force_verify=True, manual=False, force=False)
    db.session.commit()

    shadow_info = get_submission_shadow_info(str(submission.id))
    shadowed_by_submission_info = shadow_info["shadowed_by_submission"]
    assert shadowed_by_submission_info is not None
    assert shadowed_by_submission_info["submission_id"] == str(another_shadow_submission.id)


def test_get_active_organizational_submissions(app_context, request_context, current_user_check_bypass):
    organization1 = organization_fixture(id=1)
    organization2 = organization_fixture(id=2)

    user_fixture()
    user_fixture(organization_id=2, user_id=2)

    # Not verified for org 1
    report_a_1, submission_a_1 = report_and_submission_fixture(
        is_verified=False, organization_id=1, stage=SubmissionStage.ON_MY_PLATE
    )

    # Verified, but terminal stage
    report_a_2, submission_a_2 = report_and_submission_fixture(
        is_verified=True, organization_id=1, stage=SubmissionStage.QUOTED_BOUND
    )

    # Verified, not terminal stage
    report_a_3, submission_a_3 = report_and_submission_fixture(
        is_verified=True, organization_id=1, stage=SubmissionStage.ON_MY_PLATE
    )

    # Verified shell, not terminal stage
    report_a_4, submission_a_4 = report_and_submission_fixture(
        is_verified_shell=True, organization_id=1, stage=SubmissionStage.ON_MY_PLATE
    )

    report_b_1, submission_b_1 = report_and_submission_fixture(
        is_verified=True, organization_id=2, stage=SubmissionStage.ON_MY_PLATE, owner_id=2
    )

    report_b_2, submission_b_2 = report_and_submission_fixture(
        is_verified_shell=True, organization_id=2, stage=SubmissionStage.ON_MY_PLATE, owner_id=2
    )

    db.session.commit()

    active_info = get_active_organizational_submissions(1)
    active_submissions = active_info["active_submissions"]

    assert len(active_submissions) == 2
    assert {str(s["submission_id"]) for s in active_submissions} == {str(submission_a_3.id), str(submission_a_4.id)}


def test_delete_client_submission_id(app_context, current_user_check_bypass):
    admiral_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=admiral_id)
    user_fixture(organization_id=admiral_id)
    client_submission_id = "CLIENT_SUB_ID"

    report, submission = report_and_submission_fixture(organization_id=admiral_id)
    sync = submission_sync_fixture(
        policy_number=client_submission_id,
        organization_id=admiral_id,
        submission_name="submission number one",
        applied=True,
    )
    add_client_submission_id(
        str(submission.id), {"client_submission_id": client_submission_id, "submission_id": str(submission.id)}
    )
    db.session.commit()

    assert sync.applied is True
    delete_client_submission_id(str(submission.id), client_submission_id)
    assert sync.applied is False


def test_delete_client_submission_id_doesnt_set_sync_to_false_if_identifier_exists(
    app_context, current_user_check_bypass
):
    admiral_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=admiral_id)
    user_fixture(organization_id=admiral_id)
    client_submission_id = "CLIENT_SUB_ID"
    policy_number = "12345"

    report, submission = report_and_submission_fixture(organization_id=admiral_id)
    sync = submission_sync_fixture(
        policy_number=client_submission_id,
        organization_id=admiral_id,
        submission_name="submission number one",
        applied=True,
    )
    add_client_submission_id(
        str(submission.id), {"client_submission_id": client_submission_id, "submission_id": str(submission.id)}
    )
    set_submission_identifiers(str(submission.id), {"policy_number": policy_number})
    db.session.commit()

    assert sync.applied is True
    delete_client_submission_id(str(submission.id), client_submission_id)
    assert sync.applied is True


def test_set_submission_identifiers_for_conifer(app_context, current_user_check_bypass):
    # scenario for Bishop Conifer where we update identifiers from many to a single one
    conifer_id = ExistingOrganizations.BishopConifer.value
    organization_fixture(id=conifer_id)
    user_fixture(organization_id=conifer_id)
    client_submission_id = "grouped_id"
    child_sync_id = "child_sync_id"

    report, submission = report_and_submission_fixture(organization_id=conifer_id)
    sync = submission_sync_fixture(
        policy_number=client_submission_id,
        organization_id=conifer_id,
        submission_name="submission number one",
        applied=True,
    )
    submission_sync_identifiers_fixture(quote_numbers=["123", "456"], submission_sync_id=sync.id)
    child_sync = submission_sync_fixture(
        policy_number=child_sync_id,
        organization_id=conifer_id,
        submission_name="submission number one",
        applied=True,
        parent_client_id=client_submission_id,
    )
    submission_sync_identifiers_fixture(quote_numbers=["123"], submission_sync_id=child_sync.id)
    submission_client_id_fixture(client_submission_id=client_submission_id, submission_id=submission.id, source="SYNC")
    db.session.commit()

    set_submission_identifiers(str(submission.id), {"quote_number": "123"})
    db.session.refresh(submission)
    db.session.refresh(child_sync)

    assert child_sync.applied is False
    assert submission.first_client_submission_id == child_sync_id


def test_set_submission_identifiers(app_context, current_user_check_bypass):
    admiral_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=admiral_id)
    user_fixture(organization_id=admiral_id)

    report, submission = report_and_submission_fixture(organization_id=admiral_id)
    db.session.commit()

    set_submission_identifiers(str(submission.id), {"policy_number": "12345-01"})

    submission = Submission.query.get(submission.id)
    assert submission.is_renewal is False

    set_submission_identifiers(str(submission.id), {"policy_number": "12345"})
    submission = Submission.query.get(submission.id)
    assert submission.is_renewal is True


def test_set_submission_identifiers_removing_policy_number_removes_sync(app_context, current_user_check_bypass):
    admiral_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=admiral_id)
    user_fixture(organization_id=admiral_id)
    policy_number = "12345"

    report, submission = report_and_submission_fixture(organization_id=admiral_id)
    sync = submission_sync_fixture(
        organization_id=admiral_id,
        submission_name="submission number one",
        applied=True,
    )
    submission_sync_identifiers_fixture(submission_sync_id=sync.id, policy_numbers=[policy_number])
    set_submission_identifiers(str(submission.id), {"policy_number": policy_number})
    db.session.commit()

    assert sync.applied is True
    set_submission_identifiers(str(submission.id), {"policy_number": None})
    assert sync.applied is False


def test_set_submission_identifiers_removing_policy_number_does_not_remove_sync_if_submission_number_exists(
    app_context, current_user_check_bypass
):
    admiral_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=admiral_id)
    user_fixture(organization_id=admiral_id)
    client_submission_id = "CLIENT_SUB_ID"
    policy_number = "12345"

    report, submission = report_and_submission_fixture(organization_id=admiral_id)
    sync = submission_sync_fixture(
        policy_number=client_submission_id,
        organization_id=admiral_id,
        submission_name="submission number one",
        applied=True,
    )
    submission_sync_identifiers_fixture(submission_sync_id=sync.id, policy_numbers=[policy_number])
    submission_client_id_fixture(submission_id=submission.id, client_submission_id=client_submission_id, source="SYNC")
    set_submission_identifiers(str(submission.id), {"policy_number": policy_number})
    db.session.commit()

    assert sync.applied is True
    set_submission_identifiers(str(submission.id), {"policy_number": None})
    assert sync.applied is True


def test_set_submission_identifiers_set_quote_for_bishop(app_context, current_user_check_bypass):
    org_id = ExistingOrganizations.BishopConifer.value
    organization_fixture(id=org_id)
    user_fixture(organization_id=org_id)

    report, submission = report_and_submission_fixture(organization_id=org_id)
    report_shell, submission_shell = report_and_submission_fixture(organization_id=org_id)
    submission_shell.origin = "SYNC"
    submission_shell.created_at = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    submission_client_id_fixture(submission_id=submission_shell.id, client_submission_id="foo", source="SYNC")
    submission_sync_1 = submission_sync_fixture(policy_number="foo", organization_id=org_id, applied=True)
    submission_sync_2 = submission_sync_fixture(policy_number="foobar", organization_id=org_id, applied=True)
    submission_sync_3 = submission_sync_fixture(policy_number="foobar2", organization_id=org_id, applied=True)
    submission_sync_identifiers_fixture(submission_sync_id=submission_sync_2.id, quote_numbers=["123", "456"])
    submission_sync_identifiers_fixture(submission_sync_id=submission_sync_3.id, quote_numbers=["456"])
    db.session.commit()

    set_submission_identifiers(str(submission_shell.id), {"quote_number": "123"})
    assert submission_shell.quote_number == "123"
    assert submission.quote_number is None

    # if yes: remove existing quote number, move existing client_ids, remove shell,
    #         reset rows in sync (based on client id and quote number)

    set_submission_identifiers(str(submission.id), {"quote_number": "123"})
    # Shell data removed
    assert submission_shell.quote_number is None
    assert submission_shell.is_deleted is True
    assert report_shell.is_deleted is True
    assert submission_shell.client_submission_ids == []
    # Submission updated
    assert submission.quote_number == "123"
    assert submission.client_submission_ids[0].source == "MANUAL"
    assert submission.client_submission_ids[0].client_submission_id == "foo"
    # Sync reset
    assert submission_sync_1.applied is False
    assert submission_sync_2.applied is False
    assert submission_sync_3.applied is True


def test_get_submission_agent_codes(app_context, current_user_check_bypass):
    org = organization_fixture(id=ExistingOrganizations.BishopConifer.value)
    user = user_fixture(organization_id=org.id)

    brokerage = brokerage_fixture()
    brokerage.organizations.append(org)
    second_brokerage = brokerage_fixture(name="Another Brokerage")
    second_brokerage.organizations.append(org)
    broker = broker_fixture(brokerage_id=brokerage.id, aliases=["<EMAIL>"])
    broker.organizations.append(org)
    report, submission = report_and_submission_fixture(organization_id=org.id)
    db.session.commit()
    db.session.refresh(submission)
    submission.brokerage = brokerage
    submission.broker = broker
    submission_user_fixture(submission_id=submission.id, user_id=user.id)
    broker_agent_code = "12345"
    broker_agency_code = "54321"
    broker_client_code_fixture(
        agent_email="<EMAIL>",
        agent_code=broker_agent_code,
        agency_code=broker_agency_code,
        organization_id=org.id,
    )
    brokerage_agent_code = "22222"
    brokerage_agency_code = "33333"
    brokerage_client_code_fixture(
        brokerage_id=brokerage.id,
        agency_name="Brokerage agency",
        agent_name="Brokerage agent",
        agent_code=brokerage_agent_code,
        agency_code=brokerage_agency_code,
        organization_id=org.id,
    )
    db.session.commit()
    submission_dict = get_submission_by_id(str(submission.id), expand=["agent_code", "agency_code"])

    assert submission_dict["agent_code"] == broker_agent_code
    assert submission_dict["agency_code"] == broker_agency_code

    submission.broker = None
    db.session.commit()

    submission_dict = get_submission_by_id(str(submission.id), expand=["agent_code", "agency_code"])

    assert submission_dict["agent_code"] == brokerage_agent_code
    assert submission_dict["agency_code"] == brokerage_agency_code

    submission.brokerage = second_brokerage
    db.session.commit()

    submission_dict = get_submission_by_id(str(submission.id), expand=["agent_code", "agency_code"])
    assert submission_dict["agent_code"] is None
    assert submission_dict["agency_code"] is None


@patch("copilot.logic.paragon.get_logged_in_paragon_ims_client")
@patch.object(IMSSearchClient, "search_for_entity")
@patch("copilot.logic.paragon.get_clearance_blocker_info")
def test_execute_paragon_clearance_submission_action(
    get_clearance_blocker_info_mock,
    get_logged_in_paragon_ims_client_mock,
    ims_search_client_mock,
    app_context,
    mocker,
    current_user_check_bypass,
):
    organization_fixture()
    user_fixture()

    mocker.patch(
        "flask.current_app.ers_client_v3.get_entity_if_exists",
        return_value=MagicMock(
            names=[
                MagicMock(value="FIRST_NAMED_INSURED"),
                MagicMock(value="SECOND_NAMED_INSURED"),
            ],
            premises=[
                MagicMock(address="123 Main St, Anytown, USA 12345"),
            ],
        ),
    )

    search_result = MagicMock()
    search_result.picked_ims_guid = "abc"
    ims_search_client_mock.return_value = search_result

    def _check_if_is_cleared_in_paragon_ims(*args, **kwargs):
        paragon_coverage = kwargs["paragon_coverage"]
        if paragon_coverage == ImsCoverage.CASUALTY_EXCESS_SUPPORTED:
            return [
                ClearanceInfo(
                    control_no=123456789,
                    line_name="Przemek Superior Insurance",
                    quote_status_id=21,
                    quote_status_reason_id=37,
                )
            ]
        return None

    get_clearance_blocker_info_mock.side_effect = _check_if_is_cleared_in_paragon_ims

    report, submission = report_and_submission_fixture()
    submission_business = submission_business_fixture(
        submission_id=submission.id,
        business_id=str(uuid4()),
        named_insured="FIRST_NAMED_INSURED",
        requested_address="123 Main St, Anytown, USA 12345",
    )
    file = file_fixture(submission_id=submission.id)
    db.session.flush()

    processed_file = processed_file_fixture(
        file_id=file.id,
        submission_id=submission.id,
        processed_data={
            "fein": "123456789",
        },
    )

    db.session.commit()

    request_body = {
        "submission_action_type": "PARAGON_COVERAGE_CLEARANCE_CHECK",
        "coverages": ["Casualty E&S Excess (Supported)", "Casualty E&S Excess (Unsupported)"],
    }

    response, code = execute_submission_action(str(submission.id), request_body)
    assert code == 200

    coverage_statuses = response["coverage_statuses"]
    assert len(coverage_statuses) == 2

    coverage_name_to_status = {
        coverage_status["coverage_name"]: coverage_status for coverage_status in coverage_statuses
    }

    assert (
        coverage_name_to_status["Casualty E&S Excess (Supported)"]["coverage_name"] == "Casualty E&S Excess (Supported)"
    )
    assert coverage_name_to_status["Casualty E&S Excess (Supported)"]["blocked_by_submissions"] == [
        {
            "control_number": 123456789,
            "line_name": "Przemek Superior Insurance",
            "quote_status_id": 21,
            "quote_status_reason_id": 37,
        }
    ]

    assert (
        coverage_name_to_status["Casualty E&S Excess (Unsupported)"]["coverage_name"]
        == "Casualty E&S Excess (Unsupported)"
    )
    assert coverage_name_to_status["Casualty E&S Excess (Unsupported)"]["blocked_by_submissions"] is None


def test_get_identifier_suggestions(app_context, mocker, current_user_check_bypass):
    now = datetime.utcnow()

    organization = organization_fixture()
    user_fixture()

    _, submission1 = report_and_submission_fixture(name="submission number one", is_verified=True)
    _, submission2 = report_and_submission_fixture(name="submission number two", is_verified=True)
    _, submission3 = report_and_submission_fixture(name="submission number three", is_verified=True)

    sync1 = submission_sync_fixture(
        policy_number="sub1", organization_id=organization.id, submission_name="submission number one"
    )
    sync2 = submission_sync_fixture(
        policy_number="sub2", organization_id=organization.id, submission_name="submission number two"
    )
    sync3 = submission_sync_fixture(
        policy_number="sub3", organization_id=organization.id, submission_name="submission number three"
    )
    sync4 = submission_sync_fixture(
        policy_number="sub4", organization_id=organization.id, submission_name="submission number four"
    )

    submission_identifiers_suggestion_fixture(
        submission_id=submission1.id, created_at=now, submission_sync_id=sync1.id, confidence=1.0
    )
    submission_identifiers_suggestion_fixture(
        submission_id=submission1.id, created_at=now, submission_sync_id=sync2.id, confidence=0.9
    )
    submission_identifiers_suggestion_fixture(
        submission_id=submission1.id, created_at=(now - timedelta(days=60)), submission_sync_id=sync3.id, confidence=0.8
    )
    submission_identifiers_suggestion_fixture(
        submission_id=submission1.id,
        created_at=now,
        submission_sync_id=sync4.id,
        confidence=0.7,
    )

    submission_identifiers_suggestion_fixture(submission_id=submission2.id, submission_sync_id=sync2.id, confidence=1.0)
    submission_identifiers_suggestion_fixture(submission_id=submission2.id, submission_sync_id=sync3.id, confidence=0.9)
    submission_identifiers_suggestion_fixture(submission_id=submission2.id, submission_sync_id=sync4.id, confidence=0.8)

    submission_identifiers_suggestion_fixture(submission_id=submission3.id, submission_sync_id=sync3.id, confidence=1.0)
    submission_identifiers_suggestion_fixture(submission_id=submission3.id, submission_sync_id=sync4.id, confidence=0.7)

    submission_client_id_fixture(client_submission_id=sync2.policy_number, submission_id=submission2.id, source="SYNC")
    db.session.commit()

    suggestions_envelope = get_identifier_suggestions(str(submission1.id))
    suggestions = suggestions_envelope["suggestions"]

    assert len(suggestions) == 4
    assert suggestions[0]["client_id"] == "sub1"
    assert suggestions[1]["is_assigned"] is True

    suggestions_envelope = get_identifier_suggestions(str(submission1.id), (now - timedelta(days=10)).isoformat())
    suggestions = suggestions_envelope["suggestions"]

    assert len(suggestions) == 3
    assert suggestions[2]["client_id"] == "sub4"


def test_align_submissions_client_stages(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            organization_id=6,
            email="<EMAIL>",
            is_being_impersonated=False,
            id=6,
            has_submission_permission=lambda type, id: True,
            name="Test User",
        ),
    )

    organization_fixture(id=6)
    user_fixture(organization_id=6)

    client_config = client_submission_stage_config_fixture(organization_id=6, copilot_stage="QUOTED")
    db.session.commit()

    _, submission = report_and_submission_fixture(
        origin=Origin.API, is_verified_shell=True, client_stage_id=client_config.id, organization_id=6
    )
    _, submission2 = report_and_submission_fixture(origin=Origin.API, is_verified_shell=True, organization_id=6)
    db.session.commit()

    align_submissions_client_stages()

    db.session.refresh(submission)
    db.session.refresh(submission2)

    assert submission.stage == SubmissionStage.ON_MY_PLATE
    assert submission2.stage == SubmissionStage.ON_MY_PLATE

    submission.created_at = submission.created_at - timedelta(hours=23)
    db.session.add(submission)
    db.session.commit()

    align_submissions_client_stages()

    db.session.refresh(submission)
    assert submission.stage == SubmissionStage.QUOTED


def test_update_submission_respects_taxonomy_priority(app_context, mocker):
    feature_flags_client_mock = MagicMock()

    def variation_mock(*args, **kwargs):
        return True

    feature_flags_client_mock.variation = variation_mock
    FeatureFlagsClient._client = feature_flags_client_mock

    organization_fixture()
    machine_user_fixture = user_fixture(user_id=2137, email="<EMAIL>")
    real_user_fixture = user_fixture(email="<EMAIL>")

    report, submission = report_and_submission_fixture()
    submission.is_verified = True
    db.session.commit()

    with mocker.patch("flask_login.utils._get_user", return_value=machine_user_fixture):
        # Update submission as machine user
        update_submission(
            str(submission.id),
            {"primary_naics_code": "NAICS_532111"},
        )

        db.session.refresh(submission)

        assert submission.primary_naics_code == "NAICS_532111"
        assert len(submission.field_sources) == 1

        source = submission.field_sources[0]
        assert source.field_name == "primary_naics_code"
        assert source.source == "AUTO"

        # Should emit event to sync taxonomies
        app_context.event_service.handle_submission_event.assert_called_once_with(
            event=SubmissionEvent.SUBMISSION_TAXONOMY_UPDATED,
            submission=ANY,
            additional_data={"changed_taxonomies": {"changes": {"NAICS_CHANGED": {"new_value": "NAICS_532111"}}}},
        )

    app_context.event_service.handle_submission_event.reset_mock()

    with (
        app_context.test_request_context(
            headers={
                "X-Kalepa-Caller-Context": CallerContext.TAXONOMY_SYNC,
            }
        ),
        mocker.patch("flask_login.utils._get_user", return_value=real_user_fixture),
    ):
        # Update submission as taxonomy sync
        update_submission(
            str(submission.id),
            {"primary_naics_code": "NAICS_532112"},
        )

        db.session.refresh(submission)
        assert submission.primary_naics_code == "NAICS_532112"
        assert len(submission.field_sources) == 1

        source = submission.field_sources[0]
        assert source.field_name == "primary_naics_code"
        assert source.source == "TAXONOMY_SYNC"

        # Should not emit event
        app_context.event_service.handle_submission_event.assert_not_called()

    app_context.event_service.handle_submission_event.reset_mock()

    # Try to update submission as machine user -> shouldn't succeeded as it was already updated by taxonomy sync
    with mocker.patch("flask_login.utils._get_user", return_value=machine_user_fixture):
        update_submission(
            str(submission.id),
            {"primary_naics_code": "NAICS_532113"},
        )

        db.session.refresh(submission)
        assert submission.primary_naics_code == "NAICS_532112"
        assert len(submission.field_sources) == 1

        source = submission.field_sources[0]
        assert source.field_name == "primary_naics_code"
        assert source.source == "TAXONOMY_SYNC"

        # Should not emit event
        app_context.event_service.handle_submission_event.assert_not_called()

    app_context.event_service.handle_submission_event.reset_mock()

    # Try to update submission as normal user -> should succeed
    with mocker.patch("flask_login.utils._get_user", return_value=real_user_fixture):
        update_submission(
            str(submission.id),
            {"primary_naics_code": "NAICS_532114"},
        )

        db.session.refresh(submission)
        assert submission.primary_naics_code == "NAICS_532114"
        assert len(submission.field_sources) == 1

        source = submission.field_sources[0]
        assert source.field_name == "primary_naics_code"
        assert source.source == "MANUAL"

        # Should emit event
        app_context.event_service.handle_submission_event.assert_called_once_with(
            event=SubmissionEvent.SUBMISSION_TAXONOMY_UPDATED,
            submission=ANY,
            additional_data={"changed_taxonomies": {"changes": {"NAICS_CHANGED": {"new_value": "NAICS_532114"}}}},
        )


@with_feature_flag(FeatureType.USE_PRIORITY_FOR_TAXONOMIES)
def test_update_verified_shell_triggers_taxonomy_sync_only_if_manual_request(app_context, mocker):
    organization_fixture()
    real_user_fixture = user_fixture(email="<EMAIL>")
    machine_user_fixture = user_fixture(user_id=2137, email="<EMAIL>")
    report, submission = report_and_submission_fixture()
    submission.is_verified_shell = True
    db.session.commit()

    # Update submission as machine user
    with mocker.patch("flask_login.utils._get_user", return_value=machine_user_fixture):
        update_submission(
            str(submission.id),
            {"primary_naics_code": "NAICS_532111"},
        )
        db.session.refresh(submission)

        assert submission.primary_naics_code == "NAICS_532111"
        assert len(submission.field_sources) == 1
        source = submission.field_sources[0]
        assert source.field_name == "primary_naics_code"
        assert source.source == "AUTO"

        # Should not emit event
        app_context.event_service.handle_submission_event.assert_not_called()

    app_context.event_service.handle_submission_event.reset_mock()

    # Update submission as normal user
    with mocker.patch("flask_login.utils._get_user", return_value=real_user_fixture):
        update_submission(
            str(submission.id),
            {"primary_naics_code": "NAICS_532112"},
        )

        db.session.refresh(submission)

        assert submission.primary_naics_code == "NAICS_532112"
        assert len(submission.field_sources) == 1
        source = submission.field_sources[0]
        assert source.field_name == "primary_naics_code"
        assert source.source == "MANUAL"

        # Should emit event
        app_context.event_service.handle_submission_event.assert_called_once_with(
            event=SubmissionEvent.SUBMISSION_TAXONOMY_UPDATED,
            submission=ANY,
            additional_data={"changed_taxonomies": {"changes": {"NAICS_CHANGED": {"new_value": "NAICS_532112"}}}},
        )


def test_get_submission_level_extracted_data_by_report_id(app_context, mocker):
    organization_fixture()
    user_fixture()
    current_user = AnonObj(
        name="test_user",
        has_report_permission=lambda x, y: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    report, submission = report_and_submission_fixture()
    # TODO: create submission_level_extracted_data_fixture
    submission_level_extracted_data_fixture(
        report_id=report.id,
        submission_id=submission.id,
        field="test field 1",
        value="test value 1",
        generation_method="gpt",
    )

    extracted_data = get_submission_level_extracted_data_by_report_id(str(report.id))
    assert len(extracted_data) == 1
    assert extracted_data[0]["submission_id"] == str(submission.id)
    assert extracted_data[0]["field"] == "test field 1"
    assert extracted_data[0]["value"] == "test value 1"
    assert extracted_data[0]["generation_method"] == "gpt"
    assert extracted_data[0]["source_details"] == SourceDetails.FILE_CONTENT
