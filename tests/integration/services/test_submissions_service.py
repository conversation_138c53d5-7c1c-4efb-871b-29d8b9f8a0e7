import uuid

from sqlalchemy import func
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.submission_business import (
    SubmissionBusinessEntityNamedInsured,
    SubmissionBusinessEntityRole,
)
from static_common.enums.submission_processing_state import SubmissionProcessingState

from copilot.models import db
from copilot.services.submissions import SubmissionsService
from tests.integration.factories import (
    file_fixture,
    organization_fixture,
    processed_file_fixture,
    report_fixture,
    submission_fixture,
    submission_premises_fixture,
    user_fixture,
)
from tests.integration.utils import AnonObj, always_has_permission


def test_submissions_service_file_count_and_processed_data_size(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            has_submission_permission=always_has_permission,
            email="<EMAIL>",
            is_being_impersonated=False,
            is_internal_machine_user=True,
            cross_organization_access=False,
        ),
    )
    organization_fixture()
    user_fixture()
    report = report_fixture()
    submission = submission_fixture(processing_state=SubmissionProcessingState.BUSINESS_CONFIRMATION, report=report)
    file = file_fixture(
        submission_id=submission.id,
        processing_state=FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION,
        classification=ClassificationDocumentType.DIRECTORS_AND_OFFICERS,
    )

    simple_processed_data = {"hi": "world!"}
    processed_file_fixture(file_id=file.id, processed_data=simple_processed_data)
    db.session.commit()

    actual_file_count = SubmissionsService.get_file_count_fast(submission_id=submission.id)
    actual_processed_data_size = SubmissionsService.get_processed_data_size_fast(submission_id=submission.id)

    assert actual_file_count == 1
    assert actual_processed_data_size == 21
