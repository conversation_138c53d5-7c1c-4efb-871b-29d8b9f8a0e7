from datetime import datetime
import uuid

from copilot.models import db
from copilot.models.types import SubmissionPremisesType
from copilot.services.orgs.bowhead_service import BowheadService
from copilot.services.submissions import SubmissionsService
from tests.integration.factories import (
    organization_fixture,
    report_fixture,
    submission_fixture,
    submission_premises_fixture,
    user_fixture,
)


def test_get_matching_submission_premises(app_context, mocker):
    mock_fni_premises = mocker.MagicMock()
    mock_fni_premises.premises_id = uuid.UUID(int=0)
    mock_fni_premises.submission_premises_type = SubmissionPremisesType.FNI
    mock_fni_premises.created_at = datetime(2020, 1, 1)
    mock_fni_premises.named_insured = "ACME Construction Company"

    mock_submission = mocker.MagicMock()
    mock_submission.id = uuid.UUID(int=1)
    mock_submission.fni_premises = mock_fni_premises
    mock_submission.report.id = uuid.UUID(int=2)

    # Create real test records for matching premises in the database
    organization = organization_fixture()
    user_fixture()
    report1 = report_fixture()
    report2 = report_fixture()
    report3 = report_fixture()
    report4 = report_fixture()
    report5 = report_fixture()

    # Create 5 submissions with varying degrees of similarity
    sub1 = submission_fixture(report=report1)  # should match
    sub2 = submission_fixture(report=report2)  # should match
    sub3 = submission_fixture(report=report3)  # should match
    sub4 = submission_fixture(report=report4)  # Low similarity, won't match
    sub5 = submission_fixture(report=report5)  # premises with same name but different premises_id

    db.session.commit()

    # Create premises records with varying similarities to test the matching
    # 1. Exact match (100% similarity)
    exact_match = submission_premises_fixture(
        submission_id=sub1.id,
        address="456 Oak St, Chicago, IL",
        named_insured="ACME Construction Company",  # Exact match
        premises_id=mock_fni_premises.premises_id,  # Same premises ID as the mock
        organization_id=organization.id,
    )

    # 2. High similarity match
    high_similarity = submission_premises_fixture(
        submission_id=sub2.id,
        address="789 Pine St, Los Angeles, CA",
        named_insured="ACME Construction Co",  # High similarity
        premises_id=mock_fni_premises.premises_id,
        organization_id=organization.id,
    )

    # 3. Medium similarity match (near threshold)
    medium_similarity = submission_premises_fixture(
        submission_id=sub3.id,
        address="101 Elm St, Houston, TX",
        named_insured="ACME Constructions",  # Medium similarity
        premises_id=mock_fni_premises.premises_id,
        organization_id=organization.id,
    )

    # 4. Low similarity match (below threshold, should not match)
    low_similarity = submission_premises_fixture(
        submission_id=sub4.id,
        address="202 Maple St, Seattle, WA",
        named_insured="ABC Construction",  # Low similarity
        premises_id=mock_fni_premises.premises_id,
        organization_id=organization.id,
    )

    # 5. Create a premises with same name but different premises_id (should not match)
    different_premises_id = uuid.UUID(int=3)  # Different from mock_premises.id
    diff_premises = submission_premises_fixture(
        submission_id=sub5.id,
        address="303 Birch St, Miami, FL",
        named_insured="ACME Construction Company",  # Exact match but different premises_id
        premises_id=different_premises_id,
        organization_id=organization.id,
    )

    db.session.commit()

    matching_premises = BowheadService.get_matching_submission_premises(mock_submission)

    # We expect to get back the 3 matching premises (exact, high, medium similarity)
    # ordered by similarity (most similar first)
    assert len(matching_premises) == 3

    # Check the order (most similar first)
    expected_order = [
        exact_match.id,
        high_similarity.id,
        medium_similarity.id,
    ]
    actual_order = [premise.id for premise in matching_premises]
    assert actual_order == expected_order

    # Verify that low similarity and different premises_id submissions are not included
    assert low_similarity.id not in actual_order
    assert diff_premises.id not in actual_order


def test_get_matching_submission_premises_edge_cases(app_context, mocker):
    # Test edge cases for the get_matching_submission_premises method

    # Case 1: Submission without businesses
    mock_empty_submission = mocker.MagicMock()
    mock_empty_submission.fni_premises = None  # No FNI premises
    mock_empty_submission.report.id = 999  # Any ID for the report

    # Should return empty list for submission without businesses
    matching_premises = BowheadService.get_matching_submission_premises(mock_empty_submission)
    assert matching_premises == []
