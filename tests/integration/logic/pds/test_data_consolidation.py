from datetime import datetime
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
from uuid import UUID, uuid4
import json
import os

from entity_resolution_service_client_v3 import EntityNameRequest, NameScoreResponse
from sqlalchemy import false
from static_common.enums.brokerage_employee_roles import BrokerageEmployeeRoles
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.coverage_names import CoverageName
from static_common.enums.entity import EntityInformation
from static_common.enums.external import ExternalIdentifierType
from static_common.enums.fields import FieldType
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.models.business_resolution_data import ExternalIdentifier
from static_common.models.coverages import CoverageDetails
from static_common.models.file_onboarding import (
    OnboardedFile,
    ResolvedDataField,
    ResolvedDataValue,
)
from static_common.models.submission_level_data import SourceDetails
from static_common.schemas.coverages import CoverageDetailsSchema
from static_common.schemas.file_onboarding import LeanOnboardedFileSchema
from static_common.taxonomies.industry_classification import NaicsCode
import pytest

from copilot.logic.pds.data_consolidation import (
    FinancialStatementConsolidator,
    consolidate_submission_level_data,
    finish_naics_and_description_consolidation,
    process_coverages_details,
)
from copilot.models import File, Submission, db
from copilot.models.files import OnboardedFileEntityInformation, ProcessedFile
from copilot.models.reports import Coverage, SubmissionCoverage
from copilot.models.submission_consolidation_process import (
    SubmissionConsolidationProcess,
)
from copilot.models.submission_level_extracted_data import SubmissionLevelExtractedData
from copilot.models.types import (
    CoverageType,
    SubmissionConsolidationStatus,
    SubmissionCoverageType,
)
from tests.integration.coverages.fixtures import set_up_current_user
from tests.integration.factories import (
    brokerage_employee_fixture,
    brokerage_fixture,
    coverage_fixture,
    file_fixture,
    processed_file_fixture,
    submission_level_extracted_data_fixture,
    user_fixture,
)
from tests.integration.utils import fake_uuid


@pytest.fixture
def cfs_processed_data_1() -> dict:
    return {
        "files": [],
        "fields": [
            {
                "name": "Current assets",
                "values": [
                    {
                        "value": (
                            '{"units": "USD", "interval": "ANNUAL", "times": ["2023-12-31T00:00:00"], "values":'
                            " [221611.0]}"
                        ),
                        "entity_idx": 0,
                    }
                ],
                "value_type": "TEXT",
                "display_as_fact": True,
                "fact_subtype_id": "CURRENT_ASSETS",
            },
            {
                "name": "Net income",
                "values": [
                    {
                        "value": (
                            '{"units": "USD", "interval": "ANNUAL", "times": ["2023-12-31T00:00:00",'
                            ' "2022-12-31T00:00:00"], "values": [12876.0, 64060.0]}'
                        ),
                        "entity_idx": None,
                    }
                ],
                "value_type": "TEXT",
                "display_as_fact": True,
                "fact_subtype_id": "NET_INCOME",
            },
            {
                "name": "Other assets",
                "values": [
                    {
                        "value": (
                            '{"units": "USD", "interval": "ANNUAL", "times": ["2023-12-31T00:00:00"], "values": [-5.0]}'
                        ),
                        "entity_idx": 0,
                    }
                ],
                "value_type": "TEXT",
                "display_as_fact": True,
                "fact_subtype_id": "OTHER_ASSETS",
            },
        ],
        "entities": [
            {
                "id": "leavitt's mortuary, inc",
                "type": "Business",
            }
        ],
        "entity_information": [
            {
                "name": "Name",
                "values": [{"value": "LEAVITT'S MORTUARY, INC", "file_idx": 0, "entity_idx": 0}],
                "value_type": "TEXT",
            }
        ],
    }


@pytest.fixture
def cfs_processed_data_2() -> dict:
    return {
        "files": [],
        "fields": [
            {
                "name": "Current assets",
                "values": [
                    {
                        "value": (
                            '{"units": "USD", "interval": "ANNUAL", "times": ["2022-12-31T00:00:00"], "values":'
                            " [209060.0]}"
                        ),
                        "entity_idx": 0,
                    }
                ],
                "value_type": "TEXT",
                "display_as_fact": True,
                "fact_subtype_id": "CURRENT_ASSETS",
            },
            {
                "name": "Current liabilities",
                "values": [
                    {
                        "value": (
                            '{"units": "USD", "interval": "ANNUAL", "times": ["2023-12-31T00:00:00",'
                            ' "2022-12-31T00:00:00"], "values": [276184.0, 234390.0]}'
                        ),
                        "entity_idx": None,
                    }
                ],
                "value_type": "TEXT",
                "display_as_fact": True,
                "fact_subtype_id": "CURRENT_LIABILITIES",
            },
            {
                "name": "Other assets",
                "values": [
                    {
                        "value": (
                            '{"units": "USD", "interval": "ANNUAL", "times": ["2023-12-31T00:00:00",'
                            ' "2022-12-31T00:00:00"], "values": [312.0, 271.0]}'
                        ),
                        "entity_idx": 0,
                    }
                ],
                "value_type": "TEXT",
                "display_as_fact": True,
                "fact_subtype_id": "OTHER_ASSETS",
            },
        ],
        "entities": [
            {
                "id": "leavitt's mortuary, inc",
                "type": "Business",
            }
        ],
        "entity_information": [
            {
                "name": "Name",
                "values": [{"value": "LEAVITT'S MORTUARY, INC", "file_idx": 0, "entity_idx": 0}],
                "value_type": "TEXT",
            }
        ],
    }


@pytest.fixture
def cfs_processed_data_3() -> dict:
    return {
        "files": [],
        "fields": [
            {
                "name": "Current liabilities",
                "values": [
                    {
                        "value": (
                            '{"units": "USD", "interval": "ANNUAL", "times": ["2023-12-31T00:00:00",'
                            ' "2022-12-31T00:00:00"], "values": [276184.0, 234390.0]}'
                        ),
                        "entity_idx": None,
                    }
                ],
                "value_type": "TEXT",
                "display_as_fact": True,
                "fact_subtype_id": "CURRENT_LIABILITIES",
            },
            {
                "name": "Other assets",
                "values": [
                    {
                        "value": (
                            '{"units": "USD", "interval": "ANNUAL", "times": ["2021-12-31T00:00:00"], "values":'
                            " [205.0]}"
                        ),
                        "entity_idx": 0,
                    }
                ],
                "value_type": "TEXT",
                "display_as_fact": True,
                "fact_subtype_id": "OTHER_ASSETS",
            },
        ],
        "entities": [
            {
                "id": "leavitt's mortuary, inc",
                "type": "Business",
            }
        ],
        "entity_information": [
            {
                "name": "Name",
                "values": [{"value": "LEAVITT'S MORTUARY, INC", "file_idx": 0, "entity_idx": 0}],
                "value_type": "TEXT",
            }
        ],
    }


@pytest.fixture
def cfs_processed_data_with_entities() -> dict:
    return {
        "files": [],
        "fields": [],
        "entities": [
            {"id": "leavitt's mortuary, inc", "type": "Business"},
            {"id": "rehrig pacific holdings, inc", "type": "Business"},
        ],
        "entity_information": [
            {
                "name": "Name",
                "values": [
                    {"value": "LEAVITT'S MORTUARY", "file_idx": 0, "entity_idx": 0},
                    {"value": "REHRIG PACIFIC HOLDINGS, INC.", "file_idx": 0, "entity_idx": 1},
                ],
                "value_type": "TEXT",
            }
        ],
    }


@pytest.fixture
def dao_processed_data_with_entities() -> dict:
    return {
        "files": [],
        "fields": [],
        "entities": [
            {
                "idx": 0,
                "type": "Business",
                "entity_named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED.value,
            }
        ],
    }


@pytest.fixture
def drivers_processed_data_with_entities() -> dict:
    return {
        "files": [],
        "fields": [],
        "entities": [{"id": "7b4c8bac-25db-5015-6708-1e702bedc463", "type": "Driver", "remote_id": "********"}],
        "entity_information": [
            {
                "name": "Name",
                "values": [{"value": "Ivan Bozhilov", "file_idx": 0, "entity_idx": 0}],
                "value_type": "TEXT",
            }
        ],
    }


@pytest.fixture
def sov_processed_data_with_entities() -> dict:
    return {
        "files": [],
        "fields": [],
        "entities": [{"id": "leavitt's mortuary, 456 main str, salt lake city, ut 84101", "type": "Business"}],
        "entity_information": [
            {
                "name": "Name",
                "values": [
                    {"value": "LEAVITT'S MORTUARY", "file_idx": 0, "entity_idx": 0},
                ],
                "value_type": "TEXT",
            },
            {
                "name": "Address",
                "values": [
                    {"value": "456 Main str, Salt Lake City, UT 84101", "file_idx": 0, "entity_idx": 0},
                ],
                "value_type": "TEXT",
            },
        ],
    }


@pytest.fixture
def acord_processed_data_with_entities() -> dict:
    return {
        "files": [],
        "fields": [],
        "entities": [
            {
                "id": "leavitt's mortuary, inc, 123 main str, salt lake city, ut 84101",
                "type": "Business",
                "entity_named_insured": "FIRST_NAMED_INSURED",
            },
            {"id": "rehrig pacific holdings, inc, 456 main str, salt lake city, ut 84101", "type": "Business"},
            {"id": "some", "type": "Business"},
        ],
        "entity_information": [
            {
                "name": "Name",
                "values": [
                    {"value": "LEAVITT'S MORTUARY, INC", "file_idx": 0, "entity_idx": 0},
                    {"value": "REHRIG PACIFIC HOLDINGS, INC.", "file_idx": 0, "entity_idx": 1},
                    {
                        "value": "SOME",
                        "file_idx": 0,
                        "entity_idx": 2,
                    },
                ],
                "value_type": "TEXT",
            },
            {
                "name": "Address",
                "values": [
                    {"value": "123 Main str, Salt Lake City, UT 84101", "file_idx": 0, "entity_idx": 0},
                    {"value": "456 Main str, Salt Lake City, UT 84101", "file_idx": 0, "entity_idx": 1},
                ],
                "value_type": "TEXT",
            },
            {
                "name": "FEIN",
                "values": [{"value": "123456789", "file_idx": 0, "entity_idx": 0}],
                "value_type": "TEXT",
            },
        ],
    }


@pytest.fixture
def acord_fni(acord_processed_data_with_entities) -> OnboardedFileEntityInformation:
    name = acord_processed_data_with_entities["entity_information"][0]["values"][0]["value"]
    address = acord_processed_data_with_entities["entity_information"][1]["values"][0]["value"]
    fein = acord_processed_data_with_entities["entity_information"][-1]["values"][0]["value"]
    return OnboardedFileEntityInformation(
        entity_id=uuid4(),
        entity=Mock(),
        entity_type=SubmissionEntityType.BUSINESS,
        name=name,
        address=address,
        name_field=ResolvedDataField(values=[ResolvedDataValue(value=name)], name="Name", value_type=FieldType.TEXT),
        address_field=ResolvedDataField(
            values=[ResolvedDataValue(value=address)], name="Address", value_type=FieldType.TEXT
        ),
        identifiers=[
            ExternalIdentifier(
                value=str(fein),
                type=ExternalIdentifierType.FEIN,
            )
        ],
    )


@pytest.fixture
def submission_with_cfs_with_entities(
    submission: Submission,
    cfs_processed_data_with_entities: dict,
    drivers_processed_data_with_entities: dict,
    acord_processed_data_with_entities: dict,
    dao_processed_data_with_entities: dict,
) -> dict:
    cfs_file_id = uuid4()
    drivers_file_id = uuid4()
    acord_file_id = uuid4()
    dao_file_id = uuid4()
    file_fixture(
        id=cfs_file_id,
        file_type=FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
        classification=ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF,
        parent_id=None,
        submission_id=submission.id,
        processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
    )
    cfs_processed_data_with_entities["files"] = [str(cfs_file_id)]
    processed_file_fixture(file_id=cfs_file_id, processed_data=cfs_processed_data_with_entities)
    file_fixture(
        id=drivers_file_id,
        file_type=FileType.DRIVERS,
        classification=ClassificationDocumentType.DRIVERS_SPREADSHEET,
        parent_id=None,
        submission_id=submission.id,
        processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
    )
    drivers_processed_data_with_entities["files"] = [str(drivers_file_id)]
    processed_file_fixture(file_id=drivers_file_id, processed_data=drivers_processed_data_with_entities)
    file_fixture(
        id=acord_file_id,
        file_type=FileType.ACORD_FORM,
        classification=ClassificationDocumentType.ACORD_125,
        parent_id=None,
        submission_id=submission.id,
        processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
    )
    acord_processed_data_with_entities["files"] = [str(acord_file_id)]
    processed_file_fixture(file_id=acord_file_id, processed_data=acord_processed_data_with_entities)
    file_fixture(
        id=dao_file_id,
        file_type=FileType.DIRECTORS_AND_OFFICERS,
        classification=ClassificationDocumentType.DIRECTORS_AND_OFFICERS_PDF,
        parent_id=None,
        submission_id=submission.id,
        processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
    )
    dao_processed_data_with_entities["files"] = [str(dao_file_id)]
    processed_file_fixture(file_id=dao_file_id, processed_data=dao_processed_data_with_entities)
    db.session.commit()
    return {
        "cfs_file_id": cfs_file_id,
        "drivers_file_id": drivers_file_id,
        "acord_file_id": acord_file_id,
        "dao_file_id": dao_file_id,
    }


@pytest.fixture
def submission_with_cfs_with_entities_entity_named_insured(
    submission: Submission,
    submission_with_cfs_with_entities: dict[str, UUID],
    sov_processed_data_with_entities: dict,
) -> dict:
    sov_file_id = uuid4()
    file_fixture(
        id=sov_file_id,
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        parent_id=None,
        submission_id=submission.id,
        processing_state=FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION,
    )
    sov_processed_data_with_entities["files"] = [str(sov_file_id)]
    processed_file_fixture(file_id=sov_file_id, processed_data=sov_processed_data_with_entities)
    db.session.commit()
    return {**submission_with_cfs_with_entities, "sov_file_id": sov_file_id}


@pytest.fixture
def email_file(submission: Submission) -> File:
    email_file = file_fixture(
        file_type=FileType.EMAIL,
        classification=ClassificationDocumentType.EMAIL,
        parent_id=None,
        submission_id=submission.id,
        processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
    )
    db.session.commit()
    return email_file


@pytest.fixture
def supplemental_file(submission: Submission) -> File:
    supplemental_file = file_fixture(
        file_type=FileType.SUPPLEMENTAL_FORM,
        classification=ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_PDF,
        parent_id=None,
        submission_id=submission.id,
        processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
    )
    db.session.commit()
    return supplemental_file


@pytest.fixture
def acord_file(submission: Submission) -> File:
    acord_file = file_fixture(
        file_type=FileType.ACORD_FORM,
        classification=ClassificationDocumentType.ACORD_125,
        parent_id=None,
        submission_id=submission.id,
        processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
    )
    db.session.commit()
    return acord_file


@pytest.fixture
def email_description(email_file: File, submission: Submission) -> SubmissionLevelExtractedData:
    email_description = submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=email_file.id,
        field=EntityInformation.DESCRIPTION,
        value=json.dumps("Email description"),
        is_valid=True,
    )
    db.session.commit()
    return email_description


@pytest.fixture
def supplemental_description(supplemental_file: File, submission: Submission) -> SubmissionLevelExtractedData:
    supplemental_description = submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=supplemental_file.id,
        field=EntityInformation.DESCRIPTION,
        value=json.dumps("Supplemental description"),
        is_valid=True,
    )
    db.session.commit()
    return supplemental_description


@pytest.fixture
def submission_with_extracted_submission_level_data(
    submission: Submission, email_file: File, supplemental_file: File, acord_file: File
) -> Submission:
    coverage_fixture(name=Coverage.ExistingNames.Liability, coverage_types=[CoverageType.PRIMARY, CoverageType.EXCESS])
    coverage_fixture(
        name=Coverage.ExistingNames.BusinessAuto, coverage_types=[CoverageType.PRIMARY, CoverageType.EXCESS]
    )
    coverage_details1 = [
        CoverageDetails(coverage_name=CoverageName.Liability, coverage_type=CoverageType.PRIMARY),
        CoverageDetails(
            coverage_name=CoverageName.Liability,
            coverage_type=SubmissionCoverageType.EXCESS,
            excess_attachment_point=100000,
        ),
    ]
    submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=email_file.id,
        field=EntityInformation.COVERAGES,
        value=json.dumps([(Coverage.ExistingNames.Liability, CoverageType.PRIMARY)]),
    )
    coverage_details2 = [
        CoverageDetails(
            coverage_name=CoverageName.Liability,
            coverage_type=SubmissionCoverageType.EXCESS,
            excess_attachment_point=200000,
        )
    ]
    submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=email_file.id,
        field=EntityInformation.COVERAGES_DETAILS,
        value=json.dumps(CoverageDetailsSchema().dumps(coverage_details1, many=True)),
    )
    submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=supplemental_file.id,
        field=EntityInformation.COVERAGES,
        value=json.dumps([(Coverage.ExistingNames.BusinessAuto, CoverageType.EXCESS)]),
    )
    submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=acord_file.id,
        field=EntityInformation.COVERAGES_DETAILS,
        value=json.dumps(CoverageDetailsSchema().dumps(coverage_details2, many=True)),
    )
    db.session.commit()
    return submission


@pytest.fixture
def setup_same_parent_file(submission: Submission, cfs_processed_data_1: dict, cfs_processed_data_2: dict) -> dict:
    child_file_1_id = uuid4()
    child_file_2_id = uuid4()
    parent_file_id = uuid4()
    file_fixture(
        id=parent_file_id,
        file_type=FileType.MERGED,
        classification=ClassificationDocumentType.MERGED,
        parent_id=None,
        submission_id=submission.id,
        processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
    )
    file_fixture(
        id=child_file_1_id,
        file_type=FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
        classification=ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF,
        parent_file_id=parent_file_id,
        submission_id=submission.id,
        processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
    )
    cfs_processed_data_1["files"] = [str(child_file_1_id)]
    processed_file_fixture(file_id=child_file_1_id, processed_data=cfs_processed_data_1)
    file_fixture(
        id=child_file_2_id,
        file_type=FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
        classification=ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF,
        parent_file_id=parent_file_id,
        submission_id=submission.id,
        processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
    )
    cfs_processed_data_2["files"] = [str(child_file_2_id)]
    processed_file_fixture(file_id=child_file_2_id, processed_data=cfs_processed_data_2)
    db.session.commit()
    return {
        "parent_file_id": parent_file_id,
        "child_file_1_id": child_file_1_id,
        "child_file_2_id": child_file_2_id,
    }


@pytest.fixture
def setup_different_parent_files(submission: Submission, setup_same_parent_file) -> dict:
    child_file_2_id = setup_same_parent_file["child_file_2_id"]
    parent_2_file_id = uuid4()
    file_fixture(
        id=parent_2_file_id,
        file_type=FileType.MERGED,
        classification=ClassificationDocumentType.MERGED,
        parent_id=None,
        submission_id=submission.id,
        processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
    )
    child_2_file = db.session.query(File).get(child_file_2_id)
    child_2_file.parent_file_id = parent_2_file_id
    db.session.commit()
    return {
        "parent_file_id": setup_same_parent_file["parent_file_id"],
        "child_file_1_id": setup_same_parent_file["child_file_1_id"],
        "child_file_2_id": child_file_2_id,
        "parent_2_file_id": parent_2_file_id,
    }


def test_consolidate_cfs_entities(
    app_context, submission, submission_with_cfs_with_entities, acord_fni: OnboardedFileEntityInformation, mocker
):
    cfs_file_id = submission_with_cfs_with_entities["cfs_file_id"]
    dao_file_id = submission_with_cfs_with_entities["dao_file_id"]

    def fake_scorer(name: str, potential_names: list[str], enforce_flexible_in_name_value: bool = True):
        if name == "LEAVITT'S MORTUARY, INC":
            return [
                NameScoreResponse(
                    name=EntityNameRequest(value="LEAVITT'S MORTUARY, INC"),
                    score=1.0,
                    reason="For the purpose of this test we want to match this name",
                ),
                NameScoreResponse(
                    name=EntityNameRequest(value="REHRIG PACIFIC HOLDINGS, INC."),
                    score=0.0,
                    reason="For the purpose of this test we do not want to match this name",
                ),
            ]
        elif name == "REHRIG PACIFIC HOLDINGS, INC.":
            return [
                NameScoreResponse(
                    name=EntityNameRequest(value="REHRIG PACIFIC HOLDINGS, INC."),
                    score=1.0,
                    reason="For the purpose of this test we want to match this name",
                ),
                NameScoreResponse(
                    name=EntityNameRequest(value="LEAVITT'S MORTUARY, INC"),
                    score=0.0,
                    reason="For the purpose of this test we do not want to match this name",
                ),
            ]
        return [
            NameScoreResponse(
                name=EntityNameRequest(value=name),
                score=1.0,
                reason="For the purpose of this test we want to match the names",
            )
            for name in potential_names
        ]

    mocker.patch("copilot.clients.ers_v3.ERSClientV3.get_entity_names_score", side_effect=fake_scorer)

    consolidation_process = SubmissionConsolidationProcess()
    FinancialStatementConsolidator(submission, consolidation_process).consolidate(acord_fni)

    assert consolidation_process.is_financial_statements_consolidated
    data_consolidated_files = (
        db.session.query(File).filter(File.processing_state == FileProcessingState.DATA_CONSOLIDATED).all()
    )
    assert len(data_consolidated_files) == 2
    assert data_consolidated_files[0].id == cfs_file_id
    processed_file = data_consolidated_files[0].processed_file
    assert processed_file
    processed_data = processed_file.processed_data
    assert processed_data
    assert len(processed_data["entities"]) == 2
    assert {field["name"] for field in processed_data["entity_information"]} == {"Name", "Address", "FEIN"}
    name_field = next((field for field in processed_data["entity_information"] if field["name"] == "Name"), None)
    assert name_field
    assert {value["value"] for value in name_field["values"]} == {
        "LEAVITT'S MORTUARY",
        "REHRIG PACIFIC HOLDINGS, INC.",
    }
    address_field = next((field for field in processed_data["entity_information"] if field["name"] == "Address"), None)
    assert address_field
    assert {value["value"] for value in address_field["values"]} == {
        "123 Main str, Salt Lake City, UT 84101",
        "456 Main str, Salt Lake City, UT 84101",
    }
    fein_field = next((field for field in processed_data["entity_information"] if field["name"] == "FEIN"), None)
    assert fein_field
    assert {value["value"] for value in fein_field["values"]} == {"123456789"}

    assert data_consolidated_files[1].id == dao_file_id
    processed_file = data_consolidated_files[1].processed_file
    assert processed_file.processed_data and len(processed_file.processed_data["entities"]) == 1
    assert {field["name"] for field in processed_file.processed_data["entity_information"]} == {
        "Name",
        "Address",
        "FEIN",
    }


def test_consolidate_submission_level_data(
    app_context,
    submission_with_extracted_submission_level_data,
    set_up_current_user,
):
    consolidation_process = SubmissionConsolidationProcess()
    consolidate_submission_level_data(submission_with_extracted_submission_level_data, consolidation_process)

    assert consolidation_process.is_description_and_naics_consolidated
    submission_level_extracted_data = db.session.query(SubmissionLevelExtractedData).all()
    assert submission_with_extracted_submission_level_data.generated_description_of_operations is None
    assert submission_with_extracted_submission_level_data.primary_naics_code is None
    assert len(submission_with_extracted_submission_level_data.coverages) == 3
    excess_liability_coverage = next(
        coverage
        for coverage in submission_with_extracted_submission_level_data.coverages
        if coverage.coverage.name == Coverage.ExistingNames.Liability and coverage.coverage_type == CoverageType.EXCESS
    )
    assert excess_liability_coverage.attachment_point == 200000
    assert len(submission_level_extracted_data) == 4
    for item in submission_level_extracted_data:
        if item.file.file_type == FileType.EMAIL and item.field == EntityInformation.COVERAGES:
            assert item.is_selected is False
            assert item.selected_by_user is None
        else:
            assert item.is_selected
            assert item.selected_by_user is not None


def test_consolidate_submission_level_data_with_naics_and_desc(
    app_context,
    submission: Submission,
    email_file: File,
    acord_file: File,
    set_up_current_user,
):
    description = submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=email_file.id,
        field=EntityInformation.DESCRIPTION,
        source_details=SourceDetails.GENERATED,
        value=json.dumps("Test description"),
        is_valid=True,
    )
    description_2 = submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=acord_file.id,
        field=EntityInformation.DESCRIPTION,
        source_details=SourceDetails.GENERATED,
        value=json.dumps("Test description_2"),
        is_valid=True,
    )
    db.session.commit()

    naics_1 = submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=email_file.id,
        field=EntityInformation.NAICS_CODES,
        source_details=SourceDetails.GENERATED,
        parent_id=str(description.id),
        value=json.dumps({NaicsCode.NAICS_111110: 1.0}),
    )
    naics_2 = submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=acord_file.id,
        field=EntityInformation.NAICS_CODES,
        source_details=SourceDetails.FILE_CONTENT,
        parent_id=str(description_2.id),
        value=json.dumps([NaicsCode.NAICS_236116]),
    )
    db.session.commit()
    consolidation_process = SubmissionConsolidationProcess()
    consolidate_submission_level_data(submission, consolidation_process)

    assert consolidation_process.is_description_and_naics_consolidated is False

    finish_naics_and_description_consolidation(
        submission,
        consolidation_process,
        str(naics_1.id),
        NaicsCode.NAICS_111110.value,
        str(description.id),
        "Test description",
    )

    db.session.commit()

    assert consolidation_process.is_description_and_naics_consolidated
    assert submission.primary_naics_code == NaicsCode.NAICS_111110
    assert submission.is_naics_verified is True

    submission_level_extracted_data = db.session.query(SubmissionLevelExtractedData).all()

    for item in submission_level_extracted_data:
        if item.file_id == email_file.id:
            assert item.is_selected
            assert item.selected_by_user is not None
        else:
            assert item.is_selected is False
            assert item.selected_by_user is None


@pytest.mark.parametrize("is_project", [True, False])
def test_consolidate_submission_level_data_no_naics(
    app_context,
    submission: Submission,
    email_file: File,
    set_up_current_user,
    mocker,
    is_project,
):
    mocker.patch("copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled", return_value=True)
    if is_project:
        submission_level_extracted_data_fixture(
            submission_id=submission.id,
            file_id=email_file.id,
            field=EntityInformation.IS_PROJECT,
            source_details=SourceDetails.FILE_CONTENT_RAW,
            value=json.dumps(True),
        )
    db.session.commit()
    consolidation_process = SubmissionConsolidationProcess()
    consolidate_submission_level_data(submission, consolidation_process)

    assert consolidation_process.is_description_and_naics_consolidated
    assert submission.primary_naics_code is None
    assert submission.is_naics_verified == False


def test_consolidate_cfs_entities_prefers_fni(
    app_context, submission, submission_with_cfs_with_entities_entity_named_insured, mocker
):
    cfs_file_id = submission_with_cfs_with_entities_entity_named_insured["cfs_file_id"]

    def fake_scorer(name: str, potential_names: list[str], enforce_flexible_in_name_value: bool = True):
        if name == "LEAVITT'S MORTUARY":
            return [
                NameScoreResponse(
                    name=EntityNameRequest(value="LEAVITT'S MORTUARY, INC"),
                    score=1.0,
                    reason="For the purpose of this test we want to match this name",
                ),
                NameScoreResponse(
                    name=EntityNameRequest(value="LEAVITT'S MORTUARY"),
                    score=0.9,
                    reason="For the purpose of this test we want to match this name",
                ),
                NameScoreResponse(
                    name=EntityNameRequest(value="REHRIG PACIFIC HOLDINGS, INC."),
                    score=0.0,
                    reason="For the purpose of this test we do not want to match this name",
                ),
            ]
        elif name == "REHRIG PACIFIC HOLDINGS, INC.":
            return [
                NameScoreResponse(
                    name=EntityNameRequest(value="REHRIG PACIFIC HOLDINGS, INC."),
                    score=1.0,
                    reason="For the purpose of this test we want to match this name",
                ),
                NameScoreResponse(
                    name=EntityNameRequest(value="LEAVITT'S MORTUARY, INC"),
                    score=0.0,
                    reason="For the purpose of this test we do not want to match this name",
                ),
                NameScoreResponse(
                    name=EntityNameRequest(value="LEAVITT'S MORTUARY, INC"),
                    score=0.0,
                    reason="For the purpose of this test we do not want to match this name",
                ),
            ]
        return [
            NameScoreResponse(
                name=EntityNameRequest(value=name),
                score=1.0,
                reason="For the purpose of this test we want to match the names",
            )
            for name in potential_names
        ]

    mocker.patch("copilot.clients.ers_v3.ERSClientV3.get_entity_names_score", side_effect=fake_scorer)

    consolidation_process = SubmissionConsolidationProcess()
    FinancialStatementConsolidator(submission, consolidation_process).consolidate(None)

    assert consolidation_process.is_financial_statements_consolidated

    data_consolidated_files = (
        db.session.query(File).filter(File.processing_state == FileProcessingState.DATA_CONSOLIDATED).all()
    )
    assert len(data_consolidated_files) == 2
    assert data_consolidated_files[0].id == cfs_file_id
    processed_file = data_consolidated_files[0].processed_file
    assert processed_file
    processed_data = processed_file.processed_data
    assert processed_data
    assert len(processed_data["entities"]) == 2
    assert {field["name"] for field in processed_data["entity_information"]} == {"Name", "Address", "FEIN"}
    name_field = next((field for field in processed_data["entity_information"] if field["name"] == "Name"), None)
    assert name_field
    assert {value["value"] for value in name_field["values"]} == {
        "LEAVITT'S MORTUARY",
        "REHRIG PACIFIC HOLDINGS, INC.",
    }
    address_field = next((field for field in processed_data["entity_information"] if field["name"] == "Address"), None)
    assert address_field
    assert {value["value"] for value in address_field["values"]} == {
        "123 Main str, Salt Lake City, UT 84101",
        "456 Main str, Salt Lake City, UT 84101",
    }
    fein_field = next((field for field in processed_data["entity_information"] if field["name"] == "FEIN"), None)
    assert fein_field
    assert {value["value"] for value in fein_field["values"]} == {"123456789"}


def test_consolidate_cfs_entities_keeps_fni(
    app_context, submission, submission_with_cfs_with_entities_entity_named_insured, mocker
):
    cfs_file_id = submission_with_cfs_with_entities_entity_named_insured["cfs_file_id"]
    sov_file_id = submission_with_cfs_with_entities_entity_named_insured["sov_file_id"]
    sov_pf = ProcessedFile.query.filter(ProcessedFile.file_id == sov_file_id).first()
    sov_pf.processed_data["entity_information"][0]["values"][0]["value"] = "LEAVITT'S MORTUARY, INC"
    db.session.commit()

    def fake_scorer(name: str, potential_names: list[str], enforce_flexible_in_name_value: bool = True):
        if name == "LEAVITT'S MORTUARY, INC":
            return [
                NameScoreResponse(
                    name=EntityNameRequest(value="LEAVITT'S MORTUARY, INC"),
                    score=1.0,
                    reason="For the purpose of this test we want to match this name",
                ),
                NameScoreResponse(
                    name=EntityNameRequest(value="REHRIG PACIFIC HOLDINGS, INC."),
                    score=0.0,
                    reason="For the purpose of this test we do not want to match this name",
                ),
            ]
        elif name == "REHRIG PACIFIC HOLDINGS, INC.":
            return [
                NameScoreResponse(
                    name=EntityNameRequest(value="REHRIG PACIFIC HOLDINGS, INC."),
                    score=1.0,
                    reason="For the purpose of this test we want to match this name",
                ),
                NameScoreResponse(
                    name=EntityNameRequest(value="LEAVITT'S MORTUARY, INC"),
                    score=0.0,
                    reason="For the purpose of this test we do not want to match this name",
                ),
            ]
        return [
            NameScoreResponse(
                name=EntityNameRequest(value=name),
                score=1.0,
                reason="For the purpose of this test we want to match the names",
            )
            for name in potential_names
        ]

    mocker.patch("copilot.clients.ers_v3.ERSClientV3.get_entity_names_score", side_effect=fake_scorer)

    consolidation_process = SubmissionConsolidationProcess()
    FinancialStatementConsolidator(submission, consolidation_process).consolidate(None)

    assert consolidation_process.is_financial_statements_consolidated

    data_consolidated_files = (
        db.session.query(File).filter(File.processing_state == FileProcessingState.DATA_CONSOLIDATED).all()
    )
    assert len(data_consolidated_files) == 2
    assert data_consolidated_files[0].id == cfs_file_id
    processed_file = data_consolidated_files[0].processed_file
    assert processed_file
    processed_data = processed_file.processed_data
    assert processed_data
    assert len(processed_data["entities"]) == 2
    assert {field["name"] for field in processed_data["entity_information"]} == {"Name", "Address", "FEIN"}
    name_field = next((field for field in processed_data["entity_information"] if field["name"] == "Name"), None)
    assert name_field
    assert {value["value"] for value in name_field["values"]} == {
        "LEAVITT'S MORTUARY",
        "REHRIG PACIFIC HOLDINGS, INC.",
    }
    address_field = next((field for field in processed_data["entity_information"] if field["name"] == "Address"), None)
    assert address_field
    assert {value["value"] for value in address_field["values"]} == {
        "123 Main str, Salt Lake City, UT 84101",
        "456 Main str, Salt Lake City, UT 84101",
    }
    fein_field = next((field for field in processed_data["entity_information"] if field["name"] == "FEIN"), None)
    assert fein_field
    assert {value["value"] for value in fein_field["values"]} == {"123456789"}


def test_move_files_when_nothing_to_consolidate(app_context, submission):
    file_id = uuid4()
    file_fixture(
        id=file_id,
        file_type=FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
        classification=ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF,
        submission_id=submission.id,
        processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
    )
    processed_file_fixture(
        file_id=file_id, processed_data=LeanOnboardedFileSchema().dump(OnboardedFile(files=[file_id]))
    )
    db.session.commit()
    consolidation_process = SubmissionConsolidationProcess()
    FinancialStatementConsolidator(submission, consolidation_process).consolidate(None)

    assert consolidation_process.is_financial_statements_consolidated
    assert (
        db.session.query(File).filter(File.id == file_id).first().processing_state
        == FileProcessingState.DATA_CONSOLIDATED
    )


def test_process_coverages_details(app_context, submission):
    file = file_fixture(
        id=uuid4(),
        file_type=FileType.EMAIL,
        classification=ClassificationDocumentType.EMAIL,
        submission_id=submission.id,
        processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
    )
    coverage_fixture(name=CoverageName.Liability, organization_id=submission.organization_id)
    coverage_fixture(name=CoverageName.EmploymentPracticesLiability, organization_id=submission.organization_id)
    coverage_fixture(name=CoverageName.FiduciaryLiability, organization_id=submission.organization_id)
    processed_file_fixture(file_id=file.id, processed_data={})

    coverage_details = [
        CoverageDetails(CoverageName.EmploymentPracticesLiability, None, 1000.0),
        CoverageDetails(CoverageName.FiduciaryLiability, None, 2000.0),
    ]
    coverage_details_str = CoverageDetailsSchema().dumps(coverage_details, many=True)

    assert SubmissionCoverage.query.filter_by(submission_id=submission.id).count() == 0
    assert len(submission.coverages) == 0

    process_coverages_details(submission, file.id, coverage_details_str, file.file_type)

    assert SubmissionCoverage.query.filter_by(submission_id=submission.id).count() == 2
    assert len(submission.coverages) == 2
    assert next(
        iter(
            [
                sc
                for sc in submission.coverages
                if sc.coverage.name == CoverageName.EmploymentPracticesLiability
                and sc.self_insurance_retention == 1000.0
            ]
        ),
        None,
    )
    assert next(
        iter(
            [
                sc
                for sc in submission.coverages
                if sc.coverage.name == CoverageName.FiduciaryLiability and sc.self_insurance_retention == 2000.0
            ]
        ),
        None,
    )


@pytest.mark.parametrize("existing_naics", [None, NaicsCode.NAICS_236116])
def test_finish_naics_and_description_consolidation(
    existing_naics: NaicsCode | None, app_context, submission, email_file, acord_file, set_up_current_user
):
    naics_1 = submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=email_file.id,
        field=EntityInformation.NAICS_CODES,
        source_details=SourceDetails.GENERATED,
        value=json.dumps({NaicsCode.NAICS_111110: 1.0}),
    )
    naics_2 = submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=acord_file.id,
        field=EntityInformation.NAICS_CODES,
        source_details=SourceDetails.GENERATED,
        value=json.dumps({NaicsCode.NAICS_111120: 0.4, NaicsCode.NAICS_236116: 0.8}),
    )
    description_1 = submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=email_file.id,
        field=EntityInformation.DESCRIPTION,
        source_details=SourceDetails.GENERATED,
        value=json.dumps("Test description"),
        is_valid=True,
    )
    description_2 = submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=acord_file.id,
        field=EntityInformation.DESCRIPTION,
        source_details=SourceDetails.GENERATED,
        value=json.dumps("Test description 2"),
        is_valid=True,
    )

    if existing_naics:
        submission.primary_naics_code = existing_naics.value if existing_naics else None
        submission.is_naics_verified = True if existing_naics else False

        submission_level_extracted_data_fixture(
            submission_id=submission.id,
            field=EntityInformation.NAICS_CODES.value,
            value=json.dumps({existing_naics.value: 1.0}),
            source_details=SourceDetails.MANUAL,
            is_selected=True,
        )

    db.session.commit()

    consolidation_process = SubmissionConsolidationProcess()
    consolidation_process.is_em_and_acord_files_consolidated = True
    consolidation_process.status = SubmissionConsolidationStatus.PENDING

    selected_naics_id = str(naics_1.id)
    selected_naics_value = "NAICS_111110"
    selected_description_id = str(description_1.id)
    selected_description_value = "Test Description"

    with (
        patch("copilot.logic.pds.data_consolidation.handle_updating_naics_code") as mock_handle_updating_naics_code,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
    ):
        finish_naics_and_description_consolidation(
            submission,
            consolidation_process,
            selected_naics_id,
            selected_naics_value,
            selected_description_id,
            selected_description_value,
        )

        assert consolidation_process.is_description_and_naics_consolidated
        assert consolidation_process.status == SubmissionConsolidationStatus.COMPLETED
        if not existing_naics:
            assert submission.primary_naics_code == selected_naics_value
            assert naics_1.is_selected
        else:
            assert submission.primary_naics_code == existing_naics.value
            assert naics_1.is_selected is False

        assert submission.is_naics_verified
        assert submission.generated_description_of_operations == selected_description_value
        data_consolidated_event.assert_called_once_with(submission_id=submission.id, is_auto_processed=True)

        assert naics_2.is_selected is False
        assert description_1.is_selected
        assert description_2.is_selected is False
        mock_handle_updating_naics_code.assert_called_once()


def test_consolidate_submission_level_data_for_boss_submission(
    app_context,
    boss_submission: Submission,
    email_file: File,
    acord_file: File,
    set_up_current_user,
):
    os.environ["USE_BROKER_RESOLVER"] = "True"
    nw_underwriter = user_fixture(id=23, organization_id=boss_submission.organization_id, name="NW Underwriter")
    bb_brokers = brokerage_fixture(name="BB Brokers", organization_id=boss_submission.organization_id)
    saul_goodman = brokerage_employee_fixture(
        brokerage_id=bb_brokers.id,
        name="Saul Goodman",
        email="<EMAIL>",
        organization_id=boss_submission.organization_id,
        roles=[BrokerageEmployeeRoles.AGENT, BrokerageEmployeeRoles.CORRESPONDENCE_CONTACT],
    )
    hhm_brokerage = brokerage_fixture(name="HHM brokerage", organization_id=boss_submission.organization_id)
    hhm_broker = brokerage_employee_fixture(
        brokerage_id=hhm_brokerage.id,
        name="Chuck McGill",
        email="<EMAIL>",
        organization_id=boss_submission.organization_id,
        roles=[BrokerageEmployeeRoles.AGENT, BrokerageEmployeeRoles.CORRESPONDENCE_CONTACT],
    )
    db.session.commit()

    submission_level_extracted_data_fixture(
        submission_id=boss_submission.id,
        file_id=email_file.id,
        field=EntityInformation.UNDERWRITER_USER_IDS,
        source_details=SourceDetails.FILE_CONTENT_RAW,
        value=json.dumps([23]),
    )
    submission_level_extracted_data_fixture(
        submission_id=boss_submission.id,
        file_id=email_file.id,
        field=EntityInformation.IS_RENEWAL,
        source_details=SourceDetails.FILE_CONTENT_RAW,
        value=json.dumps(False),
    )
    submission_level_extracted_data_fixture(
        submission_id=boss_submission.id,
        file_id=email_file.id,
        field=EntityInformation.POLICY_EFFECTIVE_START_DATE,
        source_details=SourceDetails.FILE_CONTENT_RAW,
        value=json.dumps(f"2027-01-01"),
    )
    submission_level_extracted_data_fixture(
        submission_id=boss_submission.id,
        file_id=email_file.id,
        field=EntityInformation.POLICY_END_DATE,
        source_details=SourceDetails.FILE_CONTENT_RAW,
        value=json.dumps(f"2028-01-01"),
    )
    submission_level_extracted_data_fixture(
        submission_id=boss_submission.id,
        file_id=email_file.id,
        field=EntityInformation.BROKERAGE_ID,
        source_details=SourceDetails.FILE_CONTENT_RAW,
        value=json.dumps(hhm_brokerage.id, default=str),
    )
    submission_level_extracted_data_fixture(
        submission_id=boss_submission.id,
        file_id=email_file.id,
        field=EntityInformation.BROKER_ID,
        source_details=SourceDetails.FILE_CONTENT_RAW,
        value=json.dumps(hhm_broker.id, default=str),
    )

    db.session.commit()

    consolidation_process = SubmissionConsolidationProcess()
    consolidate_submission_level_data(boss_submission, consolidation_process)

    db.session.refresh(boss_submission)

    assert len(boss_submission.assigned_underwriters) == 0
    assert boss_submission.broker_id == hhm_broker.id
    assert boss_submission.brokerage_id == hhm_brokerage.id
    assert boss_submission.is_renewal is False
    assert boss_submission.proposed_effective_date == datetime(2027, 1, 1)
    assert boss_submission.policy_expiration_date == datetime(2028, 1, 1)

    # lets add api data
    submission_level_extracted_data_fixture(
        submission_id=boss_submission.id,
        file_id=None,
        field=EntityInformation.UNDERWRITER_USER_IDS,
        source_details=SourceDetails.API,
        value=json.dumps([23]),
    )
    submission_level_extracted_data_fixture(
        submission_id=boss_submission.id,
        file_id=None,
        field=EntityInformation.IS_RENEWAL,
        source_details=SourceDetails.API,
        value=json.dumps(True),
    )
    submission_level_extracted_data_fixture(
        submission_id=boss_submission.id,
        file_id=None,
        field=EntityInformation.POLICY_EFFECTIVE_START_DATE,
        source_details=SourceDetails.API,
        value=json.dumps("2028-01-01"),
    )
    submission_level_extracted_data_fixture(
        submission_id=boss_submission.id,
        file_id=None,
        field=EntityInformation.POLICY_END_DATE,
        source_details=SourceDetails.API,
        value=json.dumps("2029-01-01"),
    )
    submission_level_extracted_data_fixture(
        submission_id=boss_submission.id,
        file_id=None,
        field=EntityInformation.BROKERAGE_ID,
        source_details=SourceDetails.API,
        value=json.dumps(bb_brokers.id, default=str),
    )
    submission_level_extracted_data_fixture(
        submission_id=boss_submission.id,
        file_id=None,
        field=EntityInformation.BROKER_ID,
        source_details=SourceDetails.API,
        value=json.dumps(saul_goodman.id, default=str),
    )

    db.session.commit()

    consolidation_process = SubmissionConsolidationProcess()
    consolidate_submission_level_data(boss_submission, consolidation_process)

    db.session.refresh(boss_submission)

    assert len(boss_submission.assigned_underwriters) == 1
    assert boss_submission.assigned_underwriters[0].user_id == nw_underwriter.id
    assert boss_submission.broker_id == saul_goodman.id
    assert boss_submission.brokerage_id == bb_brokers.id
    assert boss_submission.is_renewal is True
    assert boss_submission.proposed_effective_date == datetime(2028, 1, 1)
    assert boss_submission.policy_expiration_date == datetime(2029, 1, 1)
