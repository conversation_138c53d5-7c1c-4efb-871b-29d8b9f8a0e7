from collections import OrderedDict
from pathlib import Path

from sqlalchemy.engine.row import Row

from copilot.models.reports import *
from tests.integration.factories import (
    ask_questions_fixture,
    audit_trail_fixture,
    boss_uw_mapping_fixture,
    broker_client_code_fixture,
    broker_fixture,
    brokerage_client_code_fixture,
    brokerage_fixture,
    client_application_fixture,
    copilot_worker_execution_event_fixture,
    coverage_fixture,
    custom_file_type_fixture,
    email_classification_fixture,
    email_classification_label_fixture,
    email_classifier_fixture,
    email_fixture,
    email_status_fixture,
    email_template_fixture,
    enhanced_file_fixture,
    execution_event_fixture,
    feedback_fixture,
    file_extracted_metadata_fixture,
    file_fixture,
    file_label_studio_fixture,
    file_metric_fixture,
    hub_template_fixture,
    ifta_data_fixture,
    lob_carrier_fixture,
    lob_fixture,
    loss_fixture,
    loss_policy_fixture,
    loss_run_sensible_extraction_document_fixture,
    metric_group_fixture,
    metric_preference_fixture,
    metric_preferences_templates_fixture,
    metric_source_fixture,
    metric_template_fixture,
    metric_v2_fixture,
    naics_code_fixture,
    notebook_message_fixture,
    notebook_thread_fixture,
    org_metric_config_fixture,
    organization_fixture,
    paragon_company_line_fixture,
    paragon_underwriter_fixture,
    paragon_wc_uw_mapping_fixture,
    policy_creation_fixture,
    processed_file_fixture,
    quality_audit_questions_fixture,
    read_submission_fixture,
    report_alert_fixture,
    report_and_submission_fixture,
    report_bundle_fixture,
    report_email_correspondence_fixture,
    report_fixture,
    report_link_fixture,
    report_processing_dependency_fixture,
    report_shadow_dependency_fixture,
    report_summary_preference_fixture,
    report_triage_fixture,
    routing_rule_fixture,
    scheduled_email_fixture,
    sensible_calls_fixture,
    sensible_document_response_cache_fixture,
    sensible_extraction_document_fixture,
    sensible_extraction_fixture,
    sensible_quota_fixture,
    settings_fixture,
    shareholder_fixture,
    stuck_details_fixture,
    stuck_submission_feedback_fixture,
    submission_bookmark_fixture,
    submission_brokerage_employee_fixture,
    submission_brokerage_fixture,
    submission_business_fixture,
    submission_clearing_issue_fixture,
    submission_client_id_fixture,
    submission_coverage_fixture,
    submission_deductible_fixture,
    submission_field_source_fixture,
    submission_files_data_fixture,
    submission_fixture,
    submission_identifier_fixture,
    submission_identifiers_suggestion_fixture,
    submission_level_extracted_data_fixture,
    submission_note_fixture,
    submission_premises_fixture,
    submission_priority_fixture,
    submission_processing_fixture,
    submission_relation_fixture,
    submission_sync_fixture,
    submission_sync_identifiers_fixture,
    submission_sync_matcher_data_fixture,
    submission_sync_report_fixture,
    submission_user_fixture,
    summary_preference_fixture,
    support_user_file_fixture,
    support_user_fixture,
    support_user_report_fixture,
    sync_matching_result_fixture,
    sync_matching_run_fixture,
    task_definition_fixture,
    task_definition_model_fixture,
    task_execution_fixture,
    task_fixture,
    task_model_fixture,
    tenant_feedback_fixture,
    underlying_policy_fixture,
    update_businesses_log_fixture,
    user_action_fixture,
    user_fixture,
    user_group_fixture,
    verification_check_fixture,
    workers_comp_experience_fixture,
    workers_comp_state_rating_info_fixture,
)
from tests.integration.utils import AnonObj

TABLES_TO_SKIP = [
    "client_submission_stage_config",
    "brokers",
    "brokerages",
    "correspondence_contact",
    "alembic_version",
    "coverages_copy",
    "awsdms_ddl_audit",
    "customizable_classifier_test_runs",
    "customizable_classifiers",
    "customizable_classifiers_v2",
    "classifier_version_task_dataset",
    "classifier_versions",
    "classifier_config",
    "filter_rules",
    "classifier_config_versions",
    "classifier_version_task_definition",
    "phrases_with_llm_config_versions",
    "phrases_config_versions",
    "llm_config_versions",
    "classifier_phrases",
    "classifier_to_config_version",
    "dossiers",
    "experiments",
    "experiment_runs",
    "experiment_samples",
    "form_definition",
    "form_field",
    "form_field_source",
    "form_field_source_value",
    "form_field_value",
    "form_v2",
    "loss_copy",
    "phrase_matching_classifier_phrase",
    "quote",
    "report_permissions",
    "submission_audit",
    "submission_coverages_copy",
    "submission_deductibles_copy",
    "submission_history",
    "subscription",
    "subtypes_benchmark_data",
    "sync_configuration",
    "test_run_classification_task",
    "user_open_reports",
    "user_to_group",
    "org_to_brokerage_employee",
    "org_to_brokerage",
    "sendgrid_email_data",
    "user_submission_notification",
    "alerts",
    "paragon_producers",
    "metric_v2",
    "metric_preference",
    "metric_source",
    "paragon_ims_daily_missing_links_audit",
    "broker_group",
    "broker_group_mappings",
    "conifer_uw_mappings",
    "aig_uw_mappings",
    "admiral_uw_mapping_assignments",
    "admiral_uw_mappings",
    "admiral_assignment_logs",
    "uw_assignment_logs",
    "bowhead_uw_mappings",
    "data_validation_results",
    "resolved_business_entries",
    "ers_search_results",
    "edited_values",
    "edited_fields",
    "changed_fact_subtypes",
    "pds_stats",
    "submission_consolidation_process",
    "report_snapshot",
    "preferences",
    "attachments_based_email_classifiers",
    "customizable_email_classifiers",
    "task_scoring",
    "task_scoring_execution",
    "fact_subtype_selection_benchmark",
    "llm_version",
    "submission_clearing_sub_status",
    "broker_client_codes",
    "taxonomy_mappings",
    "taxonomy_descriptions",
    "task_definition_metrics",
    "task_model_metrics",
    "sync_matching_runs",
    "sync_matching_results",
    "submission_identifiers_suggestions",
    "submission_recommendation_results",
    "secura_uw_mappings",
    "task_dataset",
    "task_dataset_input",
    "task_dataset_model_outcome",
    "task_dataset_ground_truth",
    "task_dataset_execution",
    "task_cost_limit",
    "task_definition_audit",
    "brokerage_client_codes",
    "integration_logs",
    "async_operations",
    "code_based_email_classifiers",
]
TABLES_NOT_COPIED = [
    "metric_v2",
    "metric_preference",
    "metric_source",
    "paragon_producers",
    "report_shadow_dependency",
    "files_extracted_metadata",
    "correspondence_contact",
    "brokerage_employees",
    "brokerages_v2",
    "organization",
    "policy",
    "brokers",
    "report_email_correspondence",
    "submissions_client_ids",
    "brokerages",
    "report_bundle",
    "update_businesses_log",
    "report_triage",
    "stuck_submission_feedback",
    "submission_sync",
    "submission_recommendation_results",
    "sensible_document_response_cache",
    "sensible_extraction",
    "users",
    "report_processing_dependency",
    "org_metric_config",
    "quality_audit_questions",
    "email_status",
    "lob_carrier",
    "lob",
    "ask_questions",
    "boss_uw_mappings",
    "conifer_uw_mappings",
    "verification_checks",
    "metric_group",
    "naics_code",
    "sensible_quota",
    "summary_preference",
    "coverages",
    "emails",
    "submission_files_data",
    "submission_priority",
    "user_group",
    "support_user_report",
    "support_users",
    "report_alerts",
    "report_summary_preference",
    "routing_rule",
    "sensible_calls",
    "sensible_extraction_document",
    "settings",
    "user_action",
    "notebook_thread",
    "tenant_feedback",
    "client_application",
    "copilot_worker_execution_event",
    "files_label_studio",
    "paragon_underwriters",
    "paragon_company_lines",
    "feedback",
    "scheduled_emails",
    "audit_trails",
    "email_templates",
    "hub_templates",
    "loss_run_sensible_extraction_document",
    "metric_preferences_templates",
    "stuck_details",
    "metric_template",
    "notebook_message",
    "paragon_wc_uw_mappings",
    "submission_sync_matcher_data",
    "submission_processing",
    "client_submission_stage_config",
    "submission_sync_identifiers",
    "submission_sync_report",
    "aig_uw_mappings",
    "admiral_uw_mapping_assignments",
    "admiral_uw_mappings",
    "admiral_assignment_logs",
    "uw_assignment_logs",
    "bowhead_uw_mappings",
    "task_definition",
    "task_model",
    "task_definition_model",
    "task",
    "task_execution",
    "submission_identifiers",
    "custom_file_type",
    "email_classification_labels",
    "email_classifiers",
    "email_classifications",
    "support_user_files",
    "submission_clearing_sub_status",
    "secura_uw_mappings",
    "submission_field_source",
]

DEFAULT_COLUMNS_TO_SKIP = {"id", "created_at", "updated_at"}
COLUMNS_TO_SKIP = {
    "files_extracted_metadata": {"checksum"},
    "users": {"redirect_user_id"},
    "reports_v2": {
        "organization_permission_level",
        "rush_source",
        "shadow_type",
        "org_group",
        "is_email_classification_enabled",
    },
    "submissions": {
        "acord_125",
        "copilot_2_mode_id",
        "is_copilot_2_mode_frozen",
        "parent_id",
        "stuck_reason",
        "broker_id",
        "frozen_as_of",
        "is_escalated",
        "agent_id",
        "agency_id",
        "correspondence_contact_id",
        "payroll",
        "client_stage_id",
        "client_stage",
        "client_stage_comment",
        "client_clearing_status",
        "clearing_status",
        "prevent_clearing_updates",
        "client_recommendations_score",
        "facts_config",
    },
    "submission_businesses": {"entity_type"},
    "files": {
        "parent_file_id",
        "internal_notes",
        "issue",
        "replaced_by_file_ids",
        "execution_arn",
        "retry_count",
        "processing_state_updated_at",
        "description",
        "client_tags",
        "hidden",
        "cache_origin_file_id",
        "custom_file_type_id",
        "custom_classification",
        "user_shadow_state",
    },
    "loss": {"merged_parent_id", "evidence"},
    "loss_policy": {"evidence"},
    "settings": {"is_clarion_door_enabled", "decline_reasons"},
    "coverages": {"groups"},
    "boss_uw_mappings": {"broker_id", "brokerage_id"},
    "submission_note": {"canonical_id", "is_note", "original_note_id"},
    "submission_level_extracted_data": {"parent_id"},
    "task": {"output_task_execution_id", "task_dataset_execution_id"},
    "task_execution": {"validated_task_execution_id"},
    "ifta_data": {"evidence"},
    "workers_comp_experience": {"evidence"},
    "workers_comp_state_rating_info": {"evidence"},
    "task_definition_model": {"only_organization_ids", "except_organization_ids"},
    "submission_premises": {"submission_id"},
    "submission_coverages": {"rating_premium"},
}
COLUMNS_NOT_COPIED = {
    "submissions": {
        "send_decline_email_error",
        "decline_email_tracking_id",
        "decline_email_delivered",
        "decline_email_cc",
        "decline_custom_template",
        "decline_attachments",
        "is_waiting_for_auto_verify",
        "verified_at",
        "auto_verified_at",
        "manual_verified_at",
        "audited_at",
        "audited_by",
        "missing_data_status",
        "missing_documents",
        "decline_custom_subject",
        "payroll",
        "is_pre_renewal",
        "is_renewal_shell",
        "facts_config",
        "stage_details",
    },
    "loss": {"coverage_id"},
    "reports_v2": {"is_rush", "is_user_waiting_for_shadow"},
    "files": {"external_identifier"},
}
NOT_NULL_COLUMNS_AFTER_COPY = {
    "submission_coverages": {"source", "source_details"},
    "submissions": {
        "is_auto_processed",
        "is_for_audit",
    },
    "submission_businesses": {"serial_id"},
    "reports_v2": {"is_copy"},
    "loss": {"partial_hash", "full_hash"},
    "submission_brokerage_employees": {"source"},
    "submission_brokerage": {"source"},
    "submission_relations": {"source"},
}

GET_TABLE_NAMES_SQL = """
SELECT tablename
FROM pg_catalog.pg_tables
WHERE schemaname != 'pg_catalog'
  AND schemaname != 'information_schema'
  AND tablename not in :tables_to_skip
"""

FIXTURE_MAPPINGS = OrderedDict(
    {
        "organization": organization_fixture,
        "users": user_fixture,
        "audit_trails": audit_trail_fixture,
        "brokerages_v2": brokerage_fixture,
        "brokerage_employees": broker_fixture,
        "boss_uw_mappings": boss_uw_mapping_fixture,
        "client_application": client_application_fixture,
        "coverages": coverage_fixture,
        "email_templates": email_template_fixture,
        "report_email_correspondence": report_email_correspondence_fixture,
        "emails": email_fixture,
        "email_status": email_status_fixture,
        "report_bundle": report_bundle_fixture,
        "lob": lob_fixture,
        "report_and_submission": report_and_submission_fixture,
        "execution_event": execution_event_fixture,
        "feedback": feedback_fixture,
        "ask_questions": ask_questions_fixture,
        "submission_businesses": submission_business_fixture,
        "copilot_worker_execution_event": copilot_worker_execution_event_fixture,
        "files": file_fixture,
        "file_metrics": file_metric_fixture,
        "files_extracted_metadata": file_extracted_metadata_fixture,
        "files_label_studio": file_label_studio_fixture,
        "hub_templates": hub_template_fixture,
        "ifta_data": ifta_data_fixture,
        "lob_carrier": lob_carrier_fixture,
        "loss_policy": loss_policy_fixture,
        "loss": loss_fixture,
        "sensible_extraction": sensible_extraction_fixture,
        "sensible_extraction_document": sensible_extraction_document_fixture,
        "loss_run_sensible_extraction_document": loss_run_sensible_extraction_document_fixture,
        "metric_group": metric_group_fixture,
        "metric_preference": metric_preference_fixture,
        "metric_v2": metric_v2_fixture,
        "metric_source": metric_source_fixture,
        "metric_template": metric_template_fixture,
        "metric_preferences_templates": metric_preferences_templates_fixture,
        "naics_code": naics_code_fixture,
        "notebook_thread": notebook_thread_fixture,
        "notebook_message": notebook_message_fixture,
        "org_metric_config": org_metric_config_fixture,
        "paragon_company_lines": paragon_company_line_fixture,
        "paragon_underwriters": paragon_underwriter_fixture,
        "policy": policy_creation_fixture,
        "processed_files": processed_file_fixture,
        "quality_audit_questions": quality_audit_questions_fixture,
        "read_submission": read_submission_fixture,
        "report_alerts": report_alert_fixture,
        "report_link": report_link_fixture,
        "report_processing_dependency": report_processing_dependency_fixture,
        "report_shadow_dependency": report_shadow_dependency_fixture,
        "summary_preference": summary_preference_fixture,
        "report_summary_preference": report_summary_preference_fixture,
        "report_triage": report_triage_fixture,
        "routing_rule": routing_rule_fixture,
        "scheduled_emails": scheduled_email_fixture,
        "sensible_calls": sensible_calls_fixture,
        "sensible_document_response_cache": sensible_document_response_cache_fixture,
        "sensible_quota": sensible_quota_fixture,
        "settings": settings_fixture,
        "stuck_details": stuck_details_fixture,
        "stuck_submission_feedback": stuck_submission_feedback_fixture,
        "submission_bookmarks": submission_bookmark_fixture,
        "submission_clearing_issue": submission_clearing_issue_fixture,
        "submission_coverages": submission_coverage_fixture,
        "underlying_policy": underlying_policy_fixture,
        "submission_deductibles": submission_deductible_fixture,
        "submission_files_data": submission_files_data_fixture,
        "submission_note": submission_note_fixture,
        "submission_priority": submission_priority_fixture,
        "submission_sync": submission_sync_fixture,
        "submissions_client_ids": submission_client_id_fixture,
        "submissions_users": submission_user_fixture,
        "support_users": support_user_fixture,
        "support_user_report": support_user_report_fixture,
        "tenant_feedback": tenant_feedback_fixture,
        "update_businesses_log": update_businesses_log_fixture,
        "user_action": user_action_fixture,
        "user_group": user_group_fixture,
        "verification_checks": verification_check_fixture,
        "workers_comp_experience": workers_comp_experience_fixture,
        "workers_comp_state_rating_info": workers_comp_state_rating_info_fixture,
        "paragon_wc_uw_mappings": paragon_wc_uw_mapping_fixture,
        "submission_sync_matcher_data": submission_sync_matcher_data_fixture,
        "submission_brokerage_employees": submission_brokerage_employee_fixture,
        "submission_brokerage": submission_brokerage_fixture,
        "submission_relations": submission_relation_fixture,
        "submission_identifiers": submission_identifier_fixture,
        "submission_level_extracted_data": submission_level_extracted_data_fixture,
        "submission_processing": submission_processing_fixture,
        "shareholders": shareholder_fixture,
        "submission_sync_identifiers": submission_sync_identifiers_fixture,
        "submission_sync_report": submission_sync_report_fixture,
        "task_definition": task_definition_fixture,
        "task_model": task_model_fixture,
        "task_definition_model": task_definition_model_fixture,
        "task": task_fixture,
        "task_execution": task_execution_fixture,
        "custom_file_type": custom_file_type_fixture,
        "email_classification_labels": email_classification_label_fixture,
        "email_classifiers": email_classifier_fixture,
        "email_classifications": email_classification_fixture,
        "support_user_files": support_user_file_fixture,
        "broker_client_codes": broker_client_code_fixture,
        "sync_matching_runs": sync_matching_run_fixture,
        "sync_matching_results": sync_matching_result_fixture,
        "submission_identifiers_suggestions": submission_identifiers_suggestion_fixture,
        "enhanced_files": enhanced_file_fixture,
        "brokerage_client_codes": brokerage_client_code_fixture,
        "submission_field_source": submission_field_source_fixture,
        "submission_premises": submission_premises_fixture,
    }
)

CURRENT_DIR = Path(__file__).parent


def _get_columns_to_skip(table_name: str) -> set[str]:
    return DEFAULT_COLUMNS_TO_SKIP.union(COLUMNS_TO_SKIP.get(table_name, set()))


def _get_columns_not_copied(table_name: str) -> set[str]:
    return COLUMNS_NOT_COPIED.get(table_name, set())


def _get_columns_not_null(table_name: str) -> set[str]:
    return NOT_NULL_COLUMNS_AFTER_COPY.get(table_name, set())


def to_camel_case(snake_str):
    return "".join(x.capitalize() for x in snake_str.lower().split("_"))


def _populate_database():
    with open(CURRENT_DIR / "../../data/duplication/full_db.json") as f:
        json_db = json.load(f)

    for key, fixture in FIXTURE_MAPPINGS.items():
        for fix_value in json_db[key]:
            if key == "report_and_submission":
                # special case and only one
                # if there are more a recursive solution is needed
                report = report_fixture(**fix_value["report"])
                submission = submission_fixture(**fix_value["submission"], report=report)
                fixture(report=report, submission=submission)
            else:
                fixture(**fix_value)
            db.session.commit()


def _check_every_table_has_number_n_records(
    n: int = 0,
    tables_to_number_of_rows: dict[str, int] = None,
) -> list[str]:
    rows = db.session.execute(GET_TABLE_NAMES_SQL, {"tables_to_skip": tuple(TABLES_TO_SKIP)}).fetchall()
    table_names = [r[0] for r in rows]
    for tn in table_names:
        num_rows = db.session.execute(f"select count(*) from {tn}").fetchall()
        assert len(num_rows) == 1
        number_of_rows = num_rows[0][0]
        assert number_of_rows == (tables_to_number_of_rows or {}).get(tn, n), f"failed for {tn}"
    return table_names


def _check_table_row_has_fields_full(table_name: str):
    rows: list[Row] = db.session.execute(f"select * from {table_name}").fetchall()
    row = rows[0]
    cols_to_skip = _get_columns_to_skip(table_name)
    for col in [c for c in list(row.keys()) if c not in cols_to_skip]:
        assert row[col] is not None, f"failed for {table_name} and {col}"


def _check_report_link_equality():
    rows: list[Row] = db.session.execute("select * from report_link").fetchall()
    assert len(rows) == 5


def _has_created_at(table_name: str) -> bool:
    query = "SELECT column_name FROM information_schema.columns WHERE table_name = :table_name"
    rows = db.session.execute(query, {"table_name": table_name}).fetchall()
    col_names = {r[0] for r in rows}
    return "created_at" in col_names


def _assert_equality(rows: list[Row], expected_number_of_rows: int, table_name: str, old_to_new_id: dict):
    assert len(rows) == expected_number_of_rows, f"Failed for {table_name}"
    original = rows[0]
    duplicate = rows[-1]

    cols_to_skip = _get_columns_to_skip(table_name)
    not_null_cols = _get_columns_not_null(table_name)
    cols_not_copied = _get_columns_not_copied(table_name)
    for key in list(original.keys()):
        if key in cols_to_skip:
            continue

        # it could be written in a single statement,
        # but it will be long and it may be confusing
        if key in cols_not_copied:
            assert original[key] is not None
            assert duplicate[key] is None
        elif key in not_null_cols:
            assert original[key] is not None
            assert duplicate[key] is not None
        elif isinstance(original[key], dict) or isinstance(duplicate[key], dict):
            assert original[key] == duplicate[key]
        elif isinstance(original[key], list) or isinstance(duplicate[key], list):
            assert original[key] == duplicate[key]
        elif original[key] in old_to_new_id:
            assert original[key] is not None
            assert duplicate[key] is not None
        else:
            assert original[key] == duplicate[key]


def _check_submission_relations_equality(old_to_new_id: dict):
    rows: list[Row] = db.session.execute("select * from submission_relations order by created_at").fetchall()
    assert len(rows) == 2

    rows: list[Row] = db.session.execute(
        "select * from submission_relations where type = 'COPY' order by created_at"
    ).fetchall()
    assert len(rows) == 1


def _check_table_duplicates_equality(table_name: str, old_to_new_id: dict):
    query = f"select * from {table_name}"

    if _has_created_at(table_name):
        query = f"{query} order by created_at"

    expected_rows = 2
    logger.info("checking table", table_name=table_name)

    if table_name == "reports_v2" or table_name == "submissions":
        expected_rows = 3

    if table_name == "report_link":
        return _check_report_link_equality()

    if table_name == "submission_relations":
        return _check_submission_relations_equality(old_to_new_id)

    rows: list[Row] = db.session.execute(query).fetchall()

    if table_name in TABLES_NOT_COPIED:
        assert len(rows) == 1
        return

    _assert_equality(rows, expected_rows, table_name, old_to_new_id)


def test_report_duplication(app_context, request_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            is_being_impersonated=False,
            email="test",
        ),
    )
    _check_every_table_has_number_n_records(0)
    _populate_database()
    table_names = _check_every_table_has_number_n_records(
        1,
        {
            "reports_v2": 2,
            "submissions": 2,
            "submission_relations": 1,
        },
    )

    for tn in table_names:
        _check_table_row_has_fields_full(tn)

    report: ReportV2 = ReportV2.query.get("5eccb523-79b8-4386-b8d0-06aac80c7a3a")

    old_to_new = {}
    duplicate = report.copy(old_to_new)
    db.session.add(duplicate)
    db.session.commit()

    for tn in table_names:
        _check_table_duplicates_equality(tn, old_to_new)
