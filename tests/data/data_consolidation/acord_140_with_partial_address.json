{"files": ["53762994-1c69-43a5-b681-2aa84d06e2b1"], "fields": [{"name": "Property Description", "values": [{"value": "Spoilage/Special form", "entity_idx": 1, "observed_value": "Spoilage/Special form", "validation_result": {"is_valid": true}}], "value_type": "TEXT", "display_as_fact": true, "fact_subtype_id": "PROPERTY_DESCRIPTION", "fact_subtype_suggestions": []}, {"name": "Construction class", "values": [{"value": "Class 1 – Frame", "evidences": [{"page": 1, "type": "PDFEvidence", "confidence": 0.9983476257324219, "observed_value_bbox": {"xmax": 0.0708235294117647, "xmin": 0.03705882352941176, "ymax": 0.6008181818181818, "ymin": 0.5933636363636364}}], "entity_idx": 1, "observed_value": "<PERSON>ame", "validation_result": {"is_valid": true}}], "value_type": "TEXT", "display_as_fact": true, "fact_subtype_id": "CONSTRUCTION_CLASS", "fact_subtype_suggestions": []}, {"name": "Number of Stories", "values": [{"value": 1, "evidences": [{"page": 1, "type": "PDFEvidence", "confidence": 0.9973499298095703, "observed_value_bbox": {"xmax": 0.707529411764706, "xmin": 0.7031764705882353, "ymax": 0.6006363636363636, "ymin": 0.5935454545454545}}], "entity_idx": 1, "observed_value": "1", "validation_result": {"is_valid": true}}], "value_type": "INTEGER", "display_as_fact": true, "fact_subtype_id": "NUMBER_OF_STORIES", "fact_subtype_suggestions": []}, {"name": "Number of Basements", "values": [{"value": "1", "evidences": [{"page": 1, "type": "PDFEvidence", "confidence": 0.9961394500732422, "observed_value_bbox": {"xmax": 0.7889411764705883, "xmin": 0.7394117647058823, "ymax": 0.6005454545454545, "ymin": 0.5787272727272726}}], "entity_idx": 1, "observed_value": "1", "validation_result": {"is_valid": true, "warning_message": "Value 1.0 is above upper bound 0.47999999999999954"}}], "value_type": "INTEGER", "display_as_fact": true, "fact_subtype_id": "NUMBER_OF_BASEMENTS", "fact_subtype_suggestions": []}, {"name": "Year Built", "values": [{"value": "1965", "evidences": [{"page": 1, "type": "PDFEvidence", "confidence": 0.999395980834961, "observed_value_bbox": {"xmax": 0.8392941176470589, "xmin": 0.8136470588235295, "ymax": 0.6008181818181818, "ymin": 0.5934545454545455}}], "entity_idx": 1, "observed_value": "1965", "validation_result": {"is_valid": true}}], "value_type": "DATETIME", "display_as_fact": true, "fact_subtype_id": "YEAR_BUILT", "fact_subtype_suggestions": []}, {"name": "Building Area", "values": [{"value": "5289", "evidences": [{"page": 1, "type": "PDFEvidence", "confidence": 0.999620361328125, "observed_value_bbox": {"xmax": 0.8921176470588236, "xmin": 0.8662352941176471, "ymax": 0.6008181818181818, "ymin": 0.5935454545454545}}], "entity_idx": 1, "observed_value": "5289.0", "validation_result": {"is_valid": true}}], "value_type": "NUMBER_ARRAY", "display_as_fact": true, "fact_subtype_id": "BUILDING_SIZE", "fact_subtype_suggestions": []}, {"name": "Wiring building improvements?", "values": [{"value": "Yes", "entity_idx": 1, "observed_value": "True", "validation_result": {"is_valid": true}}], "value_type": "BOOLEAN", "display_as_fact": true, "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING", "fact_subtype_suggestions": []}, {"name": "Year of wiring improvements", "values": [{"value": "2010", "evidences": [{"page": 1, "type": "PDFEvidence", "confidence": 0.982314453125, "observed_value_bbox": {"xmax": 0.15117647058823527, "xmin": 0.05858823529411765, "ymax": 0.6355454545454545, "ymin": 0.6238181818181818}}], "entity_idx": 1, "observed_value": "2010", "validation_result": {"is_valid": true}}], "value_type": "DATETIME", "display_as_fact": true, "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR", "fact_subtype_suggestions": []}, {"name": "Plumbing building improvements?", "values": [{"value": "Yes", "entity_idx": 1, "observed_value": "True", "validation_result": {"is_valid": true}}], "value_type": "BOOLEAN", "display_as_fact": true, "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING", "fact_subtype_suggestions": []}, {"name": "Year of plumbing improvements", "values": [{"value": "2010", "evidences": [{"page": 1, "type": "PDFEvidence", "confidence": 0.9998138427734375, "observed_value_bbox": {"xmax": 0.2987058823529412, "xmin": 0.27188235294117646, "ymax": 0.6311818181818182, "ymin": 0.6238181818181818}}], "entity_idx": 1, "observed_value": "2010", "validation_result": {"is_valid": true}}], "value_type": "DATETIME", "display_as_fact": true, "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR", "fact_subtype_suggestions": []}, {"name": "Roofing building improvements?", "values": [{"value": "Yes", "entity_idx": 1, "observed_value": "True", "validation_result": {"is_valid": true}}], "value_type": "BOOLEAN", "display_as_fact": true, "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING", "fact_subtype_suggestions": []}, {"name": "Year of roofing improvements", "values": [{"value": "2012", "evidences": [{"page": 1, "type": "PDFEvidence", "confidence": 0.9798735046386718, "observed_value_bbox": {"xmax": 0.1572941176470588, "xmin": 0.059176470588235296, "ymax": 0.6506363636363637, "ymin": 0.639}}], "entity_idx": 1, "observed_value": "2012", "validation_result": {"is_valid": true}}], "value_type": "DATETIME", "display_as_fact": true, "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR", "fact_subtype_suggestions": []}, {"name": "Heating building improvements?", "values": [{"value": "Yes", "entity_idx": 1, "observed_value": "True", "validation_result": {"is_valid": true}}], "value_type": "BOOLEAN", "display_as_fact": true, "fact_subtype_id": "BUILDING_IMPROVEMENTS_HEATING", "fact_subtype_suggestions": []}, {"name": "Year of heating improvements", "values": [{"value": "2024", "evidences": [{"page": 1, "type": "PDFEvidence", "confidence": 0.9566644287109375, "observed_value_bbox": {"xmax": 0.29247058823529415, "xmin": 0.19741176470588234, "ymax": 0.6505454545454545, "ymin": 0.6389090909090909}}], "entity_idx": 1, "observed_value": "2024", "validation_result": {"is_valid": true, "warning_message": "Value 2024.0 is above upper bound 2023.0"}}], "value_type": "DATETIME", "display_as_fact": true, "fact_subtype_id": "BUILDING_IMPROVEMENTS_HEATING_YEAR", "fact_subtype_suggestions": []}, {"name": "Burglar Alarm Type", "values": [{"value": "Hold Up", "evidences": [{"page": 1, "type": "PDFEvidence", "confidence": 0.9984056091308594, "observed_value_bbox": {"xmax": 0.08023529411764707, "xmin": 0.03670588235294118, "ymax": 0.769, "ymin": 0.7602727272727272}}], "entity_idx": 1, "observed_value": "Hold-Up", "validation_result": {"is_valid": true}}], "value_type": "TEXT", "display_as_fact": true, "fact_subtype_id": "BURGLAR_ALARM_TYPE", "fact_subtype_suggestions": []}, {"name": "Sprinklers area coverage percentage", "values": [{"value": "100.0", "evidences": [{"page": 1, "type": "PDFEvidence", "confidence": 0.9996311950683594, "observed_value_bbox": {"xmax": 0.5036470588235293, "xmin": 0.4849411764705882, "ymax": 0.8280000000000001, "ymin": 0.8210000000000001}}], "entity_idx": 1, "observed_value": "100.0", "validation_result": {"is_valid": true}}], "value_type": "INTEGER", "display_as_fact": true, "fact_subtype_id": "SPRINKLER_AREA_COVERAGE_PERCENT", "fact_subtype_suggestions": []}, {"name": "Occupancy Class (Derived)", "values": [{"value": "Other", "entity_idx": 1, "observed_value": "Other", "validation_result": {"is_valid": true}}], "value_type": "TEXT", "display_as_fact": true, "fact_subtype_id": "OCCUPANCY_CLASS", "fact_subtype_suggestions": []}, {"name": "Occupancy Type (Derived)", "values": [{"value": "Other", "entity_idx": 1, "observed_value": "Other", "validation_result": {"is_valid": true}}], "value_type": "TEXT", "display_as_fact": true, "fact_subtype_id": "OCCUPANCY_TYPE", "fact_subtype_suggestions": []}], "entities": [{"id": "smokehaus ferndale llc 177 vester, loc:1", "type": "Business", "acord_location_information": {"building_number": null, "location_number": 1}}, {"id": "2f831d0d-7442-4913-be64-7895ccd4f3fe", "type": "Structure", "resolved": true, "remote_id": "1", "parent_idx": 0, "acord_location_information": {"building_number": 1, "location_number": 1}}, {"id": "a87b46b6-63d0-478e-b501-6826a3a47fc2", "type": "Submission", "resolved": true}], "transient_data": {"named_insured": "Smokehaus Ferndale LLC", "premises_info": [{"requested_name": "Smokehaus Ferndale LLC", "building_number": 1, "location_number": 1, "requested_address": "177 Vester", "premises_information_table": [{"amount": 50000.0, "valuation": "RC", "deductible": 1000.0, "causes_of_loss": "Special form", "coins_percentage": 80.0, "subject_of_insurance": "Tenants Improvements and betterments"}, {"amount": 50000.0, "valuation": "RC", "deductible": 1000.0, "causes_of_loss": "Special form", "coins_percentage": 80.0, "subject_of_insurance": "Business Personal Property"}, {"amount": 12.0, "valuation": "ALS", "causes_of_loss": "Special form", "subject_of_insurance": "BI w/ Extra Expense", "forms_and_conditions_to_apply": "0 hr wait period"}, {"amount": 5000.0, "deductible": 1000.0, "causes_of_loss": "Special form", "subject_of_insurance": "Sign Coverage"}]}], "file_classification": "ACORD_140"}, "additional_data": {"fe_properties": null, "finished_do_sub_step": null}, "entity_information": [{"name": "Name", "values": [{"value": "Smokehaus Ferndale LLC", "entity_idx": 0}, {"value": "Smokehaus Ferndale LLC", "entity_idx": 1}], "value_type": "TEXT", "fact_subtype_suggestions": []}, {"name": "Address", "values": [{"value": "177 Vester", "entity_idx": 0}, {"value": "177 Vester", "entity_idx": 1}], "value_type": "TEXT", "fact_subtype_suggestions": []}]}