{"files": ["d3d6b20d-97fb-4adf-9dc7-7f2383ad8cd4"], "fields": [{"name": "GL Code", "values": [{"value": "16916", "evidences": [{"page": 1, "type": "PDFEvidence", "confidence": 0.995, "observed_value_bbox": {"xmax": 0.5398823529411765, "xmin": 0.5084235294117647, "ymax": 0.6475727272727273, "ymin": 0.6384545454545454}}], "entity_idx": 0, "observed_value": "16916", "validation_result": {"is_valid": true}}], "value_type": "INTEGER", "display_as_fact": true, "fact_subtype_id": "GL_CODE", "fact_subtype_suggestions": []}, {"name": "Federal Employer Identification Number", "values": [{"value": "332267651", "evidences": [{"page": 1, "type": "PDFEvidence", "confidence": 0.984, "observed_value_bbox": {"xmax": 0.950564705882353, "xmin": 0.8601176470588235, "ymax": 0.6475727272727273, "ymin": 0.6241363636363636}}], "entity_idx": 0, "observed_value": "332267651", "validation_result": {"is_valid": true}}], "value_type": "TEXT", "display_as_fact": true, "fact_subtype_id": "FEIN", "fact_subtype_suggestions": []}, {"name": "In City Limits", "values": [{"value": "Yes", "entity_idx": 2, "observed_value": "True", "validation_result": {"is_valid": true}}], "value_type": "BOOLEAN", "display_as_fact": true, "fact_subtype_id": "IN_CITY_LIMITS", "fact_subtype_suggestions": []}, {"name": "Premises Interest Type", "values": [{"value": "Tenant", "entity_idx": 2, "observed_value": "Tenant", "validation_result": {"is_valid": true}}], "value_type": "TEXT", "display_as_fact": true, "fact_subtype_id": "PREMISES_INTEREST_TYPE", "fact_subtype_suggestions": []}, {"name": "Building Area", "values": [{"value": "5289", "evidences": [{"page": 2, "type": "PDFEvidence", "confidence": 0.996, "observed_value_bbox": {"xmax": 0.9618, "xmin": 0.8539294117647058, "ymax": 0.21570909090909088, "ymin": 0.2066}}], "entity_idx": 2, "observed_value": "5289.0", "validation_result": {"is_valid": true}}], "value_type": "NUMBER_ARRAY", "display_as_fact": true, "fact_subtype_id": "BUILDING_SIZE", "fact_subtype_suggestions": []}, {"name": "Leased area?", "values": [{"value": "No", "evidences": [{"page": 2, "type": "PDFEvidence", "confidence": 0.876, "observed_value_bbox": {"xmax": 0.9303411764705882, "xmin": 0.9247176470588235, "ymax": 0.23047272727272727, "ymin": 0.22221818181818181}}], "entity_idx": 2, "observed_value": "False", "validation_result": {"is_valid": true}}], "value_type": "BOOLEAN", "display_as_fact": true, "fact_subtype_id": "LEASED_AREA", "fact_subtype_suggestions": []}, {"name": "Accommodation and Food Services", "values": [{"value": true, "evidences": [{"page": 2, "type": "PDFEvidence", "confidence": 0.849, "observed_value_bbox": {"xmax": 0.44231764705882354, "xmin": 0.4203411764705883, "ymax": 0.49170909090909093, "ymin": 0.4792363636363637}}], "entity_idx": 0}], "naics_code": "NAICS_72", "value_type": "BOOLEAN", "display_as_fact": true, "fact_subtype_suggestions": []}, {"name": "Operations Summary", "values": [{"value": "New Venture Restaurant W/ Alcohol Smokehaus is a premium, full-service restaurant concept specializing in high-end smoked meats infused with Southern flair. Located in a high-visibility area with an established dining base, Smokehaus is positioned directly", "evidences": [{"page": 2, "type": "PDFEvidence", "confidence": 0.991, "observed_value_bbox": {"xmax": 0.848870588235294, "xmin": 0.03538823529411765, "ymax": 0.586809090909091, "ymin": 0.5238727272727273}}], "entity_idx": 0, "observed_value": "New Venture Restaurant W/ Alcohol Smokehaus is a premium, full-service restaurant concept specializing in high-end smoked meats infused with Southern flair. Located in a high-visibility area with an established dining base, Smokehaus is positioned directly", "validation_result": {"is_valid": true}}], "value_type": "TEXT", "display_as_fact": true, "fact_subtype_id": "OPERATIONS", "fact_subtype_suggestions": []}], "entities": [{"id": "smokehaus ferndale llc 177 vester ferndale mi 48220", "type": "Business", "entity_named_insured": "FIRST_NAMED_INSURED"}, {"id": "smokehaus ferndale llc 177 vester ferndale wayne mi 48220, loc:1", "type": "Business", "acord_location_information": {"building_number": null, "location_number": 1}}, {"id": "0d73ceb9-d243-45ae-a1e1-e05abaa454d8", "type": "Structure", "resolved": true, "remote_id": "1", "parent_idx": 1, "acord_location_information": {"building_number": 1, "location_number": 1}}, {"id": "a87b46b6-63d0-478e-b501-6826a3a47fc2", "type": "Submission", "resolved": true}], "additional_data": {"fe_properties": null, "finished_do_sub_step": null}, "entity_information": [{"name": "Name", "values": [{"value": "Smokehaus Ferndale LLC", "entity_idx": 0}, {"value": "Smokehaus Ferndale LLC", "entity_idx": 1}, {"value": "Smokehaus Ferndale LLC", "entity_idx": 2}], "value_type": "TEXT", "fact_subtype_suggestions": []}, {"name": "Address", "values": [{"value": "177 Vester Ferndale MI 48220", "entity_idx": 0}, {"value": "177 Vester Ferndale Wayne MI 48220", "entity_idx": 1}, {"value": "177 Vester Ferndale Wayne MI 48220", "entity_idx": 2}], "value_type": "TEXT", "fact_subtype_suggestions": []}, {"name": "Policy Effective Start Date", "values": [{"value": "2025-05-16 00:00:00+00:00", "entity_idx": 3}], "value_type": "TEXT", "fact_subtype_suggestions": []}, {"name": "Policy End Date", "values": [{"value": "2026-05-16 00:00:00+00:00", "entity_idx": 3}], "value_type": "TEXT", "fact_subtype_suggestions": []}, {"name": "Agency", "values": [{"value": "Core Insurance Group LLC 50787 Corporate Dr Shelby Township MI 48315", "entity_idx": 3}], "value_type": "TEXT", "fact_subtype_suggestions": []}, {"name": "Carrier", "values": [{"value": "General Submission", "entity_idx": 3}], "value_type": "TEXT", "fact_subtype_suggestions": []}, {"name": "Agency Phone", "values": [{"value": "(*************", "entity_idx": 3}], "value_type": "TEXT", "fact_subtype_suggestions": []}, {"name": "Agency Contact Name", "values": [{"value": "<PERSON>", "entity_idx": 3}], "value_type": "TEXT", "fact_subtype_suggestions": []}, {"name": "Agency Email Address", "values": [{"value": "<EMAIL>", "entity_idx": 3}], "value_type": "TEXT", "fact_subtype_suggestions": []}, {"name": "Agency Customer ID", "values": [{"value": "00013404", "entity_idx": 3}], "value_type": "TEXT", "fact_subtype_suggestions": []}, {"name": "Carrier Policy Number", "values": [{"value": "25/26 NB SUBMISSION", "entity_idx": 3}], "value_type": "TEXT", "fact_subtype_suggestions": []}, {"name": "Carrier Status Of Transaction", "values": [{"value": "quote", "entity_idx": 3}, {"value": "quote", "entity_idx": 3}], "value_type": "TEXT", "fact_subtype_suggestions": []}, {"name": "ACORD Date Received", "values": [{"value": "2025-05-16 00:00:00+00:00", "entity_idx": 3}], "value_type": "TEXT", "fact_subtype_suggestions": []}, {"name": "Coverages details", "values": [{"value": "[{\"coverage_name\": \"Liability\", \"coverage_type\": \"PRIMARY\", \"self_insurance_retention\": null, \"limit\": null, \"each_occurrence\": null, \"excess_limit\": null, \"excess_attachment_point\": null, \"period\": null, \"other_terms\": null, \"retention\": null, \"expiring_premium\": null, \"target_premium\": null, \"gl_limits\": [], \"gl_deductibles\": [], \"limit_applies\": []}, {\"coverage_name\": \"Property\", \"coverage_type\": \"PRIMARY\", \"self_insurance_retention\": null, \"limit\": null, \"each_occurrence\": null, \"excess_limit\": null, \"excess_attachment_point\": null, \"period\": null, \"other_terms\": null, \"retention\": null, \"expiring_premium\": null, \"target_premium\": null, \"gl_limits\": [], \"gl_deductibles\": [], \"limit_applies\": []}]", "entity_idx": 3}], "value_type": "TEXT", "fact_subtype_suggestions": []}]}