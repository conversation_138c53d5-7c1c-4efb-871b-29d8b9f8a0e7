from datetime import datetime
from unittest.mock import Mock, patch
from uuid import uuid4

import pytest

from copilot.services.orgs.bowhead_service import (
    BowheadRelationResult,
    BowheadRelationType,
    BowheadService,
)


class TestBowheadService:
    def create_mock_submission(
        self,
        submission_id=None,
        broker_id=None,
        proposed_effective_date=None,
        is_deleted=False,
        report_is_deleted=False,
    ):
        """Helper to create mock submission objects"""
        if submission_id is None:
            submission_id = uuid4()
        if broker_id is None:
            broker_id = uuid4()

        submission = Mock()
        submission.id = submission_id
        submission.broker_id = broker_id
        submission.proposed_effective_date = proposed_effective_date
        submission.is_deleted = is_deleted

        # Mock the report relationship
        mock_report = Mock()
        mock_report.id = uuid4()
        mock_report.is_deleted = report_is_deleted
        submission.report = mock_report

        return submission

    def create_mock_submission_premises(self, submission_id):
        """Helper to create mock submission premises objects"""
        premises = Mock()
        premises.submission_id = submission_id
        return premises

    @pytest.fixture
    def current_submission(self):
        """Fixture for the current submission being analyzed"""
        return self.create_mock_submission(
            submission_id=uuid4(), broker_id=uuid4(), proposed_effective_date=datetime(2024, 1, 15)
        )

    @pytest.fixture
    def bound_log(self):
        """Fixture for bound logger"""
        return Mock()

    def test_analyze_relations_no_matched_premises_returns_new_account(self, current_submission, bound_log):
        """Test that empty matched_submission_premises returns NO_CONFLICT_AND_NEW_ACCOUNT"""
        matched_premises = []

        result = BowheadService.analyze_relations(current_submission, matched_premises, bound_log)

        assert len(result) == 1
        assert result[0].relation_type == BowheadRelationType.NO_CONFLICT_AND_NEW_ACCOUNT
        assert result[0].current_submission == current_submission
        assert result[0].matched_submission is None
        assert result[0].matched_submission_premises is None

    @patch("copilot.services.orgs.bowhead_service.SubmissionDAO.get_minimal_submission")
    def test_analyze_relations_renewal_detection(self, mock_get_submission, current_submission, bound_log):
        """Test detection of renewal relationships"""
        # Setup: matched submission with same broker, effective date 1 year ago
        matched_sub_id = uuid4()
        matched_submission = self.create_mock_submission(
            submission_id=matched_sub_id,
            broker_id=current_submission.broker_id,  # Same broker
            proposed_effective_date=datetime(2023, 1, 10),  # ~1 year ago, within 90 days
        )
        mock_get_submission.return_value = matched_submission

        matched_premises = [self.create_mock_submission_premises(matched_sub_id)]

        result = BowheadService.analyze_relations(current_submission, matched_premises, bound_log)

        assert len(result) == 1
        assert result[0].relation_type == BowheadRelationType.RENEWAL
        assert result[0].matched_submission == matched_submission

    @patch("copilot.services.orgs.bowhead_service.SubmissionDAO.get_minimal_submission")
    def test_analyze_relations_conflict_same_broker(self, mock_get_submission, current_submission, bound_log):
        """Test detection of conflict with same broker"""
        matched_sub_id = uuid4()
        matched_submission = self.create_mock_submission(
            submission_id=matched_sub_id,
            broker_id=current_submission.broker_id,  # Same broker
            proposed_effective_date=datetime(2024, 1, 20),  # Within 90 days, not renewal
        )
        mock_get_submission.return_value = matched_submission

        matched_premises = [self.create_mock_submission_premises(matched_sub_id)]

        result = BowheadService.analyze_relations(current_submission, matched_premises, bound_log)

        assert len(result) == 1
        assert result[0].relation_type == BowheadRelationType.CONFLICT_FOR_SAME_BROKER
        assert result[0].matched_submission == matched_submission

    @patch("copilot.services.orgs.bowhead_service.SubmissionDAO.get_minimal_submission")
    def test_analyze_relations_conflict_different_broker(self, mock_get_submission, current_submission, bound_log):
        """Test detection of conflict with different broker"""
        matched_sub_id = uuid4()
        matched_submission = self.create_mock_submission(
            submission_id=matched_sub_id,
            broker_id=uuid4(),  # Different broker
            proposed_effective_date=datetime(2024, 1, 20),  # Within 90 days
        )
        mock_get_submission.return_value = matched_submission

        matched_premises = [self.create_mock_submission_premises(matched_sub_id)]

        result = BowheadService.analyze_relations(current_submission, matched_premises, bound_log)

        assert len(result) == 1
        assert result[0].relation_type == BowheadRelationType.CONFLICT_FOR_DIFFERENT_BROKER
        assert result[0].matched_submission == matched_submission

    @patch("copilot.services.orgs.bowhead_service.SubmissionDAO.get_minimal_submission")
    def test_analyze_relations_no_conflict_existing_account(self, mock_get_submission, current_submission, bound_log):
        """Test detection of existing account without conflict"""
        matched_sub_id = uuid4()
        matched_submission = self.create_mock_submission(
            submission_id=matched_sub_id,
            broker_id=uuid4(),  # Different broker
            proposed_effective_date=datetime(2023, 6, 15),  # Outside 90 days, not renewal
        )
        mock_get_submission.return_value = matched_submission

        matched_premises = [self.create_mock_submission_premises(matched_sub_id)]

        result = BowheadService.analyze_relations(current_submission, matched_premises, bound_log)

        assert len(result) == 1
        assert result[0].relation_type == BowheadRelationType.NO_CONFLICT_BUT_EXISTING_ACCOUNT
        assert result[0].matched_submission == matched_submission

    @patch("copilot.services.orgs.bowhead_service.SubmissionDAO.get_minimal_submission")
    def test_analyze_relations_skips_deleted_submission(self, mock_get_submission, current_submission, bound_log):
        """Test that deleted submissions are skipped"""
        matched_sub_id = uuid4()
        deleted_submission = self.create_mock_submission(submission_id=matched_sub_id, is_deleted=True)
        mock_get_submission.return_value = deleted_submission

        matched_premises = [self.create_mock_submission_premises(matched_sub_id)]

        result = BowheadService.analyze_relations(current_submission, matched_premises, bound_log)

        assert len(result) == 0

    @patch("copilot.services.orgs.bowhead_service.SubmissionDAO.get_minimal_submission")
    def test_analyze_relations_skips_deleted_report(self, mock_get_submission, current_submission, bound_log):
        """Test that submissions with deleted reports are skipped"""
        matched_sub_id = uuid4()
        submission_with_deleted_report = self.create_mock_submission(
            submission_id=matched_sub_id, report_is_deleted=True
        )
        mock_get_submission.return_value = submission_with_deleted_report

        matched_premises = [self.create_mock_submission_premises(matched_sub_id)]

        result = BowheadService.analyze_relations(current_submission, matched_premises, bound_log)

        assert len(result) == 0

    @patch("copilot.services.orgs.bowhead_service.SubmissionDAO.get_minimal_submission")
    def test_analyze_relations_deduplicates_submissions(self, mock_get_submission, current_submission, bound_log):
        """Test that duplicate submission IDs are handled correctly"""
        matched_sub_id = uuid4()
        matched_submission = self.create_mock_submission(
            submission_id=matched_sub_id, broker_id=uuid4(), proposed_effective_date=datetime(2024, 1, 20)
        )
        mock_get_submission.return_value = matched_submission

        # Create multiple premises pointing to the same submission
        matched_premises = [
            self.create_mock_submission_premises(matched_sub_id),
            self.create_mock_submission_premises(matched_sub_id),
            self.create_mock_submission_premises(matched_sub_id),
        ]

        result = BowheadService.analyze_relations(current_submission, matched_premises, bound_log)

        # Should only process the submission once
        assert len(result) == 1
        assert mock_get_submission.call_count == 1

    def test_within_days_default_window(self):
        """Test _within_days method with default window"""
        date1 = datetime(2024, 1, 15)
        date2 = datetime(2024, 1, 20)  # 5 days difference

        result = BowheadService._within_days(date1, date2)
        assert result is True

        date3 = datetime(2024, 5, 15)  # 120 days difference
        result = BowheadService._within_days(date1, date3)
        assert result is False

    def test_within_days_custom_window(self):
        """Test _within_days method with custom window"""
        date1 = datetime(2024, 1, 15)
        date2 = datetime(2024, 1, 25)  # 10 days difference

        result = BowheadService._within_days(date1, date2, days=5)
        assert result is False

        result = BowheadService._within_days(date1, date2, days=15)
        assert result is True

    def test_is_renewal_of_same_broker_anniversary_date(self):
        """Test _is_renewal_of method with same broker and anniversary date"""
        current = self.create_mock_submission(broker_id=uuid4(), proposed_effective_date=datetime(2024, 1, 15))

        other = self.create_mock_submission(
            broker_id=current.broker_id,  # Same broker
            proposed_effective_date=datetime(2023, 1, 10),  # ~1 year ago, within 90 days
        )

        result = BowheadService._is_renewal_of(current, other)
        assert result is True

    def test_is_renewal_of_different_broker(self):
        """Test _is_renewal_of method returns False for different brokers"""
        current = self.create_mock_submission(broker_id=uuid4(), proposed_effective_date=datetime(2024, 1, 15))

        other = self.create_mock_submission(
            broker_id=uuid4(), proposed_effective_date=datetime(2023, 1, 10)  # Different broker
        )

        result = BowheadService._is_renewal_of(current, other)
        assert result is False

    def test_is_renewal_of_missing_dates(self):
        """Test _is_renewal_of method returns False when dates are missing"""
        current = self.create_mock_submission(broker_id=uuid4(), proposed_effective_date=None)  # Missing date

        other = self.create_mock_submission(broker_id=current.broker_id, proposed_effective_date=datetime(2023, 1, 10))

        result = BowheadService._is_renewal_of(current, other)
        assert result is False

    def test_is_in_conflict_same_broker_within_window(self):
        """Test _is_in_conflict method for same broker within time window"""
        current = self.create_mock_submission(broker_id=uuid4(), proposed_effective_date=datetime(2024, 1, 15))

        other = self.create_mock_submission(
            broker_id=current.broker_id, proposed_effective_date=datetime(2024, 1, 20)  # Within 90 days
        )

        result = BowheadService._is_in_conflict(current, other, same_broker=True)
        assert result is True

    def test_is_in_conflict_different_broker_within_window(self):
        """Test _is_in_conflict method for different broker within time window"""
        current = self.create_mock_submission(broker_id=uuid4(), proposed_effective_date=datetime(2024, 1, 15))

        other = self.create_mock_submission(
            broker_id=uuid4(), proposed_effective_date=datetime(2024, 1, 20)  # Different broker  # Within 90 days
        )

        result = BowheadService._is_in_conflict(current, other, same_broker=False)
        assert result is True

    def test_is_in_conflict_wrong_broker_condition(self):
        """Test _is_in_conflict method returns False when broker condition doesn't match"""
        current = self.create_mock_submission(broker_id=uuid4(), proposed_effective_date=datetime(2024, 1, 15))

        other = self.create_mock_submission(
            broker_id=current.broker_id, proposed_effective_date=datetime(2024, 1, 20)  # Same broker
        )

        # Asking for different broker but they're the same
        result = BowheadService._is_in_conflict(current, other, same_broker=False)
        assert result is False

    def test_is_in_conflict_missing_dates(self):
        """Test _is_in_conflict method returns False when dates are missing"""
        current = self.create_mock_submission(broker_id=uuid4(), proposed_effective_date=None)  # Missing date

        other = self.create_mock_submission(broker_id=current.broker_id, proposed_effective_date=datetime(2024, 1, 20))

        result = BowheadService._is_in_conflict(current, other, same_broker=True)
        assert result is False

    def test_create_result(self):
        """Test _create_result method creates correct BowheadRelationResult"""
        current_sub = self.create_mock_submission()
        matched_sub = self.create_mock_submission()
        matched_premises = self.create_mock_submission_premises(uuid4())
        relation_type = BowheadRelationType.RENEWAL

        result = BowheadService._create_result(current_sub, matched_sub, matched_premises, relation_type)

        assert isinstance(result, BowheadRelationResult)
        assert result.current_submission == current_sub
        assert result.matched_submission == matched_sub
        assert result.matched_submission_premises == matched_premises
        assert result.relation_type == relation_type

    @patch("copilot.services.orgs.bowhead_service.SubmissionDAO.get_minimal_submission")
    def test_analyze_relations_multiple_submissions(self, mock_get_submission, current_submission, bound_log):
        """Test analyze_relations with multiple different submissions"""
        # Setup multiple different matched submissions
        renewal_sub_id = uuid4()
        conflict_same_broker_sub_id = uuid4()
        conflict_diff_broker_sub_id = uuid4()

        renewal_submission = self.create_mock_submission(
            submission_id=renewal_sub_id,
            broker_id=current_submission.broker_id,
            proposed_effective_date=datetime(2023, 1, 10),  # Renewal
        )

        conflict_same_submission = self.create_mock_submission(
            submission_id=conflict_same_broker_sub_id,
            broker_id=current_submission.broker_id,
            proposed_effective_date=datetime(2024, 1, 20),  # Same broker conflict
        )

        conflict_diff_submission = self.create_mock_submission(
            submission_id=conflict_diff_broker_sub_id,
            broker_id=uuid4(),
            proposed_effective_date=datetime(2024, 1, 25),  # Different broker conflict
        )

        # Setup mock to return different submissions based on ID
        def mock_get_sub_side_effect(sub_id, **kwargs):
            if sub_id == str(renewal_sub_id):
                return renewal_submission
            elif sub_id == str(conflict_same_broker_sub_id):
                return conflict_same_submission
            elif sub_id == str(conflict_diff_broker_sub_id):
                return conflict_diff_submission

        mock_get_submission.side_effect = mock_get_sub_side_effect

        matched_premises = [
            self.create_mock_submission_premises(renewal_sub_id),
            self.create_mock_submission_premises(conflict_same_broker_sub_id),
            self.create_mock_submission_premises(conflict_diff_broker_sub_id),
        ]

        results = BowheadService.analyze_relations(current_submission, matched_premises, bound_log)

        assert len(results) == 3

        # Find each result type
        renewal_result = next(r for r in results if r.relation_type == BowheadRelationType.RENEWAL)
        same_broker_result = next(r for r in results if r.relation_type == BowheadRelationType.CONFLICT_FOR_SAME_BROKER)
        diff_broker_result = next(
            r for r in results if r.relation_type == BowheadRelationType.CONFLICT_FOR_DIFFERENT_BROKER
        )

        assert renewal_result.matched_submission == renewal_submission
        assert same_broker_result.matched_submission == conflict_same_submission
        assert diff_broker_result.matched_submission == conflict_diff_submission
