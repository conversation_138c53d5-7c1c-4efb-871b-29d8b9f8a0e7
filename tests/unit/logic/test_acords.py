from typing import List
from unittest.mock import MagicMock
from uuid import uuid4
import json

from entity_resolution_service_client_v3 import EntityNameRequest, NameScoreResponse
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.entity import EntityFieldID
from static_common.enums.external import ExternalIdentifierType
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.sensible import SensibleStatus
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.models.business_resolution_data import ExternalIdentifier
from static_common.models.file_onboarding import (
    Acord126TransientData,
    Acord140TransientData,
    OnboardedFile,
    PremisesInformationTableWithEntity,
    ResolvedDataField,
    ResolvedDataValue,
    SubmissionEntity,
)
from static_common.schemas.file_onboarding import (
    LeanOnboardedFileSchema,
    OnboardedFileSchema,
)
import pytest

from copilot.logic.acords import (
    pick_name,
    update_acord_126,
    update_acord_131,
    update_acord_140,
    update_acord_160,
    update_acord_823,
    update_acord_processed_data,
    update_applied_130,
    update_external_identifiers,
)
from copilot.models import File
from copilot.models.files import (
    AcordEntityManager,
    OnboardedFileEntityInformation,
    ProcessedFile,
)
from copilot.models.requested_properties import RequestedObject
from tests.integration.factories import file_fixture, processed_file_fixture

onboarded_file_schema = LeanOnboardedFileSchema()


@pytest.fixture
def onboarded_entity_information() -> OnboardedFileEntityInformation:
    return OnboardedFileEntityInformation(
        "req_name req_address",
        SubmissionEntityType.BUSINESS,
        entity=SubmissionEntity(
            entity_named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED, id="req_name req_address"
        ),
        name="req_name",
        address="req_address",
        name_field=ResolvedDataField(
            name="Name", value_type=FieldType.TEXT, values=[ResolvedDataValue(value="req_name", entity_idx=0)]
        ),
        address_field=ResolvedDataField(
            name="Address", value_type=FieldType.TEXT, values=[ResolvedDataValue(value="req_address", entity_idx=0)]
        ),
        identifiers=[ExternalIdentifier(type=ExternalIdentifierType.FEIN, value="123456789")],
    )


@pytest.fixture
def acord_entity_manager(onboarded_entity_information):
    em = MagicMock(spec=AcordEntityManager)
    em.get_first_named_insured = MagicMock(return_value=onboarded_entity_information)
    em.find_entity_by_location = MagicMock(return_value=onboarded_entity_information)
    return em


@pytest.fixture
def acord_126() -> File:
    with open(f"tests/data/data_consolidation/acord_126_processed_data.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(processed_file=ProcessedFile(processed_data=processed_data))


@pytest.fixture
def acord_823() -> File:
    with open(f"tests/data/data_consolidation/acord_823_processed_data.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(processed_file=ProcessedFile(processed_data=processed_data))


@pytest.fixture
def acord_131() -> File:
    with open(f"tests/data/data_consolidation/acord_131_processed_data.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(processed_file=ProcessedFile(processed_data=processed_data))


@pytest.fixture
def acord_823_without_name() -> File:
    with open(f"tests/data/data_consolidation/acord_823_processed_data_without_name.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(processed_file=ProcessedFile(processed_data=processed_data))


@pytest.fixture
def acord_125_real_data() -> File:
    with open(f"tests/data/data_consolidation/acord_125_real_data.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(
        processed_file=ProcessedFile(processed_data=processed_data), classification=ClassificationDocumentType.ACORD_125
    )


@pytest.fixture
def acord_125_no_fni_name() -> File:
    with open(f"tests/data/data_consolidation/acord_125_no_fni_name.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(
        processed_file=ProcessedFile(processed_data=processed_data), classification=ClassificationDocumentType.ACORD_125
    )


@pytest.fixture
def acord_125_one_structure() -> File:
    with open(f"tests/data/data_consolidation/acord_125_one_structure.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(
        processed_file=ProcessedFile(processed_data=processed_data), classification=ClassificationDocumentType.ACORD_125
    )


@pytest.fixture
def acord_823_real_data() -> File:
    with open(f"tests/data/data_consolidation/acord_823_real_data.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(
        processed_file=ProcessedFile(processed_data=processed_data), classification=ClassificationDocumentType.ACORD_823
    )


@pytest.fixture
def acord_140_real_data() -> File:
    with open(f"tests/data/data_consolidation/acord_140_real_data.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(
        processed_file=ProcessedFile(processed_data=processed_data),
        sensible_status=SensibleStatus.COMPLETE,
        classification=ClassificationDocumentType.ACORD_140,
    )


@pytest.fixture
def acord_140_with_transient_data() -> File:
    with open(f"tests/data/data_consolidation/acord_140_transient_data.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(
        processed_file=ProcessedFile(processed_data=processed_data),
        sensible_status=SensibleStatus.COMPLETE,
        classification=ClassificationDocumentType.ACORD_140,
    )


@pytest.fixture
def acord_823_transient() -> File:
    with open(f"tests/data/data_consolidation/acord_823_transient.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(
        processed_file=ProcessedFile(processed_data=processed_data),
        sensible_status=SensibleStatus.COMPLETE,
        classification=ClassificationDocumentType.ACORD_823,
    )


@pytest.fixture
def acord_140_one_structure() -> File:
    with open(f"tests/data/data_consolidation/acord_140_one_structure.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(
        processed_file=ProcessedFile(processed_data=processed_data),
        sensible_status=SensibleStatus.COMPLETE,
        classification=ClassificationDocumentType.ACORD_140,
    )


@pytest.fixture
def acord_130_real_data() -> File:
    with open(f"tests/data/data_consolidation/acord_130_real_data.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(
        processed_file=ProcessedFile(processed_data=processed_data),
        sensible_status=SensibleStatus.COMPLETE,
        classification=ClassificationDocumentType.ACORD_130,
    )


@pytest.fixture
def applied_130_real_data() -> File:
    with open(f"tests/data/data_consolidation/applied_130_real_data.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(
        processed_file=ProcessedFile(processed_data=processed_data),
        sensible_status=SensibleStatus.COMPLETE,
        classification=ClassificationDocumentType.APPLIED_130,
    )


def test_update_acord_126(mocker, acord_126, onboarded_entity_information, acord_entity_manager):
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    mocker.patch("copilot.models._private.db.session.commit", return_value=None)
    name = onboarded_entity_information.name
    address = onboarded_entity_information.address
    update_acord_126(acord_126, acord_entity_manager)
    onboarded_file: OnboardedFile = onboarded_file_schema.load(acord_126.processed_file.processed_data)
    # noinspection PyTypeChecker
    transient_data: Acord126TransientData = onboarded_file.transient_data
    for hazard in transient_data.schedule_of_hazards:
        assert hazard.requested_name == name
        assert hazard.requested_address == address
    assert len(onboarded_file.entities) == 2
    assert onboarded_file.entities[1].id == f"{name} {address}"
    assert onboarded_file.entities[0].parent_idx == 1
    field = next(field for field in onboarded_file.fields if field.fact_subtype_id == "HAZARDOUS_MATERIALS")
    assert field.values[0].entity_idx == 1
    fein = next((field for field in onboarded_file.fields if field.fact_subtype_id == FactSubtypeID.FEIN), None)
    assert fein
    assert len(fein.values) == 1
    assert fein.values[0].entity_idx == 1


def test_update_acord_126_no_fni_name(mocker, acord_125_no_fni_name, acord_126):
    em = AcordEntityManager([acord_125_no_fni_name])
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    mocker.patch("copilot.models._private.db.session.commit", return_value=None)
    update_acord_126(acord_126, em)
    onboarded_file: OnboardedFile = onboarded_file_schema.load(acord_126.processed_file.processed_data)
    assert len(onboarded_file.entities) == 2
    field = next(field for field in onboarded_file.fields if field.fact_subtype_id == "HAZARDOUS_MATERIALS")
    assert field.values[0].entity_idx == 1


def test_update_acord_131_new_entity(mocker, acord_131, onboarded_entity_information, acord_entity_manager):
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    mocker.patch("copilot.models._private.db.session.commit", return_value=None)
    name = onboarded_entity_information.name
    address = onboarded_entity_information.address
    update_acord_131(acord_131, acord_entity_manager)
    onboarded_file: OnboardedFile = onboarded_file_schema.load(acord_131.processed_file.processed_data)
    assert len(onboarded_file.entities) == 7
    assert onboarded_file.entities[6].id == f"{name} {address}"
    assert len(onboarded_file.entity_information[0].values) == 7
    assert len(onboarded_file.entity_information[1].values) == 7
    assert len([entity for entity in onboarded_file.entities if entity.id.startswith("req_name")]) == 5
    assert onboarded_file.fields[9].values[0].entity_idx == 6
    assert len(onboarded_file.fields) == 23
    fein = next((field for field in onboarded_file.fields if field.fact_subtype_id == FactSubtypeID.FEIN), None)
    assert fein
    assert len(fein.values) == 1
    assert fein.values[0].entity_idx == 6


def test_update_acord_131_existing_entity(mocker, acord_131, onboarded_entity_information, acord_entity_manager):
    acord_entity_manager.extract_entities_from_file = MagicMock(return_value=[onboarded_entity_information])
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    mocker.patch("copilot.models._private.db.session.commit", return_value=None)
    onboarded_file: OnboardedFile = onboarded_file_schema.load(acord_131.processed_file.processed_data)
    onboarded_file.entities.append(onboarded_entity_information.entity)
    onboarded_file.entity_information[0].values.append(
        ResolvedDataValue(onboarded_entity_information.name_field.values[0].value, entity_idx=6)
    )
    onboarded_file.entity_information[1].values.append(
        ResolvedDataValue(onboarded_entity_information.address_field.values[0].value, entity_idx=6)
    )
    acord_131.processed_file.processed_data = onboarded_file_schema.dump(onboarded_file)
    update_acord_131(acord_131, acord_entity_manager)
    onboarded_file: OnboardedFile = onboarded_file_schema.load(acord_131.processed_file.processed_data)
    assert len(onboarded_file.entities) == 7
    assert len(onboarded_file.entity_information[0].values) == 7
    assert len(onboarded_file.entity_information[1].values) == 7


def test_update_acord_823(mocker, acord_823, onboarded_entity_information, acord_entity_manager):
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    mocker.patch("copilot.models._private.db.session.commit", return_value=None)
    name: str = onboarded_entity_information.name
    update_acord_823(acord_823, acord_entity_manager)
    onboarded_file: OnboardedFile = onboarded_file_schema.load(acord_823.processed_file.processed_data)

    for entity in [e for e in onboarded_file.entities if e.type == SubmissionEntityType.BUSINESS.value]:
        assert name.lower() in entity.id

    for val in next(ei for ei in onboarded_file.entity_information if ei.name == EntityFieldID.NAME.value).values:
        assert val.value == name

    assert len(onboarded_file.entity_information[0].values) == len(onboarded_file.entities)
    assert len(onboarded_file.entity_information[1].values) == len(onboarded_file.entities)


def test_update_acord_823_without_name(
    mocker, acord_823_without_name, onboarded_entity_information, acord_entity_manager
):
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    mocker.patch("copilot.models._private.db.session.commit", return_value=None)
    name: str = onboarded_entity_information.name
    update_acord_823(acord_823_without_name, acord_entity_manager)
    onboarded_file: OnboardedFile = onboarded_file_schema.load(acord_823_without_name.processed_file.processed_data)

    for entity in [e for e in onboarded_file.entities if e.type == SubmissionEntityType.BUSINESS.value]:
        assert name.lower() in entity.id

    for val in next(ei for ei in onboarded_file.entity_information if ei.name == EntityFieldID.NAME.value).values:
        assert val.value == name

    assert len(onboarded_file.entity_information[0].values) == len(onboarded_file.entities)
    assert len(onboarded_file.entity_information[1].values) == len(onboarded_file.entities)


def test_update_acord_140(mocker, acord_125_real_data, acord_823_real_data, acord_140_real_data):
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    mocker.patch("copilot.models._private.db.session.commit", return_value=None)

    acm = AcordEntityManager([acord_125_real_data, acord_823_real_data])
    onboarded_file: OnboardedFile = onboarded_file_schema.load(acord_140_real_data.processed_file.processed_data)

    for entity in [e for e in onboarded_file.entities if e.type == SubmissionEntityType.STRUCTURE]:
        assert not acm.find_entity_by_id(entity.id)

    update_acord_140(acord_140_real_data, acm)

    onboarded_file: OnboardedFile = onboarded_file_schema.load(acord_140_real_data.processed_file.processed_data)

    for entity in onboarded_file.entities:
        assert acm.find_entity_by_id(entity.id)

    assert acord_140_real_data.sensible_status == SensibleStatus.COMPLETE


def test_update_acord_140_one_structure(mocker, acord_125_one_structure, acord_140_one_structure):
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    mocker.patch("copilot.models._private.db.session.commit", return_value=None)

    acm = AcordEntityManager([acord_125_one_structure])
    onboarded_file: OnboardedFile = onboarded_file_schema.load(acord_140_one_structure.processed_file.processed_data)

    for entity in [e for e in onboarded_file.entities if e.type == SubmissionEntityType.STRUCTURE]:
        assert not acm.find_entity_by_id(entity.id)

    update_acord_140(acord_140_one_structure, acm)

    onboarded_file: OnboardedFile = onboarded_file_schema.load(acord_140_one_structure.processed_file.processed_data)

    for entity in onboarded_file.entities:
        assert acm.find_entity_by_id(entity.id)

    assert acord_140_one_structure.sensible_status == SensibleStatus.COMPLETE


def test_acord_consolidation(mocker, acord_125_real_data, acord_823_real_data, acord_140_real_data):
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    mocker.patch("copilot.models._private.db.session.commit", return_value=None)
    acords = sorted(
        [acord_140_real_data, acord_823_real_data, acord_125_real_data],
        key=lambda x: ClassificationDocumentType.get_consolidation_priority(x.classification),
    )
    acm = AcordEntityManager(acords)

    for acord in acords:
        update_acord_processed_data(acord, acm)

    onboarded_file: OnboardedFile = onboarded_file_schema.load(acord_140_real_data.processed_file.processed_data)

    for entity in [e for e in onboarded_file.entities if e.type == SubmissionEntityType.STRUCTURE]:
        assert acm.find_entity_by_id(entity.id)


def test_update_acord_140_transient_data(mocker, acord_823_transient, acord_140_with_transient_data):
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    mocker.patch("copilot.models._private.db.session.commit", return_value=None)

    acm = AcordEntityManager([acord_823_transient])
    onboarded_file: OnboardedFile = onboarded_file_schema.load(
        acord_140_with_transient_data.processed_file.processed_data
    )

    for entity in [e for e in onboarded_file.entities if e.type == SubmissionEntityType.STRUCTURE]:
        assert not acm.find_entity_by_id(entity.id)

    update_acord_140(acord_140_with_transient_data, acm)

    onboarded_file = onboarded_file_schema.load(acord_140_with_transient_data.processed_file.processed_data)
    # noinspection PyTypeChecker
    transient_data: Acord140TransientData = onboarded_file.transient_data
    premises_info: list[PremisesInformationTableWithEntity] = transient_data.premises_info

    for pi in premises_info:
        assert pi.requested_name


@pytest.fixture
def load_acord_data():
    def _factory(file_path: str, acord_classification: ClassificationDocumentType) -> File:
        with open(file_path) as f:
            file = f.read()
            processed_data = json.loads(file)

        return File(
            id=uuid4(),
            processed_file=ProcessedFile(processed_data=processed_data),
            sensible_status=SensibleStatus.COMPLETE,
            classification=acord_classification,
        )

    return _factory


def test_update_acord_140_transient_data_2(mocker, load_acord_data):
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    mocker.patch("copilot.models._private.db.session.commit", return_value=None)

    acord_125_pd = load_acord_data(
        "tests/data/data_consolidation/acord_125_real_pd.json", ClassificationDocumentType.ACORD_125
    )
    acord_823_pd = load_acord_data(
        "tests/data/data_consolidation/acord_823_real_pd.json", ClassificationDocumentType.ACORD_823
    )

    acord_140_with_real_transient_data = load_acord_data(
        "tests/data/data_consolidation/acord_140_with_real_transient_data.json", ClassificationDocumentType.ACORD_140
    )

    acm = AcordEntityManager([acord_125_pd, acord_140_with_real_transient_data, acord_823_pd])
    onboarded_file: OnboardedFile = onboarded_file_schema.load(
        acord_140_with_real_transient_data.processed_file.processed_data
    )

    for premise_info in onboarded_file.transient_data.premises_info:
        assert premise_info.requested_name is None
        assert premise_info.requested_address is None

    update_acord_140(acord_140_with_real_transient_data, acm)

    onboarded_file: OnboardedFile = onboarded_file_schema.load(
        acord_140_with_real_transient_data.processed_file.processed_data
    )

    premises_info = [
        {
            "building_number": 1,
            "location_number": 1,
            "requested_name": "Polish American Association",
            "requested_address": "3834 N Cicero Ave Chicago IL 60641",
        },
        {
            "building_number": 2,
            "location_number": 1,
            "requested_name": "Polish American Association",
            "requested_address": "3834 N Cicero Ave Chicago IL 60641",
        },
        {
            "building_number": 1,
            "location_number": 2,
            "requested_name": "Polish American Association",
            "requested_address": "3815 N Cicero Ave",
        },
        {
            "building_number": 1,
            "location_number": 3,
            "requested_name": "Polish American Association",
            "requested_address": "6276 W Archer Ave",
        },
        {
            "building_number": 1,
            "location_number": 4,
            "requested_name": "Polish American Association",
            "requested_address": "2008 W Chicago Ave",
        },
        {
            "building_number": 1,
            "location_number": 5,
            "requested_name": "Polish American Association",
            "requested_address": "4849 N Milwaukee",
        },
    ]


def test_update_acord_140_overwrites_address_from_acord_125(mocker, load_acord_data):
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    mocker.patch("copilot.models._private.db.session.commit", return_value=None)

    acord_125_pd = load_acord_data(
        "tests/data/data_consolidation/acord_125_with_full_address.json", ClassificationDocumentType.ACORD_125
    )

    acord_140_with_real_transient_data = load_acord_data(
        "tests/data/data_consolidation/acord_140_with_partial_address.json", ClassificationDocumentType.ACORD_140
    )

    acm = AcordEntityManager([acord_125_pd, acord_140_with_real_transient_data])

    update_acord_140(acord_140_with_real_transient_data, acm)

    onboarded_file: OnboardedFile = onboarded_file_schema.load(
        acord_140_with_real_transient_data.processed_file.processed_data
    )

    address_field = next(ei for ei in onboarded_file.entity_information if ei.name == EntityFieldID.ADDRESS.value)
    assert {v.value for v in address_field.values} == {"177 Vester Ferndale Wayne MI 48220"}

    assert onboarded_file.transient_data.premises_info[0].requested_address == "177 Vester Ferndale Wayne MI 48220"


def test_update_applied_130(mocker, acord_130_real_data, applied_130_real_data):
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    mocker.patch("copilot.models._private.db.session.commit", return_value=None)

    acm = AcordEntityManager([acord_130_real_data])
    onboarded_file: OnboardedFile = onboarded_file_schema.load(applied_130_real_data.processed_file.processed_data)

    update_applied_130(applied_130_real_data, acm)

    onboarded_file: OnboardedFile = onboarded_file_schema.load(applied_130_real_data.processed_file.processed_data)

    assert onboarded_file.entity_information[1].name == "Name"
    assert len(onboarded_file.entity_information[1].values) == 9
    for val in onboarded_file.entity_information[1].values:
        assert val.value == "SC Laboratories, Inc."


def test_pick_name():
    assert pick_name(set(), "smolladdr") == ("", "smolladdr")
    assert pick_name({"correctly_matched", "correctly_matchedd"}, "garbage_correctly_matched_smolladdr") == (
        "",
        "garbage_correctly_matched_smolladdr",
    )
    assert pick_name({"correctly_matched"}, "garbage_correctly_matched and_some_address") == (
        "correctly_matched",
        "and_some_address",
    )
    assert pick_name({"crectly_matched", "FSD", "corectly_matched"}, "correctly_matched and_some_address") == (
        "corectly_matched",
        "and_some_address",
    )
    assert pick_name({"dfgg", "FSD", "arew"}, "correctly_matched and_some_address") == (
        "",
        "correctly_matched and_some_address",
    )
    assert pick_name({"Random company"}, "Random Company Address Line 1") == ("Random company", "Address Line 1")


def test_update_external_identifiers_when_missing_info():
    """
    Just making sure that the code doesn't blow if there is missing information
    """
    onb_file = OnboardedFile()
    update_external_identifiers(None, onb_file, 0)
    entity = OnboardedFileEntityInformation(
        entity_id="some_id",
        file_id=uuid4(),
        entity_type=SubmissionEntityType.BUSINESS,
        entity=SubmissionEntity(),
    )
    update_external_identifiers(entity, onb_file, None)
    update_external_identifiers(entity, onb_file, 0)


def test_update_external_identifiers_when_conflicting_value(mocker):
    """
    When there is conflicting value, we want to log a warning and not update the external identifier
    """
    onb_file = OnboardedFile(
        entities=[
            SubmissionEntity(
                id="some_id",
                type=SubmissionEntityType.BUSINESS,
            )
        ],
        fields=[
            ResolvedDataField(
                name="Name",
                value_type=FieldType.TEXT,
                fact_subtype_id=FactSubtypeID.FEIN,
                values=[ResolvedDataValue(value="123456789", entity_idx=0)],
            )
        ],
    )
    entity = OnboardedFileEntityInformation(
        entity_id="some_id",
        file_id=uuid4(),
        entity_type=SubmissionEntityType.BUSINESS,
        entity=SubmissionEntity(),
        identifiers=[ExternalIdentifier(type=ExternalIdentifierType.FEIN, value="987654321")],
    )
    mocked_logger = mocker.patch("copilot.logic.acords.logger.warning", return_value=None)
    update_external_identifiers(entity, onb_file, 0)
    mocked_logger.assert_called_once()
    assert mocked_logger.call_args.args[0] == "[ACORD CONSOLIDATION] Conflicting external identifiers"
    assert onb_file.fields[0].values[0].value == "123456789"


def test_update_external_identifiers(mocker):
    onb_file = OnboardedFile(
        entities=[
            SubmissionEntity(
                id="some_id",
                type=SubmissionEntityType.BUSINESS,
            ),
            SubmissionEntity(
                id="some_other_id",
                type=SubmissionEntityType.BUSINESS,
            ),
        ],
        fields=[
            ResolvedDataField(
                name="Name",
                value_type=FieldType.TEXT,
                fact_subtype_id=FactSubtypeID.FEIN,
                values=[ResolvedDataValue(value="123456789", entity_idx=1)],
            )
        ],
    )
    entity = OnboardedFileEntityInformation(
        entity_id="some_id",
        file_id=uuid4(),
        entity_type=SubmissionEntityType.BUSINESS,
        entity=SubmissionEntity(),
        identifiers=[
            ExternalIdentifier(type=ExternalIdentifierType.FEIN, value="987654321"),
            ExternalIdentifier(type=ExternalIdentifierType.WEBSITE, value="www.example.com"),
        ],
    )
    update_external_identifiers(entity, onb_file, 0)
    assert len(onb_file.fields) == 2
    assert onb_file.fields[0].fact_subtype_id == FactSubtypeID.FEIN
    existing_value = next(val for val in onb_file.fields[0].values if val.entity_idx == 1)
    assert existing_value.value == "123456789"
    added_value = next(val for val in onb_file.fields[0].values if val.entity_idx == 0)
    assert added_value.value == "987654321"
    assert onb_file.fields[1].fact_subtype_id == FactSubtypeID.WEBSITE
    assert len(onb_file.fields[1].values) == 1
    assert onb_file.fields[1].values[0].value == "www.example.com"


@pytest.fixture
def acord_125_for_160_real_data() -> File:
    with open(f"tests/data/data_consolidation/acord_125_for_160_real_data.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(
        processed_file=ProcessedFile(processed_data=processed_data), classification=ClassificationDocumentType.ACORD_125
    )


@pytest.fixture
def acord_160_real_data() -> File:
    with open(f"tests/data/data_consolidation/acord_160_real_data.json") as f:
        file = f.read()
        processed_data = json.loads(file)

    return File(
        processed_file=ProcessedFile(processed_data=processed_data),
        sensible_status=SensibleStatus.COMPLETE,
        classification=ClassificationDocumentType.ACORD_160,
    )


def test_update_acord_160(mocker, acord_125_for_160_real_data, acord_160_real_data):
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    mocker.patch("copilot.models._private.db.session.commit", return_value=None)

    acm = AcordEntityManager([acord_125_for_160_real_data, acord_160_real_data])

    update_acord_160(acord_160_real_data, acm)

    onboarded_file: OnboardedFile = onboarded_file_schema.load(acord_160_real_data.processed_file.processed_data)

    address_field = next(
        field for field in onboarded_file.entity_information if field.name == EntityFieldID.ADDRESS.value
    )

    assert len(address_field.values) == 6
    assert address_field.values[0].value == "Box 868 Monroe, NC 28111"
    assert address_field.values[0].entity_idx == 6
    assert all(v.value == "4308 Pageland Hwy Monroe Union NC 28112" for v in address_field.values[1:])

    assert acord_160_real_data.sensible_status == SensibleStatus.COMPLETE
